/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import SelectTree from 'components/form/UISelectCondition';
import { connect } from 'react-redux';

import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import { createStructuredSelector } from 'reselect';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import ConditionError from '../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import { safeParse } from '../../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../../utils/constants';
import { getDataDefautEvenActionBased } from '../utils';
import { makeSelectMainCreateWorkflow } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';
import { DataSourceBlockContent } from '../styled';
import { FormConditionError } from '../../../../containers/Filters/AddFilter/FormCondition/styles';

const labelPerfEvent = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_EVENT,
  'Perform event',
);
const labelEvent = getTranslateMessage(TRANSLATE_KEY._TITL_EVENT, 'Events');

const labelSelectEvent = getTranslateMessage(
  TRANSLATE_KEY._TITL_PERFORM_EVENT,
  'Select event',
);

const SelectEvent = props => {
  const {
    item,
    options,
    property,
    groupIndex,
    itemIndex,
    version,
    // divDisabled,
  } = props;

  const changeProperty = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  useEffect(() => {
    if (props.mainConfigure.design === 'update') {
      const dataDefault = getDataDefautEvenActionBased(options);

      const data = {
        groupIndex,
        itemIndex,
        value:
          property && Object.keys(property).length
            ? { ...property }
            : { ...dataDefault },
        isInit: true,
      };
      props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
      props.callback('UPDATE_CONDITIONS', data);
    } else if (!property) {
      const dataDefault = getDataDefautEvenActionBased(options);
      const data = {
        groupIndex,
        itemIndex,
        value: { ...dataDefault },
        isInit: true,
      };
      props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
      props.callback('UPDATE_CONDITIONS', data);
    } else {
      const data = {
        groupIndex,
        itemIndex,
        value: { ...property },
        isInit: true,
      };
      props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
      props.callback('UPDATE_CONDITIONS', data);
    }
  }, [props.componentId]);

  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  return (
    <DataSourceBlockContent>
      <div className="title select-event">{labelPerfEvent}</div>
      <div className="content select-tree">
        <WrapperDisable disabled={props.disabledEvent}>
          <SelectTree
            onlyParent={props.onlyParent}
            use="tree"
            options={options}
            isParentOpen={props.isParentOpen}
            value={property}
            onChange={changeProperty}
            placeholder={labelSelectEvent}
            isViewMode={props.isViewMode}
          />
        </WrapperDisable>
        {statusItemCode === STATUS_ITEM_CODE.FORBIDDEN ? (
          <FormConditionError className="p-top-1">
            {`This event is not allowed to stream to Journeys. Please turn on this option.`}
          </FormConditionError>
        ) : (
          <ConditionError
            keyError={1}
            error={props.item.get('error')}
            objectLabel={labelEvent}
            statusItemCode={statusItemCode}
            item={item}
          />
        )}
      </div>
    </DataSourceBlockContent>
  );
};

const mapStateToProps = createStructuredSelector({
  mainConfigure: makeSelectMainCreateWorkflow(),
});

export default connect(mapStateToProps)(React.memo(SelectEvent));
