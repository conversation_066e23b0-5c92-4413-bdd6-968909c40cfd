/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable dot-notation */
/* eslint-disable no-lonely-if */
/* eslint-disable no-empty */
/* eslint-disable prettier/prettier */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-useless-computed-key */
/* eslint-disable no-unneeded-ternary */
import { OrderedMap } from 'immutable';
import {
  buildValueConditionFromUI,
  validateItemCondition,
} from '../../../containers/Filters/utils';

import {
  CustomerIcon,
  EventSourcesIcon,
  SegmentsIcon,
  VisitorIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import TRANSLATE_KEY from '../../../messages/constant';
import { generateKey, safeParse } from '../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../utils/constants';
import { MAP_SEGMENT_VALUES } from '../UIPerformEvent/constants';

import { flatMap, groupBy, isObject, nth, split } from 'lodash';
import { ITEM_TYPE_ID } from '../../Templates/TargetAudienceFilter/utils';
import {
  // toAPITargetAudience,
  toUITargetAudience,
} from '../UITargetAudience/utils';
import { toAPITargetAudience } from '../UITargetSelection/utils';
export const initDataByTypeFilter = {
  user_attributes: {},
  item_segment: {},
  event_attribute: undefined,
};

export const FILTER_TYPE = {
  CUSTOMER_ATTRIBUTE: 'customer_attribute',
  VISITOR_ATTRIBUTE: 'user_attribute',
  TARGET_AUDIENCE: 'item_segment',
  PERFORMED_EVENT: 'event_attribute',
};

/* eslint-disable no-else-return */
export const DATA_OPTIONS_FILTER = [
  {
    label: 'Customers',
    description: 'Using attributes of Customers',
    value: FILTER_TYPE.CUSTOMER_ATTRIBUTE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
    icon: <CustomerIcon color="#005EB8" />,
  },
  {
    label: 'Visitors',
    description: 'Using attributes of Visitors',
    value: FILTER_TYPE.VISITOR_ATTRIBUTE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
    icon: <VisitorIcon color="#005EB8" />,
  },
  {
    label: 'Target Audiences',
    description: 'Using predefined segments',
    value: FILTER_TYPE.TARGET_AUDIENCE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_SEGT,
    icon: <SegmentsIcon color="#005EB8" />,
  },
  {
    label: 'Performed Events',
    description: 'Using attributes from events happened before',
    value: FILTER_TYPE.PERFORMED_EVENT,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_EVNT,
    icon: <EventSourcesIcon color="#005EB8" />,
  },
];

export const DATA_OPTIONS_FILTER_NO_CUSTOMER = [
  {
    label: 'Visitors',
    description: 'Using attributes of Visitors',
    value: FILTER_TYPE.VISITOR_ATTRIBUTE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
    icon: <VisitorIcon color="#005EB8" />,
  },
  {
    label: 'Target Audiences',
    description: 'Using predefined segments',
    value: FILTER_TYPE.TARGET_AUDIENCE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_SEGT,
    icon: <SegmentsIcon color="#005EB8" />,
  },
  {
    label: 'Performed Events',
    description: 'Using attributes from events happened before',
    value: FILTER_TYPE.PERFORMED_EVENT,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_EVNT,
    icon: <EventSourcesIcon color="#005EB8" />,
  },
];

export const DATA_OPTIONS_FILTER_NO_EVENT_ATTR = [
  {
    label: 'Customers',
    description: 'Using attributes of Customers',
    value: FILTER_TYPE.CUSTOMER_ATTRIBUTE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
    icon: <CustomerIcon color="#005EB8" />,
  },
  {
    label: 'Visitors',
    description: 'Using attributes of Visitors',
    value: FILTER_TYPE.VISITOR_ATTRIBUTE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
    icon: <VisitorIcon color="#005EB8" />,
  },
  {
    label: 'Target Audiences',
    description: 'Using predefined segments',
    value: FILTER_TYPE.TARGET_AUDIENCE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_SEGT,
    icon: <SegmentsIcon color="#005EB8" />,
  },
];

export const DATA_OPTIONS_FILTER_CUSTOMER_NO_EVENT_ATTR = [
  {
    label: 'Customers',
    description: 'Using attributes of Customers',
    value: FILTER_TYPE.CUSTOMER_ATTRIBUTE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
    icon: <CustomerIcon color="#005EB8" />,
  },
  {
    label: 'Target Audiences',
    description: 'Using predefined segments',
    value: FILTER_TYPE.TARGET_AUDIENCE,
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_SEGT,
    icon: <SegmentsIcon color="#005EB8" />,
  },
];

export const MAP_DATA_OPTIONS_FILTER = {
  user_attributes: DATA_OPTIONS_FILTER[0],
  user_attribute: DATA_OPTIONS_FILTER[1], // API lưu là user_attribute , FE thông tin là user_attributes :(
  customer_attribute: DATA_OPTIONS_FILTER[0],
  item_segment: DATA_OPTIONS_FILTER[2],
  event_attribute: DATA_OPTIONS_FILTER[3],
};

export const initDataUIFilter = () => ({
  filterType: {},
  user_attributes: {},
  item_segment: {},
  event_attribute: undefined,
  isFetchInfoData: false,
});

const MAP_CONDITION_TYPE = {
  '-1003': FILTER_TYPE.CUSTOMER_ATTRIBUTE,
  '-1007': FILTER_TYPE.VISITOR_ATTRIBUTE,
};

function toConditionCompPropAPI(item) {
  if (validateItemCondition(item)) {
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    const objValue = buildValueConditionFromUI(item);

    let tempt = {};
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      tempt = {
        conditionType: MAP_CONDITION_TYPE[item.get('property').itemTypeId],
        // property_name: item.get('property').propertyCode,
        column: item.get('property').propertyCode,
        itemTypeId: item.get('property').itemTypeId,
        dataType: item.get('property').itemDataType,
        operator: item.get('operator').value,
        extendValue: item.get('extendValue'),
        ...objValue,
      };
    } else {
      tempt = item.get('backup');
      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}

export const toAPIAudienceAttributes = conditions => {
  if (
    Object.keys(safeParse(conditions, {})).length === 0 ||
    conditions.size === 0
  ) {
    return { OR: [{ AND: [] }] };
  }
  const isInit = conditions.first().get('isInit');
  if (isInit === true) {
    return conditions.first().get('backup');
  }

  const rulesOr = { OR: [] };
  conditions.forEach(condition => {
    const ruleAnd = { AND: [] };
    condition.forEach(item => {
      const conditionType = safeParse(item.get('conditionType'), {});
      if (conditionType.value === MAP_SEGMENT_VALUES.comp_attr) {
        const tempt = safeParse(toConditionCompPropAPI(item), null);

        if (tempt !== null) {
          ruleAnd.AND.push(tempt);
        }
      }
    });
    if (ruleAnd.AND.length > 0) {
      rulesOr.OR.push(ruleAnd);
    }
  });

  if (rulesOr.OR.length === 0) {
    return { OR: [{ AND: [] }] };
  }

  return rulesOr;
};

function toConditionEventAttributesBlast(item) {
  if (validateItemCondition(item)) {
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    const objValue = buildValueConditionFromUI(item);

    let tempt = {};
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      const itemTypeId =
        item.get('property').itemTypeId === 0
          ? null
          : item.get('property').itemTypeId;

      const metaData = {
        itemTypeId,
        itemTypeName: item.get('property').itemTypeName,
        itemPropertyName: item.get('property').name,
        eventPropertySyntax: item.get('property').propertySyntax,
      };
      tempt = {
        conditionType: 'event_attribute',
        // property_name: item.get('property').propertyCode,
        column: item.get('property').name,
        itemTypeId,
        dataType: item.get('property').itemDataType,
        operator: item.get('operator').value,
        propertySyntax: item.get('property').propertySyntax,
        extendValue: item.get('extendValue'),
        ...objValue,
        metadata: metaData,
      };
    } else {
      tempt = item.get('backup');
      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}

export function toConditionEventAttributes(rules) {
  const ruleAnd = [];
  const groupId = generateKey();

  const eventInfo = rules.get('property');
  const eventKey = rules.get('eventKey');

  // eslint-disable-next-line consistent-return
  rules.get('refineWithProperties').forEach((item, _index) => {
    if (validateItemCondition(item)) {
      const statusItemCode = safeParse(
        item.get('statusItemCode'),
        STATUS_ITEM_CODE.ACTIVE,
      );
      const objValue = buildValueConditionFromUI(item);

      const value = item.get('value');

      let tempt = {};
      if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
        const itemTypeId =
          item.get('property').itemTypeId === 0
            ? null
            : item.get('property').itemTypeId;

        const metaData = {
          itemTypeId,
          itemTypeName: item.get('property').itemTypeName,
          itemPropertyName: item.get('property').name,
          eventPropertySyntax: item.get('property').propertySyntax,
          ...(isObject(value) && {
            source: value.source,
            code: value.code,
          }),
        };
        tempt = {
          conditionType: FILTER_TYPE.PERFORMED_EVENT,
          // property_name: item.get('property').propertyCode,
          column: item.get('property').name,
          itemTypeId,
          dataType: item.get('property').itemDataType,
          operator: item.get('operator').value,
          propertySyntax: item.get('property').propertySyntax,
          extendValue: item.get('extendValue'),
          ...objValue,
          metadata: metaData,
          groupId,
          eventInfo: {
            eventCategoryId: eventInfo.eventCategoryId,
            eventActionId: eventInfo.eventActionId,
            label: eventInfo.label,
            nodeId: nth(split(eventInfo.value, '-'), -2),
            eventKey,
          },
        };
      } else {
        tempt = item.get('backup');
        if (tempt !== null && tempt !== undefined) {
          tempt = { ...tempt, ...objValue };
          return tempt;
        }
      }
      ruleAnd.push(tempt);
    }
  });

  return ruleAnd;
}

export const toAPIEventAttributes = (conditions, isBlastCampaign) => {
  if (Object.keys(safeParse(conditions, {})).length === 0) {
    return { OR: [{ AND: [] }] };
  }
  const isInit = conditions.first().get('isInit');
  if (isInit === true) {
    return conditions.first().get('backup');
  }

  const rulesOr = { OR: [] };
  conditions.forEach(condition => {
    const ruleAnd = { AND: [] };
    condition.forEach(item => {
      const toConditionComp = isBlastCampaign
        ? toConditionEventAttributesBlast
        : toConditionEventAttributes;
      const tempt = safeParse(toConditionComp(item), null);

      if (tempt !== null) {
        if (isBlastCampaign) {
          ruleAnd.AND.push(tempt);
        } else {
          tempt.forEach(tmpCondition => {
            ruleAnd.AND.push(tmpCondition);
          });
        }
      }
    });
    if (ruleAnd.AND.length > 0) {
      rulesOr.OR.push(ruleAnd);
    }
  });

  if (rulesOr.OR.length === 0) {
    return { OR: [{ AND: [] }] };
  }

  return rulesOr;
};

const MAP_ITEM_TYPE_ID = {
  '-1003': FILTER_TYPE.CUSTOMER_ATTRIBUTE,
  '-1007': FILTER_TYPE.VISITOR_ATTRIBUTE,
};

const wrapperToAPIAudienceAttributes = data => {
  const { itemTypeId = '-1003', rules } = data;
  const dataToAPI = {
    filterType: MAP_ITEM_TYPE_ID[itemTypeId],
    filters: {},
  };

  dataToAPI.filters = toAPIAudienceAttributes(rules);

  return dataToAPI;
};

const wrapperToAPIEventAttributes = (data, isBlastCampaign) => {
  const { rules } = data;
  const dataToAPI = {
    filterType: FILTER_TYPE.PERFORMED_EVENT,
    filters: {},
  };

  dataToAPI.filters = toAPIEventAttributes(rules, isBlastCampaign);

  return dataToAPI;
};

const wrapperToAPIAudienceSegment = data => {
  const dataToAPI = {
    filterType: FILTER_TYPE.TARGET_AUDIENCE,
    // filters: toAPIAudienceSegment(data),
    // ...toAPITargetAudience(data, 'includes'),
    ...toAPITargetAudience(data),
  };

  return dataToAPI;
};

export const toAPINodeFilter = (data, isBlastCampaign = false) => {
  if (Object.keys(safeParse(data, {})).length === 0) {
    return {};
  }

  const filterType = safeParse(
    data.filterType,
    MAP_DATA_OPTIONS_FILTER.user_attributes,
  );

  if (
    filterType.value === FILTER_TYPE.VISITOR_ATTRIBUTE ||
    filterType.value === FILTER_TYPE.CUSTOMER_ATTRIBUTE ||
    filterType.value === 'user_attributes'
  ) {
    return wrapperToAPIAudienceAttributes(data['user_attributes']);
  } else if (filterType.value === FILTER_TYPE.TARGET_AUDIENCE) {
    return wrapperToAPIAudienceSegment(data[filterType.value]);
  } else if (filterType.value === FILTER_TYPE.PERFORMED_EVENT) {
    return wrapperToAPIEventAttributes(data[filterType.value], isBlastCampaign);
  }

  // unselect type
  return {
    filterType: null,
    filters: null,
  };
};

export const toUINodeFilter = data => {
  const { filterType } = data;
  const dataToUI = initDataUIFilter();
  dataToUI.filterType = MAP_DATA_OPTIONS_FILTER[filterType];

  if (filterType === FILTER_TYPE.TARGET_AUDIENCE) {
    const { itemTypeId } = data;
    dataToUI.itemTypeId = itemTypeId;
    const dataTargetAudience = toUITargetAudience({
      itemTypeId,
      dataFilter: data,
    });
    dataToUI.item_segment = dataTargetAudience;
    // Object.keys(dataTargetAudience).forEach(key => {
    //   dataToUI[key] = dataTargetAudience[key];
    // });
    // dataToUI.filterType = MAP_DATA_OPTIONS_FILTER.item_segment;
  } else if (
    filterType === FILTER_TYPE.CUSTOMER_ATTRIBUTE ||
    filterType === FILTER_TYPE.VISITOR_ATTRIBUTE
  ) {
    const userAttributes = {};
    userAttributes.itemTypeId =
      filterType === FILTER_TYPE.CUSTOMER_ATTRIBUTE
        ? ITEM_TYPE_ID.CUSTOMER
        : ITEM_TYPE_ID.VISITOR;
    userAttributes.rules = OrderedMap({
      'data-init': OrderedMap({
        backup: data.filters,
        isInit: true,
      }),
    });

    dataToUI.user_attributes = userAttributes;
    dataToUI.isInitUserAttrs = true;
  } else if (filterType === FILTER_TYPE.PERFORMED_EVENT) {
    const eventAttributes = {};

    eventAttributes.rules = OrderedMap({
      'data-init': OrderedMap({
        backup: data.filters,
        filters: data.filters,
        isInit: true,
      }),
    });

    dataToUI.event_attribute = eventAttributes;
  }

  // console.log('dataToUI', { dataToUI, data });

  return dataToUI;
};

const splitConditionsByGroup = conditions => {
  return flatMap(conditions, conditionGroup => {
    const grouped = groupBy(conditionGroup.AND, 'groupId');

    return grouped;
  });
};

export const toConditionPerfEventUI = (
  data,
  eventValue,
  insightPropertyIds,
  triggerNodeId,
) => {
  const rules = [];

  const groupRules = splitConditionsByGroup(data.filters?.OR);

  groupRules.forEach(objRules => {
    const groupOrId = generateKey();
    Object.values(objRules).forEach(filters => {
      rules.push({
        filters: {
          OR: [
            {
              AND: filters.map(rule => {
                return {
                  ...rule,
                  value: rule.metadata?.code
                    ? {
                        source: rule.metadata.source,
                        code: rule.metadata.code,
                      }
                    : rule.value,
                  item_type_id: rule.itemTypeId,
                };
              }),
            },
          ],
        },
        groupOrId,
        insightPropertyIds,
        eventActionId:
          filters[0]?.eventInfo?.eventActionId || eventValue.eventActionId,
        eventCategoryId:
          filters[0]?.eventInfo?.eventCategoryId || eventValue.eventCategoryId,
        eventTrackingName:
          filters[0]?.eventInfo?.eventTrackingName || eventValue.label,
        nodeId: filters[0]?.eventInfo?.nodeId || triggerNodeId,
        eventKey: filters[0]?.eventInfo?.eventKey || eventValue?.eventKey,
        condition_type: MAP_SEGMENT_VALUES.perf_event,
        conditionType: 'event_attribute',
      });
    });
  });

  const condition = OrderedMap({
    'data-init': OrderedMap({
      backup: safeParse(rules, []),
      isInit: true,
    }),
  });
  return condition;
};

export const mapDataResponseListEvents = events => {
  return {
    code: 200,
    data: events,
  };
};
