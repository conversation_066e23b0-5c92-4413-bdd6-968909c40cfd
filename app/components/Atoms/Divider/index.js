// Libraries
import classNames from 'classnames';
import * as React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

export const Divider = props => {
  const {
    type,
    dot,
    dashed,
    className,
    style,
    width,
    height,
    children,
    label,
    ...restOf
  } = props;

  return (
    <Wrapper
      className={classNames(`--${type}`, className, {
        '--dot': dot,
        '--dashed': dashed,
      })}
      style={{ ...style, height, width }}
      {...restOf}
    >
      {label && (
        <Label className="label">
          <span>{label}</span>
        </Label>
      )}
    </Wrapper>
  );
};

Divider.propTypes = {
  type: PropTypes.string,
  dot: PropTypes.bool,
  dashed: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  width: PropTypes.string,
  height: PropTypes.string,
  children: PropTypes.string,
  label: PropTypes.string,
};

Divider.defaultProps = {
  type: 'vertical',
  dot: true,
};

const Wrapper = styled.div`
  box-sizing: border-box;
  border-width: 0;
  position: relative;

  &.--horizontal {
    border-top-width: 1px;
    border-color: #d2d2d2;
    width: 100%;
    margin: 0 15px;
  }

  &.--vertical {
    border-right-width: 1px;
    border-color: #d2d2d2;
    height: 35px;
    width: max-content;
    margin: 0 15px;
  }

  &.--dot {
    border-style: dotted;
    border-color: #d2d2d2;

    &.--vertical {
      border-right-width: 2px;
    }
  }

  &.--dashed {
    border-style: dashed;
    border-color: #d2d2d2;

    &.--vertical {
      border-right-width: 2px;
    }
  }
`;

const Label = styled.div`
  position: absolute;

  padding-right: 10px;
  left: -10px;
  top: -10px;
  background: white;
  font-size: 12px;
  font-weight: 500;

  span {
    background: #f0f0f0;
    border-radius: 20px;
    padding: 4px 10px;
  }
`;
