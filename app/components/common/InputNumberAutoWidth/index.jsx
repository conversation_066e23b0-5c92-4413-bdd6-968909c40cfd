import { InputNumber } from '@antscorp/antsomi-ui';
import React, { useState, useRef, useEffect } from 'react';

const InputNumberAutoWidth = props => {
  const { value: valueProp, style, ...rest } = props || {};

  const [value, setValue] = useState(valueProp);
  const [width, setWidth] = useState(40);
  const textRef = useRef(null);

  useEffect(() => {
    if (textRef.current) {
      setWidth(textRef.current.offsetWidth + 40);
    }
  }, [value]);
  useEffect(() => {
    if (valueProp !== value) {
      setValue(valueProp);
    }
  }, [valueProp]);

  return (
    <div style={{ display: 'inline-block', position: 'relative' }}>
      <span
        ref={textRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          whiteSpace: 'nowrap',
          fontSize: 12,
        }}
      >
        {value || 0}
      </span>
      <InputNumber
        value={value}
        style={{
          width: `${width}px`,
          minWidth: '50px',
          transition: 'width 0.2s ease-in-out',
          ...(style || {}),
        }}
        {...rest}
      />
    </div>
  );
};

export default InputNumberAutoWidth;
