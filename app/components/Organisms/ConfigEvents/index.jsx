/* eslint-disable indent */
import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { StyledPerformEvent } from './styled';
import EventChip from './EventChip';
import DialogEvent from './DialogEvent';
import AddEventDropdown from './AddEventDropdown';

import { ACTION } from './config';

const ConfigEvents = props => {
  const {
    onChange,
    chipProps = () => ({}),
    trackingHasFilter = true,
    disabled = false,
    ...otherProps
  } = props;

  const [popupState, setPopupState] = useState({
    isOpen: false,
    id: null,
  });

  const handleAction = renderEventCb => (action, value) => {
    switch (action) {
      case ACTION.showPopupFilter: {
        const { id } = value;

        setPopupState({ isOpen: true, id });
        break;
      }
      case ACTION.removeEvent: {
        if (typeof renderEventCb === 'function') {
          const { id } = value;

          renderEventCb('DELETE_ITEM', { groupIndex: id });
        }
        break;
      }
      default:
        break;
    }
  };

  const renderUI = ({ renderedEvents, callback }) =>
    renderedEvents.map(renderedEvent => {
      const { key, event, renderBlockEvent } = renderedEvent;

      const hiddenActions = [];

      if (renderedEvents.length <= 1 || disabled) {
        hiddenActions.push(ACTION.removeEvent);
      }

      return (
        <React.Fragment key={key}>
          <EventChip
            id={key}
            item={event.toJS()}
            onClickAction={handleAction(callback)}
            hiddenActions={hiddenActions}
            disabled={disabled}
            {...chipProps(key, event)}
          />

          <DialogEvent
            isOpen={popupState.isOpen && popupState.id === key}
            onCloseDialog={() => setPopupState({ isOpen: false, id: null })}
          >
            {renderBlockEvent({
              hiddenHeader: true,
              trackingHasFilter,
              hiddenSelectEvent: !popupState.isOpen,
              hiddenDatasouces: !popupState.isOpen,
              hiddenRefine: true,
              showHeader: false,
            })}
          </DialogEvent>
        </React.Fragment>
      );
    });

  const renderAddButton = useCallback(renderParams => {
    const { addEvent, events = [] } = renderParams;

    return (
      <AddEventDropdown
        events={events}
        onClickEvent={event => addEvent({ event })}
      >
        Add event
      </AddEventDropdown>
    );
  }, []);

  return (
    <StyledPerformEvent
      translateLabelTitle="_TITL_WAITING_EVENT"
      hiddenTitle
      showTitle={false}
      disabledEvent={disabled}
      hiddenButtonAdd={disabled}
      disabledEventConditions={disabled}
      allowDuplicateEvents={false}
      paramsFetchEvent={{}}
      isMultiple
      renderUI={renderUI}
      renderAddButton={renderAddButton}
      maximumEvent={10}
      onChange={onChange}
      {...otherProps}
    />
  );
};

ConfigEvents.propTypes = {
  onChange: PropTypes.func,
  chipProps: PropTypes.func,
  trackingHasFilter: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default ConfigEvents;
