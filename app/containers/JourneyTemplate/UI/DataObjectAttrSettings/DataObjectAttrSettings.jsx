import React, { useMemo } from 'react';
import { get } from 'lodash';
import { useSelector } from 'react-redux';

import { JOURNEY_OBJECT_TYPE, MODE } from '../../constant';
import BOAttributeDetail from '../../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Detail';
import { useActiveObj } from '../../hooks/useActiveObj';
import { selectMode } from '../../selector';
import { ERROR_CODE } from '../../utils/config';

export const DataObjectAttrSettings = () => {
  const mode = useSelector(selectMode);

  const { activeObj, info, saveInfo } = useActiveObj();

  const isEventAttrObject =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.dataObjectAttr;

  const errors = useMemo(() => {
    const itemTypeNameExist = info.errors?.find(
      i => i.code === ERROR_CODE.BO_ATTR_CODE_EXIST,
    );

    if (itemTypeNameExist) {
      return [
        {
          code: 'code',
          message: 'This code has been taken, please use another one',
        },
      ];
    }

    return [];
  }, [info.errors]);

  if (!isEventAttrObject) return null;

  const getComponentProperties = () => {
    const properties = {};

    if (!info.isExist) {
      properties.settings = saveInfo.settings;
      properties.newItemTypeId = get(
        info,
        'dataObject.verify.settings.item_type_id',
      );
    }

    if (info.isExist && mode === MODE.Use) {
      properties.itemTypeId = info.settings.item_type_id;
      properties.itemPropertyName = info.settings.item_property_name;
    }

    if (info.isExist && mode === MODE.Save) {
      properties.itemTypeId = info.itemTypeId;
      properties.itemPropertyName = info.itemPropertyName;
    }

    return properties;
  };

  // console.log({ info });
  // console.log({ activeObj });
  // console.log({ saveInfo });
  // console.log({ mode });
  // console.log(getComponentProperties());

  return (
    <BOAttributeDetail
      isViewMode
      errors={errors}
      {...getComponentProperties()}
    />
  );
};
