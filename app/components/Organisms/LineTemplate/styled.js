// Styled
import styled from 'styled-components';

// Components
import { UIButton } from '@xlab-team/ui-components';
import { Grid } from '@material-ui/core';

export const ContainerTemplate = styled.div`
  width: 100%;
`;

export const GridContainer = styled(Grid)`
  padding: 6px 0px;
`;

export const Divider = styled.div`
  width: 100%;
  height: 2px;
  background-image: linear-gradient(
    to right,
    #b8cfe6 33%,
    rgba(255, 255, 255, 0) 0%
  );
  background-repeat: repeat-x;
  background-position: center center;
  background-size: 9px 2px;
`;

export const PaddingElement = styled.div`
  height: 24px;
`;

export const ContainerSlider = styled.div`
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
`;

export const WarningSMSRules = styled.div`
  text-align: right;

  .icon-info-rule {
    margin-right: 6px;
    font-size: 16px;
    color: #595959;
  }

  .text-rule {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
  }

  .text-warn {
    font-style: italic;
    font-size: 10px;
    color: #999;
    margin: 0;
  }

  width: ${({ width = 'auto' }) => width};

  .MuiFormHelperText-root {
    margin: 0;
  }
`;

export const WrapperCenterFlexCenter = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
`;

export const Title = styled.div`
  color: #333;
  white-space: nowrap;
  font-size: 12px;
  /* margin-right: 0.5rem; */
`;

export const CalloutText = styled.div`
  margin: 7px 0px 0px;
  width: 100%;
  text-align: right;
  font-weight: 400;
  font-size: 11px;
  color: #a2a2a2;
`;

export const AddBtnCustom = styled(UIButton)`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;

  > span {
    margin-left: 0px !important;
    font-weight: bold;
  }
`;
