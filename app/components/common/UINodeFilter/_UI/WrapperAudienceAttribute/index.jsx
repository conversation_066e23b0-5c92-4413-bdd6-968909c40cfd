/* eslint-disable no-else-return */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
/* eslint-disable  */
// Libraries
import React, { useEffect, useRef } from 'react';
import { useImmer } from 'use-immer';
import styled from 'styled-components';

// Components
import AudienceAttributes from '../AudienceAttributes';

// Utils
import { labelTargetVisitor, labelTargetCustomer, initState } from './utils';
import { safeParse } from '../../../../../utils/common';
import { FILTER_TYPE } from '../../utils';

const WrapperTargetAudience = styled.div`
  /* overflow-x: auto; */

  .MuiTypography-body1 {
    font-size: 12px;
  }

  fieldset {
    margin-bottom: 22px;
  }

  .private-overlay {
    left: -4px;
  }

  .MuiFormControlLabel-root {
    margin-left: -7px;
  }
`;

const WrapperAudienceAttributes = props => {
  const [state, setState] = useImmer(initState);
  const { initData, itemTypeId } = props;

  const preItemTypeId = useRef('-1003');

  const setStateCommon = object => {
    setState(draft => {
      Object.keys(object).forEach(key => {
        draft[key] = object[key];
      });
    });
  };

  useEffect(() => {
    preItemTypeId.current = state.itemTypeId;
  }, [state.itemTypeId]);

  const onChange = e => {
    setStateCommon({ itemTypeId: e.target.value });
  };

  useEffect(() => {
    if (Object.keys(safeParse(initData, {})).length > 0) {
      setStateCommon({
        itemTypeId: safeParse(initData.itemTypeId, '-1007'),
      });
    } else {
      const initItemTypeId = safeParse(props.itemTypeId, '-1007');
      setStateCommon({
        itemTypeId: initItemTypeId,
      });
    }
  }, [props.componentId]);

  const onChangeAudienceAttributes = data => {
    const filterType =
      itemTypeId === '-1003'
        ? FILTER_TYPE.CUSTOMER_ATTRIBUTE
        : FILTER_TYPE.VISITOR_ATTRIBUTE;

    props.onChange(filterType, {
      rules: data,
      itemTypeId: itemTypeId,
    });
  };
  let renderedOptions = [];

  if (props.isShowFullOption === false && props.itemTypeId == '-1003') {
    renderedOptions = [
      {
        value: '-1007',
        checked: String(state.itemTypeId) === '-1007',
        disabled: true,
      },
      {
        value: '-1003',
        checked: String(state.itemTypeId) === '-1003',
        disabled: props.disabled,
      },
    ];
  } else {
    renderedOptions = [
      {
        value: '-1007',
        checked: String(state.itemTypeId) === '-1007',
        disabled: props.disabled,
      },
      {
        value: '-1003',
        checked: String(state.itemTypeId) === '-1003',
        disabled: props.disabled,
      },
    ];
  }

  renderedOptions[0]['label'] = labelTargetVisitor;
  renderedOptions[1]['label'] = labelTargetCustomer;

  return (
    <WrapperTargetAudience data-test={props['data-test']}>
      <AudienceAttributes
        onChange={onChangeAudienceAttributes}
        itemTypeId={itemTypeId}
        preItemTypeId={preItemTypeId}
        initData={
          Object.keys(initData).length === 0 ? undefined : initData.rules
        }
        validateKey={props.validateKey}
        componentId={props.componentId}
        disabled={props.disabled}
        design={props.design}
        isViewMode={props.isViewMode}
      />
    </WrapperTargetAudience>
  );
};

WrapperAudienceAttributes.defaultProps = {
  initData: {},
  onChange: () => {},
  isShowFullOption: true,
  disabled: false,
};

export default WrapperAudienceAttributes;
