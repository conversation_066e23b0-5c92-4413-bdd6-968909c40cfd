/* eslint-disable react/prop-types */
/* eslint-disable  no-shadow */
/* eslint-disable  indent */
import React, { useEffect, useMemo, useRef, useState } from 'react';

import SelectTree from 'components/form/UISelectCondition';
import Icon from '@material-ui/core/Icon';
import {
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import { Chip } from '@material-ui/core';
import classnames from 'classnames';

import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';
import ObjectServices from '../../../../../services/Object';

import {
  DataSourceBlockContent,
  BlockRefineCondition,
  SubContentTooltip,
} from '../../styled';
import { WrapperIcon } from '../../HeaderCondition/styled';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ConditionError from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import ConditionValue from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionValue';
import {
  ARCHIVE_STATUS,
  STATUS_ITEM_CODE,
  REMOVE_STATUS,
  TYPE_ATTRIBUTE,
} from '../../../../../utils/constants';
import { safeParse } from '../../../../../utils/common';
import { formatValueItemByDataType } from '../../../../../containers/Filters/ItemRule/Preview/utils';
import {
  filterDeep,
  makeArrayToLabelFilter,
} from '../../../../../utils/web/utils';
import { SelectedConditionTextMode } from '../../../UINodeFilter/styled';
import {
  getLabelFull,
  getLabelMulti,
} from '../../../../form/AutoSuggestion/utils';
import { getInnerTextWidth } from '../../../../../modules/Dashboard/MarketingHub/Journey/Create/Content/InputAutoResize/utils';

const labelSelectAttrs = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_ATTRIBUTE,
  'Select attribute',
);
const labelSelectOperator = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_OPERATOR,
  'Select operator',
);

const Refine = props => {
  const gridRef = useRef(null);

  const [lableCus, setLableCus] = useState([]);

  const {
    item,
    options,
    groupIndex,
    itemIndex,
    refineIndex,
    eventValue,
    // version,
    // eventValue,
  } = props;

  const filteredOptions = useMemo(
    () =>
      filterDeep(
        props.options,
        (_value, _key, option) => {
          const { status, type } = option;

          return (
            ![ARCHIVE_STATUS, REMOVE_STATUS].includes(Number(status)) ||
            type !== TYPE_ATTRIBUTE.COMPUTED
          );
        },
        {
          childrenPath: 'options',
        },
      ),
    [options],
  );

  // console.log(filteredOptions);

  const changeProperty = value => {
    props.callback('CHANGE_PROPERTY', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
    });
  };

  const changeOperator = value => {
    props.callback('CHANGE_OPERATOR', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
    });
  };

  const changeValue = value => {
    props.callback('CHANGE_VALUE', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
    });
  };

  const changeOtherValue = (name, value) => {
    props.callback('CHANGE_VALUE', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
      name,
    });
  };

  const deleteItem = () => {
    props.callback('DELETE_ITEM', {
      groupIndex,
      itemIndex,
      refineIndex,
    });
  };

  const property = safeParse(item.get('property'), null);
  const value = safeParse(item.get('value'), null);
  const error = safeParse(item.get('error'), 0);
  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  const fetchLabelData = async (feServices, valueIds, property) => {
    const params = {
      data: {},
    };
    const result = [];
    params.data = {
      ids: valueIds,
      decryptFields: property ? [] : [`${property.encryptCode}`],
      itemTypeId: property && property.itemTypeId,
      propertyCode: property && property.encryptCode,
      itemTypeName: property && property.itemTypeName,
      config: property && property.configSuggestion,
      itemPropertyName: property && property.encryptCode,
      scope: property && property.configSuggestion && property.configSuggestion.scope,
    };
    const res = await ObjectServices[feServices].lookupByIds(params);
    if (res && res.list) {
      setLableCus(res.list.map(item => item.label));
    }
    return result;
  };

  useDeepCompareEffect(() => {
    const feServices = 'suggestion';
    if (!value) {
      setLableCus([]);
    } else if (props.isViewMode && value && property) {
      fetchLabelData(feServices, value, property);
    }
  }, [value, property]);

  const getSelectedConditionTextMode = item => {
    const propertyMode = item.get('property');
    const operator = item.get('operator');

    const initValue = formatValueItemByDataType(item, true, true);
    const value = Array.isArray(initValue)
      ? makeArrayToLabelFilter(initValue)
      : initValue;
    if (
      operator &&
      [
        'after_date',
        'before_date',
        'contains',
        'doesnt_contain',
        'start_with',
        'not_start_with',
        'end_with',
        'not_end_with',
        'equals',
        'not_equals',
      ].includes(operator.value)
    ) {
      return (
        <Chip
          style={{ minHeight: '28px', height: 'unset' }}
          variant="outlined"
          label={
            <SelectedConditionTextMode>
              <span className="property-text">{propertyMode.label}&nbsp;</span>
              <span className="operator-text">{operator.label}&nbsp;</span>
              <span className="property-text">{`"${value}"`}</span>
              <span style={{ position: 'relative', top: '2px' }}>
                <ConditionError
                  style={{ width: '100%' }}
                  statusItemCode={statusItemCode}
                  item={props.item}
                  isErrorIcon
                />
              </span>
            </SelectedConditionTextMode>
          }
        />
      );
    }
    if (operator && ['matches', 'not_matches'].includes(operator.value)) {
      const label = safeParse(item.get('value'), null);
      const preText = `${propertyMode.label} ${operator.label} `;
      const preTexWidth = +getInnerTextWidth(preText, { fontSize: '12px' });
      const valueMulti = getLabelMulti(
        lableCus && lableCus.length ? lableCus : label,
        gridRef &&
          gridRef.current &&
          gridRef.current.offsetWidth - preTexWidth - 70,
        true,
        false,
        {
          fontSize: '12px',
        },
      );

      const valueTooltip = getLabelFull(
        lableCus.length ? lableCus : value,
        true,
        ['array_string', 'string'].includes(property.dataType),
      );

      return (
        <Chip
          style={{ minHeight: '28px', height: 'unset' }}
          variant="outlined"
          label={
            <SelectedConditionTextMode>
              <span className="property-text">{`${propertyMode.label} `}</span>
              <span className="operator-text">{operator.label}&nbsp;</span>
              <span className="property-text">{valueMulti.labels}&nbsp;</span>
              {valueMulti.labelMore && (
                <span className="property-text">and</span>
              )}
              <UITippy
                content={valueTooltip.labels}
                showPopupWhenClick
                subContent={
                  <SubContentTooltip>
                    Click <span>{valueMulti.labelMore}</span> to view more
                  </SubContentTooltip>
                }
              >
                <span className="text-more">{valueMulti.labelMore}&nbsp;</span>
              </UITippy>
              <span style={{ position: 'relative', top: '2px' }}>
                <ConditionError
                  style={{ width: '100%' }}
                  statusItemCode={statusItemCode}
                  item={props.item}
                  isErrorIcon
                />
              </span>
            </SelectedConditionTextMode>
          }
        />
      );
    }
    return (
      <Chip
        style={{ minHeight: '28px', height: 'unset' }}
        variant="outlined"
        label={
          <SelectedConditionTextMode>
            <span className="property-text">{property.label}&nbsp;</span>
            <span className="operator-text">
              {operator.label.replace(/\bis\b/gi, '').trim()}
              &nbsp;
            </span>
            <span className="property-text">{value}</span>
            <span style={{ position: 'relative', top: '2px' }}>
              <ConditionError
                style={{ width: '100%' }}
                statusItemCode={statusItemCode}
                item={props.item}
                isErrorIcon
              />
            </span>
          </SelectedConditionTextMode>
        }
      />
    );
  };

  const divDisabled =
    statusItemCode !== STATUS_ITEM_CODE.ACTIVE &&
    statusItemCode !== STATUS_ITEM_CODE.DISABLED;

  return (
    <React.Fragment>
      <DataSourceBlockContent className={props.isViewMode && 'padding-sm'}>
        <div className="title">
          <div
            className={classnames('refine', {
              'view-mode': props.isViewMode,
            })}
          >
            {props.title}
          </div>
        </div>
        {props.isViewMode ? (
          <div className="w-100" ref={gridRef}>
            {getSelectedConditionTextMode(item)}
          </div>
        ) : (
          <div className="content" style={{ display: 'flex' }}>
            <BlockRefineCondition>
              <div className="property">
                <WrapperDisable disabled={divDisabled}>
                  <SelectTree
                    onlyParent={props.onlyParent}
                    displayFormat
                    use="tree"
                    options={filteredOptions}
                    isParentOpen={props.isParentOpen}
                    value={property}
                    onChange={changeProperty}
                    placeholder={labelSelectAttrs}
                    errors={
                      error === 1
                        ? [
                            getTranslateMessage(
                              TRANSLATE_KEY._NOTI_FIELD_REQUIRED,
                              'Property is required',
                            ),
                          ]
                        : []
                    }
                  />
                  <ConditionError keyError={1} error={error} />
                </WrapperDisable>
              </div>

              <div className="operator">
                <WrapperDisable disabled={divDisabled}>
                  <SelectTree
                    onlyParent={props.onlyParent}
                    use="tree"
                    isSearchable={false}
                    options={item.get('operators').list}
                    value={item.get('operator')}
                    onChange={changeOperator}
                    placeholder={labelSelectOperator}
                    errors={
                      error === 2
                        ? [
                            getTranslateMessage(
                              TRANSLATE_KEY._NOTI_FIELD_REQUIRED,
                              'Property is required',
                            ),
                          ]
                        : []
                    }
                  />
                  <ConditionError keyError={2} error={error} />
                </WrapperDisable>
              </div>

              <div className="value">
                <WrapperDisable disabled={divDisabled}>
                  {property !== null && (
                    <ConditionValue
                      maxWidth="100%"
                      width="100%"
                      item={item}
                      eventValue={eventValue}
                      dataType={property.dataType}
                      changeValue={changeValue}
                      changeOtherValue={changeOtherValue}
                      error={error}
                      isShowLabel={false}
                      isUseLabel={false}
                    />
                  )}
                </WrapperDisable>
              </div>
              <ConditionError
                statusItemCode={statusItemCode}
                item={item}
                isErrorIcon
              />

              {!props.isViewMode && (
                <div className="cancel">
                  <WrapperIcon onClick={deleteItem}>
                    <Icon style={{ color: '#7f7f7f', fontSize: '20px' }}>
                      clearOutlinedIcon
                    </Icon>
                  </WrapperIcon>
                </div>
              )}
            </BlockRefineCondition>
          </div>
        )}
      </DataSourceBlockContent>
    </React.Fragment>
  );
};

export default React.memo(Refine);
