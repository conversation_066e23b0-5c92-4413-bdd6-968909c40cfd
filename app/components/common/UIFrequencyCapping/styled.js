import styled from 'styled-components';
import { UINumber } from '@xlab-team/ui-components';
import { Alert } from '@antscorp/antsomi-ui';

export const UINumberStyled = styled(UINumber)`
  width: 8rem;
  margin-bottom: 0.25rem;
`;

export const ContainerNodeContent = styled.div`
  padding-bottom: 30px;
  .MuiFormControl-marginNormal {
    margin-top: 0 !important;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }

  .MuiTypography-body1 {
    font-size: 0.813rem !important;

    /* margin-left: -7px !important; */
  }
`;

export const Title = styled.div`
  color: #7f7f7f;
  /* width: 100%; */
  white-space: nowrap;
`;

export const StrongText = styled.span`
  color: #000000;
  font-size: 12px;
`;

export const WrapperCenter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const WrapperGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 2fr;
  grid-template-rows: 1.5rem;
  align-items: center;
`;

export const WrapperFlex = styled.div`
  display: flex;
  align-items: center;
`;
export const WrapperOr = styled.div`
  background-color: #ededed;
  margin: 5px 0px 5px 24px;
  /* margin-left: 24px; */
  justify-content: center;
  align-items: center;
  display: flex;
  width: fit-content;
  padding: 0 10px;
  border-radius: 10px;
`;
export const AlertStyled = styled(Alert)`
  padding: 5px 10px !important;
  margin: 5px 0px 5px 24px;
  width: fit-content;
`;
