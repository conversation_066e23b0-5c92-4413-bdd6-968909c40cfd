// Libraries
import { isArray } from 'lodash';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Constants
import { CONDITION_TYPE, OPERATOR } from './constants';

export const initNewCondition = (value = {}) => {
  return { value, operator: OPERATOR.EQUAL_TO, type: CONDITION_TYPE.EVENT };
};

export const isDisabledType = type =>
  ['array_string', 'array_datetime', 'array_number'].includes(type);

export const isDisabledOperator = operator =>
  [OPERATOR.EQUAL_TO, OPERATOR.NOT_EQUAL_TO].includes(operator);

export const mapOptionsWithTooltip = options => {
  if (!isArray(options)) return options;

  return options.map(option => {
    if (isDisabledType(option?.dataType)) {
      return {
        ...option,
        disabled: true,
        tooltip: translate(
          translations._PERSONALIZE_PROCESS_CODE_TOOLTIP,
          'Operator did not support Array data type',
        ),
      };
    }

    return option;
  });
};
