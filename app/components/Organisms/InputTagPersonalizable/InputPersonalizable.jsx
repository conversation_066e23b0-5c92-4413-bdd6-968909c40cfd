/* eslint-disable import/no-cycle */
// Libraries
import React, { memo, useCallback, useMemo, useRef } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  CONTENT_SOURCE_GROUP,
  EMOJI_COLLECTIONS,
  EmojiPopover,
  Flex,
  TAG_TYPE,
  MIN_H_WRAPPER,
  DETECT_LINK,
  SHORT_LINK_TYPE,
  deepUnescape,
  SHORT_LINK_V2,
} from '@antscorp/antsomi-ui';
import { InputTag } from 'components/Atoms/InputTag';
import {
  EmojiBearIcon,
  EmojiLaughIcon,
  EmojiSmileIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import { Personalizable } from '../../Molecules';

// Constants
import { CHANNEL_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import { EXTRA_ATTR_TYPE } from '../../Molecules/Personalizable/constants';

// Utils
import { addMessageToQueue } from '../../../utils/web/queue';
import { extractUrl } from './utils';
import { ADD_DYNAMIC_OPTION } from '../../common/UIEditorPersonalization/Input/utils';
import { getRemoveFormatKey } from '../../Molecules/Personalizable/utils';

const {
  VISITOR,
  CUSTOM_FN,
  CUSTOMER,
  EMOJI,
  EVENT,
  OBJECT_WIDGET,
  PROMOTION_CODE,
  SHORT_LINK,
  JOURNEY,
  CAMPAIGN,
  VARIANT,
  ALLOCATED_CODE,
  CUSTOM_TAG,
} = TAG_TYPE;

const PATH =
  'app/components/Organisms/InputTagPersonalizable/InputPersonalizable.jsx';

function InputPersonalizable(props) {
  // Props
  const {
    disabled,
    name,
    useEmoji,
    errors,
    channelCode,
    isViewMode,
    initValue, // use only for init
    readonlyTag,
    shouldEscapeHtml,
    maxHeight,
    placeholder,
    dataState,
    tagProperties,
    mapAttributes,
    mapErrorAttributes,
    listTemplateCustom,
    variantExtraData,
    showMergeTagDisplay,
    useAllocatedCode,
    isNewCustomTag,
    enableShortLink,
    hiddenDynamicOption,
    moduleConfig,
    onChange,
    onTagRemove,
    onChangeExtraDataTagProperty,
    isForceHideBtnPersonalization,
    showShortLinkWithBroadcast,
    onlyShowGeneralShortlink,
  } = props;

  // Refs
  const inputTagRef = useRef(null);
  const personalizableRef = useRef(null);

  const isChannelLine = channelCode === CHANNEL_CODE.LINE;
  const isChannelViber = channelCode === CHANNEL_CODE.VIBER;

  const emojiCollections = useMemo(() => {
    const collections = [
      { key: EMOJI_COLLECTIONS.COMMON, label: <EmojiSmileIcon /> },
    ];

    // Add emoji for Viber
    if (isChannelViber) {
      collections.push({
        key: EMOJI_COLLECTIONS.VIBER,
        label: <EmojiLaughIcon />,
      });
    }

    // Add emoji for Line
    if (isChannelLine) {
      collections.push({
        key: EMOJI_COLLECTIONS.LINE,
        label: <EmojiBearIcon />,
      });
    }

    return collections;
  }, [isChannelLine, isChannelViber]);

  const statusMemoized = useMemo(() => {
    if (!_.isArray(errors)) return undefined;

    const errs = errors.filter(err => !_.isEmpty(err));
    return errs.length ? 'error' : undefined;
  }, [errors]);

  const hiddenDynamicOptionMemo = useMemo(() => {
    if (_.isArray(hiddenDynamicOption) && onlyShowGeneralShortlink) {
      return _.concat(hiddenDynamicOption, [ADD_DYNAMIC_OPTION.addPer.value]);
    }

    return hiddenDynamicOption;
  }, [hiddenDynamicOption, onlyShowGeneralShortlink]);

  const handleTagRemove = useCallback(
    (removedTagDetail = {}) => {
      const { data: removedTag } = removedTagDetail;

      if (_.isFunction(onTagRemove) && removedTag?.key) {
        const removeTagProperty = {
          ..._.get(tagProperties, removedTag.key, {}),
          tagId: removedTag.key,
        };

        const { tagId, type } = removeTagProperty;
        const isCustomFn = type === CUSTOM_FN;
        const extraAttribute = {
          type: isCustomFn ? EXTRA_ATTR_TYPE.CUSTOM_FN : EXTRA_ATTR_TYPE.FORMAT,
          removeFormatKey: getRemoveFormatKey({ tagId, tagProperties }),
        };
        onTagRemove(removeTagProperty, extraAttribute);
      }
    },
    [tagProperties, onTagRemove],
  );

  const handleTagClick = useCallback(
    tagDetail => {
      try {
        const { data: tagDetailData = {}, tag: tagElement } = tagDetail;

        let tagData = _.cloneDeep(tagDetailData);
        if (shouldEscapeHtml) {
          tagData = deepUnescape(tagData);
        }

        const { type: tagType = '', value: tagValue = '' } = tagData;

        // Not support edit emoji
        if (tagType === EMOJI) return;

        if (personalizableRef.current) {
          const fallbackFn = () => {};

          const {
            onUpdateShortlink = fallbackFn,
            onUpdatePersonalizationTag = fallbackFn,
          } = personalizableRef.current || {};

          switch (tagType) {
            case DETECT_LINK: {
              if (typeof onUpdateShortlink === 'function') {
                const { INDIVIDUAL, GENERAL } = SHORT_LINK_TYPE;

                // Not allow dynamic link with broadcast
                const useGeneralType =
                  showShortLinkWithBroadcast || onlyShowGeneralShortlink;

                const shortlinkType = useGeneralType ? GENERAL : INDIVIDUAL;

                onUpdateShortlink(tagValue, shortlinkType, tagElement);
              }
              break;
            }
            case SHORT_LINK: {
              const url = extractUrl(tagValue);
              const shortlinkType = _.get(tagData, 'shortlinkType', '');

              if (typeof onUpdateShortlink === 'function') {
                onUpdateShortlink(url, shortlinkType, tagElement);
              }
              break;
            }
            case SHORT_LINK_V2: {
              const { shortener, url, shortlinkType } = tagData;

              if (typeof onUpdateShortlink === 'function') {
                onUpdateShortlink(url, shortlinkType, tagElement, shortener);
              }
              break;
            }
            case VISITOR:
            case CUSTOMER:
            case EVENT:
            case PROMOTION_CODE:
            case OBJECT_WIDGET:
            case CONTENT_SOURCE_GROUP:
            case JOURNEY:
            case CAMPAIGN:
            case VARIANT:
            case CUSTOM_FN:
            case ALLOCATED_CODE:
            case CUSTOM_TAG: {
              if (typeof onUpdatePersonalizationTag === 'function') {
                onUpdatePersonalizationTag(tagElement, tagData);
              }
              break;
            }
            default: {
              break;
            }
          }
        }
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'handleTagClick',
          data: {
            error: error.stack,
            tagDetail,
          },
        });
      }
    },
    [shouldEscapeHtml, showShortLinkWithBroadcast, onlyShowGeneralShortlink],
  );

  const handleChangeInputTag = useCallback(
    newInputText => {
      if (typeof onChange === 'function') {
        onChange(newInputText);
      }
    },
    [onChange],
  );

  return (
    <ErrorBoundary path={PATH}>
      <InputTag
        ref={inputTagRef}
        name={name}
        initialValue={initValue}
        placeholder={placeholder}
        isLineEmoji={isChannelLine}
        isViberEmoji={isChannelViber}
        readonly={isViewMode}
        readonlyTag={readonlyTag}
        escapeHTML={shouldEscapeHtml}
        maxHeight={maxHeight}
        enableShortLink={enableShortLink}
        status={statusMemoized}
        tagProperties={tagProperties}
        mapAttributes={mapAttributes}
        mapErrorAttributes={mapErrorAttributes}
        onTagRemove={handleTagRemove}
        onTagClick={handleTagClick}
        onChange={handleChangeInputTag}
      >
        <Flex align="center" style={{ height: MIN_H_WRAPPER }}>
          {useEmoji && (
            <EmojiPopover
              disabled={isViewMode}
              collections={emojiCollections}
              onEmojiClick={inputTagRef.current?.onAddNewTag}
            />
          )}

          <Personalizable
            disabled={disabled}
            dataState={dataState}
            moduleConfig={moduleConfig}
            showMergeTagDisplay={showMergeTagDisplay}
            isNewCustomTag={isNewCustomTag}
            useAllocatedCode={useAllocatedCode}
            ref={personalizableRef}
            isViewMode={isViewMode}
            tagProperties={tagProperties}
            mapAttributes={mapAttributes}
            enableShortLink={enableShortLink}
            variantExtraData={variantExtraData}
            listTemplateCustom={listTemplateCustom}
            hiddenDynamicOption={hiddenDynamicOptionMemo}
            showShortLinkWithBroadcast={showShortLinkWithBroadcast}
            onlyShowGeneralShortlink={onlyShowGeneralShortlink}
            onAddNewTag={inputTagRef.current?.onAddNewTag}
            onUpdateTag={inputTagRef.current?.onUpdateTag}
            onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
            isForceHideBtnPersonalization={isForceHideBtnPersonalization}
          />
        </Flex>
      </InputTag>
    </ErrorBoundary>
  );
}

InputPersonalizable.propTypes = {
  useEmoji: PropTypes.bool,
  disabled: PropTypes.bool,
  isViewMode: PropTypes.bool,
  name: PropTypes.string,
  errors: PropTypes.array,
  channelCode: PropTypes.string,
  initValue: PropTypes.string,
  placeholder: PropTypes.string,
  mapAttributes: PropTypes.object,
  mapErrorAttributes: PropTypes.object,
  listTemplateCustom: PropTypes.object,
  enableShortLink: PropTypes.bool,
  variantExtraData: PropTypes.object,
  isForceHideBtnPersonalization: PropTypes.bool,
  showShortLinkWithBroadcast: PropTypes.bool,
  onlyShowGeneralShortlink: PropTypes.bool,
  hiddenDynamicOption: PropTypes.array,
  readonlyTag: PropTypes.bool,
  dataState: PropTypes.object,
  shouldEscapeHtml: PropTypes.bool,
  maxHeight: PropTypes.number,
  showMergeTagDisplay: PropTypes.bool,
  useAllocatedCode: PropTypes.bool,
  isNewCustomTag: PropTypes.bool,
  moduleConfig: PropTypes.object,
  tagProperties: PropTypes.object,
  onChange: PropTypes.func,
  onTagRemove: PropTypes.func,
  onChangeExtraDataTagProperty: PropTypes.func,
};

InputPersonalizable.defaultProps = {
  useEmoji: false,
  channelCode: '',
  disabled: false,
  isViewMode: false,
  shouldEscapeHtml: true,
  name: '',
  initValue: '',
  mapAttributes: {},
  errors: [],
  // placeholder: 'Enter your text...',
  listTemplateCustom: { list: [], map: {} },
  isForceHideBtnPersonalization: false,
  showShortLinkWithBroadcast: false,
  onlyShowGeneralShortlink: false,
  readonlyTag: false,
  enableShortLink: false,
  variantExtraData: {},
  hiddenDynamicOption: [],
  moduleConfig: {},
  dataState: {},
  showMergeTagDisplay: true,
  useAllocatedCode: false,
  isNewCustomTag: false,
  onChange: () => {},
};

export default memo(InputPersonalizable);
