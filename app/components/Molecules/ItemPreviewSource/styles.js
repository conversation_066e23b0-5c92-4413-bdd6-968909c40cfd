// Libraries
import styled from 'styled-components';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

const { blue2, colorText } = globalToken;

export const MaterialChip = styled.div`
  position: relative;
  padding: 0 0.625rem 0 0.625rem;
  max-width: 200px;
  background-color: ${blue2};
  color: ${colorText};

  ${({ isError }) =>
    isError &&
    `background: #ed5240 !important;
    cursor: not-allowed !important;`}

  ${({ $readOnly }) => $readOnly && 'cursor: default !important;'}

  ${({ isBlastCampaign, isViewMode }) =>
    isBlastCampaign &&
    isViewMode &&
    'background-color: #fff !important; color: #000; border: 1px solid #8888; '}

  border-radius: 16px;
  display: flex;
  align-items: center;

  height: 24px;
  .content-chip {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    line-height: 24px;
  }

  > div.icon {
    display: none;
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translate(calc(50% - 13px), -50%);
    justify-content: center;
    align-items: center;
  }

  &:hover > div.icon {
    display: flex;
  }


  [class*='MuiSvgIcon'] {
    border: 0;
    cursor: pointer;
    outline: none;
    display: flex;
    font-size: 1rem;
  }
`;

export const Wrapper = styled.div`
  overflow-x: hidden;
  align-items: center;
  cursor: pointer;
  display: flex;
  min-width: 0;

  .chip-container {
    overflow: hidden;
    font-size: 0.75rem;
    font-family: Roboto-Bold;
  }
`;
