import TRANSLATE_KEY from '../../../../messages/constant';
import { getTranslateMessage } from '../../../../containers/Translate/util';
export const isShowConfig = type => {
  const arrType = ['string', 'number', 'datetime'];
  if (arrType.includes(type)) return true;
  return false;
};
export const isShowFormatEdit = dataType => {
  const TYPE = ['number', 'datetime'];
  if (TYPE.includes(dataType)) return true;
  return false;
};
export const isShowSuggestion = dataType => {
  const TYPE = ['string', 'number', 'boolean', 'array_string', 'array_number'];

  if (TYPE.includes(dataType)) return true;
  return false;
};
export const isShowDataTransformation = dataType => {
  const TYPE = ['string'];

  if (TYPE.includes(dataType)) return true;
  return false;
};

export const isShowObjectLink = (displayFormatType, dataType) => {
  const TYPE = [MAP_FORMAT.OBJECT_LINK.value];

  if (TYPE.includes(displayFormatType) && dataType === 'string') return true;
  return false;
};

export const MAP_FORMAT = {
  RAW_STRING: {
    name: 'RAW_STRING',
    label: getTranslateMessage(TRANSLATE_KEY._DISPLAY_RAW_STRING, 'Raw string'),
    value: 'RAW_STRING',
  },
  IMAGE: {
    name: 'IMAGE',
    label: getTranslateMessage(TRANSLATE_KEY._DISPLAY_IMAGE, 'Image'),
    value: 'IMAGE',
  },
  LINK: {
    name: 'LINK',
    label: getTranslateMessage(TRANSLATE_KEY._DISPLAY_LINK, 'Link'),
    value: 'LINK',
  },
  NUMBER: {
    name: 'NUMBER',
    label: 'Number',
    translateCode: TRANSLATE_KEY._DATA_TYPE_NUMBER,
    value: 'NUMBER',
  },
  PERCENTAGE: {
    name: 'PERCENTAGE',
    label: 'Percentage',
    translateCode: TRANSLATE_KEY._TITL_PERCENTAGE,
    value: 'PERCENTAGE',
  },
  CURRENCY: {
    name: 'CURRENCY',
    label: 'Currency',
    translateCode: TRANSLATE_KEY._TITL_CURRENCY,
    value: 'CURRENCY',
  },
  DATE_AND_TIME: {
    name: 'DATE_AND_TIME',
    label: getTranslateMessage(
      TRANSLATE_KEY._DISPLAY_DATE_AND_TIME,
      'Datetime',
    ),
    value: 'DATE_AND_TIME',
  },
  OBJECT_LINK: {
    name: 'OBJECT_LINK',
    label: getTranslateMessage(TRANSLATE_KEY._, 'Object link'),
    value: 'OBJECT_LINK',
  },
};

export const MAP_DISPLAY_FORMAT = {
  string: [
    MAP_FORMAT.RAW_STRING,
    MAP_FORMAT.IMAGE,
    MAP_FORMAT.LINK,
    MAP_FORMAT.OBJECT_LINK,
  ],
  number: [MAP_FORMAT.NUMBER, MAP_FORMAT.PERCENTAGE, MAP_FORMAT.CURRENCY],
  array_number: [MAP_FORMAT.NUMBER, MAP_FORMAT.PERCENTAGE, MAP_FORMAT.CURRENCY],
  datetime: [MAP_FORMAT.DATE_AND_TIME],
};
export const MAP_DISPLAY_FORMAT_CUSTOM = {
  ...MAP_DISPLAY_FORMAT,
  string: [MAP_FORMAT.RAW_STRING],
};
const MAP_INIT_FORMAT = {
  string: {
    type: 'RAW_STRING',
    config: {},
  },
  number: {
    type: 'NUMBER',
    config: {},
  },
  array_number: {
    type: 'NUMBER',
    config: {},
  },
  datetime: {
    type: 'DATE_AND_TIME',
    config: {},
  },
};

export const INIT_OBJECT_LINK = {
  type: 'OBJECT_LINK',
  config: {},
};

export const initDisplayFormat = dataType => {
  return MAP_INIT_FORMAT[dataType] || {};
};

export const initObjectLink = () => {
  return INIT_OBJECT_LINK;
};

export const initState = () => ({
  isLoading: true,
  displayAs: {
    name: 'displayAs',
    value: {},
    disabled: false,
    options: [],
  },
  isShowConfig: true,
  formatType: '',
  config: {},
  type: 'custom', // default || custom custom có thêm radio button, defalt cho portal
});
