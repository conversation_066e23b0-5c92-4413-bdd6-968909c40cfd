/* eslint-disable no-empty */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useCallback } from 'react';

import Grid from '@material-ui/core/Grid';
import {
  UILoading as Loading,
  // UIWrapperDisable as WrapperDisable,
  // UIIconButton as IconButton,
} from '@xlab-team/ui-components';

import HeaderCondition from '../HeaderCondition';
import ContainerCondition from '../GeneralContainerCondition';
import DataSources from './DataSources';
import Refine from './Refine/index';
import SelectEvent from './SelectEvent';

import { safeParse } from '../../../../utils/web/utils';
// import { STATUS_ITEM_CODE } from '../../../../utils/constants';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';

const labelPerfEvent = getTranslateMessage(
  TRANSLATE_KEY._TITL_PERFORM_EVENT,
  'Perform event',
);

const BlockConditionPerformEvent = props => {
  const { item, options, groupIndex, itemIndex, version } = props;

  const changeSource = (value, isCallback = true) => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };
    props.callback('PERF_EVENT_CONDITION_CHANGE_SOURCE', { version, data });
    if (isCallback) {
      props.callback('UPDATE_CONDITIONS', data);
    }
  };

  const deleteItem = useCallback(() => {
    props.callback('DELETE_ITEM', { groupIndex, itemIndex });
  }, []);

  const property = safeParse(props.item.get('property'), null);
  // const statusItemCode = safeParse(
  //   (props.item.get('property') || {}).statusItemCode,
  //   STATUS_ITEM_CODE.ACTIVE,
  // );
  // const divDisabled = statusItemCode !== STATUS_ITEM_CODE.ACTIVE;
  // console.log(item.toJS());

  return (
    <ContainerCondition>
      <Loading isLoading={props.isLoading} isWhite />
      <HeaderCondition label={labelPerfEvent} onClick={deleteItem} />
      {/* <WrapperDisable disabled={divDisabled}> */}
      <Grid container item xs={12} className="m-top-1 m-bottom-3">
        <SelectEvent
          callback={props.callback}
          options={options}
          item={item}
          property={property}
          moduleConfig={props.moduleConfig}
          groupIndex={groupIndex}
          itemIndex={itemIndex}
          disabledEvent={props.disabledEvent}
          isViewMode={props.isViewMode}
          componentId={props.componentId}
          // mapEventsTracking={dataEventsTracking.map}
        />
        <DataSources
          paramsFetchEvent={props.paramsFetchEvent}
          eventValue={property}
          sourcesSelected={item.get('dataSources')}
          error={item.get('errorDataSources')}
          isViewMode={props.isViewMode}
          isInitDataSources={
            item.get('isInitDataSources') ||
            (item.get('dataSources') && item.get('dataSources').length > 0)
          }
          onChange={changeSource}
          disabledEventConditions={props.disabledEventConditions}
          refineWithProperties={item.get('refineWithProperties')}
          isBlastCampaign={props.isBlastCampaign}
        />
        <Refine
          refineWithProperties={item.get('refineWithProperties')}
          moduleConfig={props.moduleConfig}
          groupIndex={groupIndex}
          itemIndex={itemIndex}
          eventValue={property}
          callback={props.callback}
          isViewMode={props.isViewMode}
          sourcesSelected={item.get('dataSources')}
          disabledEventConditions={
            props.disabledEventConditions ||
            (item.get('dataSources') && item.get('dataSources').length === 0)
          }
        />
      </Grid>
      {/* </WrapperDisable> */}
    </ContainerCondition>
  );
};

export default BlockConditionPerformEvent;
