/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
/* eslint-disable arrow-body-style */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
// Libraries
import dayjs from 'dayjs';
// import tw from 'twin.macro';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import PropTypes from 'prop-types';

import {
  TemplateListing,
  useTemplateListing,
  notification,
  usePersistTemplate,
  Tabs,
  Icon as AntsomiIcon,
  useGetSaveAsGalleryPermissionEmails,
} from '@antscorp/antsomi-ui';
import { OBJECT_TYPES, PUBLIC_LEVEL } from '@antscorp/antsomi-ui/es/constants';
import { useHistory } from 'react-router-dom';

// Hooks
// import { useExternalServiceAuth } from 'app/hooks/useExternalServiceAuth';

// Components
// import { CreateTemplateModal } from './components/CreateTemplateModal';

// Constants
import {
  LIMIT_PER_PAGE_DEFAULT,
  TEMPLATE_TAB_MENU_ITEMS,
  TEMPLATE_TAB_MENU_KEYS,
  DATE_TIME_FORMAT,
  POST_MESSAGE_TYPES,
  useExternalServiceAuth,
} from './constants';

// Constants
import JourneyServices from 'services/Journey';
// Utils
// import {
//   getMediaTemplateViewPageThumbnails,
//   initPreviewMediaTemplate,
// } from '../../utils';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { createStructuredSelector } from 'reselect';
import { connect, useSelector } from 'react-redux';
import {
  StyledUITabs,
  TemplateItem,
  TemplatesWrapper,
  UIModalStyled,
} from '../MediaTemplateEditor/styles';
import {
  TabPanel,
  UIButton,
  UIModalBody,
  UIModalFooter,
  UIModalHeader,
} from '@xlab-team/ui-components';
import { FormControlLabel, Icon, Radio, RadioGroup } from '@material-ui/core';
import {
  DEVICE_TEMPLATE,
  LAYOUT_TEMPLATE,
  TEMPLATE_PREVIEW_ID,
} from '../MediaTemplateEditor/constants';
import {
  Content,
  JourneyTacticWrapper,
  PreviewImageWrapper,
  Wrapper,
} from './styles';
import { DrawerUseJourneyTemplate } from '../../../../../containers/Drawer/DrawerUseJourneyTemplate/DrawerUseJourneyTemplate';
import { makeUrlPermisison } from '../../../../../utils/web/permission';
import { getJouneyUrl } from '../../../../../modules/Dashboard/MarketingHub/Journey/CreateTemplate/utils';
import { isEmpty, isFunction } from 'lodash';
import { makeSelectUser } from '../../../../../modules/Dashboard/selector';
import { initPreviewMediaTemplate } from '../MediaTemplateEditor/utils';
import { getVariantTemplate } from './utils';

const MAP_TITLE = {
  actEdit: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit'),
  actChangeTemplate: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Change Template',
  ),
  actUseTemplate: getTranslateMessage(
    TRANSLATE_KEY._TITL_USE_TEMPLATE,
    'USE TEMPLATE',
  ),
  actPreview: getTranslateMessage(TRANSLATE_KEY._ACT_PREVIEW_RESULT, 'Preview'),
  templateType: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Template type'),
  deviceType: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Device Type'),
  all: getTranslateMessage(TRANSLATE_KEY.TITL_ALL, 'All'),
  noTemplate: getTranslateMessage(TRANSLATE_KEY.DATLE, 'There is no template'),
  createNewTemplate: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Create New Template',
  ),
  noPermission: getTranslateMessage(
    TRANSLATE_KEY._TITL_NO_PERMISSION,
    "you don't have permission to perform this action",
  ),
  blankTemplate: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Blank Template'),
  cancel: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Cancel'),
  create: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Create'),
};

const BANNER_HEIGHT = '55vh';

// const Wrapper = styled.div`
//   ${tw`ants-h-full`}

//   .__container {
//     ${tw`ants-relative ants-flex ants-flex-col ants-w-full ants-h-full ants-bg-background`}

//     .__redirect_cell {
//       ${tw`ants-block ants-font-bold ants-truncate`}

//       text-decoration-line: none !important;
//     }
//   }
// `;
const JourneyTactic = props => {
  const { height, channelId, onApplyTemplate } = props;
  // Hooks
  const history = useHistory();
  const serviceAuth = useExternalServiceAuth();
  const [notificationApi, contextHolder] = notification.useNotification();
  const userInfo = useSelector(state => makeSelectUser(state));
  const {
    data: saveAsGalleryPermissionEmails,
    isLoading: isLoadingSaveAsGalleryPermissionEmails,
  } = useGetSaveAsGalleryPermissionEmails({
    args: {
      auth: serviceAuth,
    },
  });

  const [openDrawerTemplate, setOpenDrawerTemplate] = useState(false);

  // State
  const [state, setState] = useState({
    isOpenCreateModal: false,
    previewId: '',
    tabActive: TEMPLATE_TAB_MENU_KEYS.templateGallery,
    typesList: [],
    isLoadingTypesList: false,
    previewInfo: {
      url: '',
      deviceType: 1,
    },

    /* Preview Modal Props */
    isOpenPreviewModal: false,
    isMedia: false,
  });

  // Ref
  const thumbnailsRef = useRef();
  const previewRef = useRef();
  const previewingRef = useRef();

  // Memo
  const publicLevel = useMemo(() => {
    return TEMPLATE_TAB_MENU_ITEMS[state.tabActive].publicLevel;
  }, [state.tabActive]);

  const getListType = useMemo(() => {
    return TEMPLATE_TAB_MENU_ITEMS[state.tabActive].getListType;
  }, [state.tabActive]);

  const templateEditText = useMemo(() => {
    switch (state.tabActive) {
      case TEMPLATE_TAB_MENU_KEYS.templateGallery:
      case TEMPLATE_TAB_MENU_KEYS.sharedWithMe:
        return 'Create Campaign';
      case TEMPLATE_TAB_MENU_KEYS.myTemplate:
      default:
        return 'Create Campaign';
    }
  }, [state.tabActive]);

  const templateRemovable = useMemo(() => {
    if (isLoadingSaveAsGalleryPermissionEmails || !!channelId) return false;

    switch (state.tabActive) {
      case TEMPLATE_TAB_MENU_KEYS.templateGallery: {
        const isPermission = saveAsGalleryPermissionEmails.some(
          item => item === userInfo?.email,
        );

        return isPermission;
      }
      case TEMPLATE_TAB_MENU_KEYS.sharedWithMe:
        return false;
      case TEMPLATE_TAB_MENU_KEYS.myTemplate:
      default:
        return true;
    }
  }, [
    state.tabActive,
    userInfo?.email,
    saveAsGalleryPermissionEmails,
    channelId,
    isLoadingSaveAsGalleryPermissionEmails,
  ]);

  const {
    // Values
    categoryItems,
    templateItems,
    templateDetail,
    similarTemplates,
    checkedCategories,
    openCategoryKeys,
    previewTemplateCategories,
    selectTemplateAction,

    // Loading
    isLoadingCategoryList,
    isLoadingTemplateList,
    isLoadingTemplateDetail,

    // Functions
    onLoadMore: onLoadMoreTemplates,
    onChangeOpenCategoryKeys,
    onChangeCheckedCategories,
    onRemoveObjectTemplate,
    onSelectTemplate,
    setState: setTemplateState,
  } = useTemplateListing({
    serviceAuth,
    config: {
      getListType,
      objectType: OBJECT_TYPES.JOURNEY_TEMPLATE, // 1: Media Template
      publicLevel, // 1: Public, 0: Restricted
      limitListPerPage: LIMIT_PER_PAGE_DEFAULT,
      channel: channelId,
    },
  });

  const { model } = templateDetail || {};

  const thumbnails = useMemo(() => {
    if (!model) return [];

    const result = model.properties?.thumbnails || [];
    const variantTemplate = getVariantTemplate(
      model?.config?.journey?.workflow_setting,
    );

    result.forEach(thumbnail => {
      if (variantTemplate[thumbnail.variantId]) {
        thumbnail.templateSettings = variantTemplate[thumbnail.variantId];
      }
    });

    setState(prev => ({
      ...prev,
      previewInfo: {
        url: result[0].templateSettings
          ? ''
          : result.length
          ? result[0].url
          : '',
        deviceType: result[0].templateSettings?.deviceType,
      },
      isMedia: result[0].type === 'media',
    }));

    thumbnailsRef.current = result;

    return result;
  }, [model]);
  //   const pagePreviewThumbnails = useDeepCompareMemo(() => {
  //     return getMediaTemplateViewPageThumbnails(templateDetail);
  //   }, [templateDetail]);

  const { mutateAsync: persistTemplate } = usePersistTemplate({
    options: {
      onError() {
        notificationApi.error({
          message: 'test',
        });
      },
      onSuccess() {
        notificationApi.success({
          message: 'test',
        });
      },
    },
  });

  const togglePreviewModal = isOpen => {
    setState(prev => ({
      ...prev,
      isOpenPreviewModal:
        typeof isOpen === 'boolean' ? isOpen : !state.isOpenPreviewModal,
    }));
  };

  const handlePreviewTemplateSetting = (isClickThumbnail = false) => {
    if (!isClickThumbnail) {
      previewRef.current = thumbnailsRef.current.find(
        item => item.templateSettings,
      );
    }

    if (state.previewId) {
      previewTemplate({
        type: POST_MESSAGE_TYPES.PREVIEW_CDP_CAMPAIGN_WAITING,
        id: state.previewId,
      });
    } else {
      initPreviewMediaTemplate({
        zoneSelector: `#${TEMPLATE_PREVIEW_ID}`,
        isPreview: true,
      });
    }

    setState(prev => ({
      ...prev,
      isMedia: previewRef.current.type === 'media',
    }));
  };

  // Effects
  useEffect(() => {
    if (!state.isOpenPreviewModal) {
      previewRef.current = null;
      previewingRef.current = null;
    }
  }, [state.isOpenPreviewModal]);

  useEffect(() => {
    if (templateDetail) {
      switch (selectTemplateAction) {
        case 'edit': {
          switch (state.tabActive) {
            case TEMPLATE_TAB_MENU_KEYS.templateGallery:
            case TEMPLATE_TAB_MENU_KEYS.sharedWithMe: {
              handleCreateTemplate({
                ...model,
              });

              break;
            }
            case TEMPLATE_TAB_MENU_KEYS.myTemplate:
            default: {
              const { id, userId } = templateDetail;

              //   history.push(
              //     generatePath(ROUTES.MEDIA_TEMPLATE_DESIGN.path, {
              //       id,
              //       userId,
              //     }),
              //   );

              break;
            }
          }
          setTemplateState(prev => ({
            ...prev,
            selectedTemplateId: undefined,
            selectTemplateAction: undefined,
          }));
          togglePreviewModal(false);
          break;
        }
        case 'use': {
          setOpenDrawerTemplate(true);
          togglePreviewModal(false);

          break;
        }
        case 'preview':
        default: {
          if (!thumbnails.length || !thumbnails[0].templateSettings) {
            return;
          }
          handlePreviewTemplateSetting();

          break;
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    templateDetail,
    selectTemplateAction,
    state.tabActive,
    history,
    setTemplateState,
  ]);

  useEffect(() => {
    window.addEventListener('message', e => previewTemplate(e.data));

    return () => {
      window.removeEventListener('message', e => previewTemplate(e.data));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    setState(prev => ({ ...prev, isLoadingTypesList: true }));
    const fetchData = async () => {
      const res = await JourneyServices.mediaTemplate.getListType({
        data: {},
      });
      setState(prev => ({
        ...prev,
        typesList: res.data || [],
        isLoadingTypesList: false,
      }));
    };
    // const fetchVersion = async () => {
    //   const res = await JourneyServices.mediaTemplate.getVersion();
    //   setState(draft => {
    //     draft.version = res.data.version || '';
    //   });
    // };

    fetchData();
    // fetchVersion();
  }, []);
  const previewTemplate = ({ type, id }) => {
    if (type !== POST_MESSAGE_TYPES.PREVIEW_CDP_CAMPAIGN_WAITING) {
      return;
    }

    if (!state.previewId) {
      setState(prev => ({ ...prev, previewId: id }));
    }

    if (
      previewingRef.current &&
      previewingRef.current.variantId === previewRef.current.variantId
    ) {
      window.postMessage({
        messageType:
          POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN_VIEW_NAVIGATE,
        targetScript: state.previewId,
        viewId: previewRef.current.viewId || 'optin',
      });
      previewingRef.current = previewRef.current;
      return;
    }

    previewingRef.current = previewRef.current;
    const { templateSettings } = previewRef.current;

    setTimeout(() => {
      window.postMessage(
        {
          ...(templateSettings || thumbnailsRef.current[0].templateSettings),
          messageType: POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN,
          targetScript: id,
          viewId: previewRef.current.viewId || 'optin',
        },
        '*',
      );
    }, 100);
  };

  const handleApplyJT = data => {
    if (isEmpty(data) || !data.channel_id) return;

    const journeyUrl = getJouneyUrl(data.channel_id);
    // const blastUrl = makeUrlPermisison(`${journeyUrl}/blast-campaign/create`);
    // const baseUrl = makeUrlPermisison(`${journeyUrl}/create`);
    const blastUrl = makeUrlPermisison(
      `${journeyUrl}/list?useTactic=true&isBlastCampaign=true`,
    );
    const baseUrl = makeUrlPermisison(`${journeyUrl}/list?useTactic=true`);

    const redirectUrl = data.isBlastCampaign ? blastUrl : baseUrl;

    if (isFunction(onApplyTemplate)) {
      onApplyTemplate({
        storySettings: data,
        redirectUrl,
      });
    }
  };

  const handleCreateTemplate = async data => {
    const response = await persistTemplate({
      persistType: 'create',
      params: {
        // auth: serviceAuth,
        data: {
          ...data,
          template_name: `Untitled Project#${dayjs().format(
            DATE_TIME_FORMAT.TITLE_DATE_TIME,
          )}`,
          object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
          public_level: PUBLIC_LEVEL.RESTRICTED,
        },
      },
    });

    const { id, userId } = response;

    // history.push(
    //   generatePath(ROUTES.MEDIA_TEMPLATE_DESIGN.path, { id, userId }),
    // );
  };

  const toggleCreateTemplateModal = toggle => {
    setState(prev => ({
      ...prev,
      isOpenCreateModal:
        typeof toggle === 'boolean' ? toggle : !prev.isOpenCreateModal,
    }));
  };

  const handleClickAddTemplate = useCallback(() => {
    toggleCreateTemplateModal(true);
  }, []);

  const onClickPreviewThumbnail = useCallback(
    data => {
      const { url, templateSettings, type } = data;
      previewRef.current = data;

      if (templateSettings) {
        handlePreviewTemplateSetting(true);
      }
      setState(prev => ({
        ...prev,
        previewInfo: {
          url: templateSettings ? '' : url,
          deviceType: templateSettings?.deviceType,
        },
        isMedia: type === 'media',
      }));
    },
    [state.previewId, thumbnails],
  );

  // const onClickPreviewThumbnail = useCallback(
  //   ({ url }) => {
  //     // window.postMessage({
  //     //   messageType:
  //     //     POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN_VIEW_NAVIGATE,
  //     //   targetScript: state.previewId,
  //     //   viewId: id,
  //     // });
  //     setState(prev => ({
  //       ...prev,
  //       previewUrl: url,
  //     }));
  //   },
  //   [state.previewId, thumbnails],
  // );

  const handleChangeTab = value => {
    setState(prev => ({
      ...prev,
      tabActive: value,
    }));
  };
  const renderUITabs = () => {
    return (
      // <StyledUITabs
      //   height="57px"
      //   activeTab={state.tabActive}
      //   onChange={handleChangeTab}
      //   // classNameTabNav={`border-bottom p-x-3 ui-tabs ${classNameTabNav}`}
      //   tippy
      //   isBoxShadow={false}
      //   isBlastCampaign={props.isBlastCampaign}
      // >
      //   <TabPanel
      //     label={TEMPLATE_TAB_MENU_ITEMS.templateGallery.label}
      //     eventKey={TEMPLATE_TAB_MENU_ITEMS.templateGallery.key}
      //   />
      //   <TabPanel
      //     label={TEMPLATE_TAB_MENU_ITEMS.myTemplate.label}
      //     eventKey={TEMPLATE_TAB_MENU_ITEMS.myTemplate.key}
      //   />
      //   <TabPanel
      //     label={TEMPLATE_TAB_MENU_ITEMS.sharedWithMe.label}
      //     eventKey={TEMPLATE_TAB_MENU_ITEMS.sharedWithMe.key}
      //   />
      // </StyledUITabs>
      <Tabs
        activeKey={state.activeTab}
        items={Object.values(TEMPLATE_TAB_MENU_ITEMS)}
        onChange={key => {
          handleChangeTab(key);
          onChangeCheckedCategories({});
        }}
      />
    );
  };

  return (
    <div style={{ height: '100%' }}>
      <Wrapper top={props.top}>
        <div className="__container">
          {renderUITabs()}
          <Content height={height}>
            <TemplateListing
              templatesProps={{
                items: templateItems,
                onLoadMoreTemplates,
              }}
              categoryListingProps={{
                items: categoryItems,
                loading: isLoadingCategoryList,
                checkedCategories,
                openKeys: openCategoryKeys,
                onOpenChange: onChangeOpenCategoryKeys,
                onMenuChange: onChangeCheckedCategories,
              }}
              templateItemProps={{
                /**
                 * Check if thumbnail is media thumbnail then show skeleton
                 * NOTES: Just hot fix for now then has a flag to check thumbnail is media or not
                 */
                // showSkeleton: ({ thumbnail }) =>
                //   `${thumbnail || ''}`.includes('base64-img') &&
                //   ['jn_thumb_', 'v_thumb_'].every(
                //     prefix => !`${thumbnail || ''}`.includes(prefix),
                //   ),
                'data-test': 'template',
                showSkeleton: undefined,
                editBtnProps: {
                  text: templateEditText,
                  onClick: id => onSelectTemplate(id, 'use'),
                },
                previewBtnProps: {
                  onClick: id => {
                    togglePreviewModal(true);
                    onSelectTemplate(id, 'preview');
                  },
                },
                removable: templateRemovable,
                removeModalProps: {
                  onOk: onRemoveObjectTemplate,
                },
              }}
              // blankTemplateProps={{
              //   show: state.tabActive === TEMPLATE_TAB_MENU_KEYS.myTemplate,
              //   description: 'test',
              //   onClick: handleClickAddTemplate,
              // }}
              previewModalProps={{
                bannerProps: {
                  height: BANNER_HEIGHT,
                  deviceType:
                    state.previewInfo.deviceType === 2 ? 'mobile' : 'desktop',
                  showSkeleton:
                    state.isMedia ||
                    ((state.previewInfo.url || '').includes('base64-img') &&
                      ['jn_thumb_', 'v_thumb_'].every(
                        prefix =>
                          !(state.previewInfo.url || '').includes(prefix),
                      )),
                  children: state.previewInfo.url ? (
                    <PreviewImageWrapper>
                      <img src={state.previewInfo.url} alt="" />
                    </PreviewImageWrapper>
                  ) : (
                    <div
                      id={TEMPLATE_PREVIEW_ID}
                      className="ants-relative ants-h-full ants-w-full ants-overflow-auto"
                    />
                  ),
                },
                thumbnailProps: {
                  thumbnails,
                  onClickThumbnail: onClickPreviewThumbnail,
                },
                informationProps: {
                  itemName: templateDetail?.name,
                  categoryListing: previewTemplateCategories,
                  description: templateDetail?.description,
                  showDeviceRadios: false,
                  buttonText: templateEditText,

                  /* Click use/create template button */
                  onButtonClick: () =>
                    onSelectTemplate(templateDetail?.id || '', 'use'),
                },
                similarTemplateProps: {
                  similarTemplates,
                  similarCardProps: {
                    showSkeleton: undefined,
                    onClickWrapper: id => {
                      onSelectTemplate(id, 'preview');
                    },
                  },
                },
                loading: isLoadingTemplateDetail || false,
                open: state.isOpenPreviewModal,
                onCancel: () => togglePreviewModal(false),
                onOk: () => togglePreviewModal(false),
                // onCancel: onClosePreviewModal,
              }}
              emptyProps={{
                icon: <AntsomiIcon type="icon-ants-tactic" />,
                description: 'No tactic available',
              }}
            />
          </Content>
        </div>
        <UIModalStyled
          isOpen={state.isOpenCreateModal}
          toggle={toggleCreateTemplateModal}
          width={635}
        >
          <div
            className="wrapper-icon-close"
            onClick={toggleCreateTemplateModal}
          >
            <Icon>close</Icon>
          </div>
          <UIModalHeader>{MAP_TITLE.createNewTemplate}</UIModalHeader>
          <UIModalBody>
            <p className="label">{MAP_TITLE.deviceType}</p>
            <RadioGroup
              row
              value={state.selectedDevice}
              // onChange={e => onSelectDevice(e.target.value)}
              className="group-radio"
            >
              {Object.values(DEVICE_TEMPLATE).map(each => (
                <React.Fragment key={each.id}>
                  <FormControlLabel
                    value={each.id}
                    control={<Radio color="primary" size="small" />}
                    label={
                      <div className="radio-label">
                        {each.icon}
                        <p className="label">{each.label}</p>
                      </div>
                    }
                  />
                </React.Fragment>
              ))}
            </RadioGroup>
            <p className="label">{MAP_TITLE.templateType}</p>
            <TemplatesWrapper>
              {state.typesList.map(type => {
                const item = LAYOUT_TEMPLATE[type.template_code];
                const { id, label, img } = item;

                return (
                  <TemplateItem
                    key={id}
                    className={state.selectedType === id ? 'active' : ''}
                    // onClick={() => onSelectType(id)}
                  >
                    <img src={img} alt={label} />
                    <p className="label">{label}</p>
                  </TemplateItem>
                );
              })}
            </TemplatesWrapper>
          </UIModalBody>
          <UIModalFooter>
            <UIButton theme="outline" onClick={toggleCreateTemplateModal}>
              {MAP_TITLE.cancel}
            </UIButton>
            <UIButton theme="primary" onClick={toggleCreateTemplateModal}>
              {MAP_TITLE.create.toUpperCase()}
            </UIButton>
          </UIModalFooter>
        </UIModalStyled>
        {/* <CreateTemplateModal
          open={state.isOpenCreateModal}
          onCancel={() => toggleCreateTemplateModal(false)}
          onFinish={data => {
            const { deviceType, templateType } = data;

            toggleCreateTemplateModal(false);
            handleCreateTemplate({
              template_type: templateType?.id,
              device_type: deviceType,
              properties: {
                deviceType,
                template: {
                  id: templateType?.id,
                  type: templateType?.name,
                  name: templateType?.label,
                },
              },
              goal: [1],
            });
          }}
        /> */}
      </Wrapper>
      {contextHolder}

      <DrawerUseJourneyTemplate
        open={openDrawerTemplate}
        template={templateDetail}
        onApply={handleApplyJT}
        onClose={() => {
          setOpenDrawerTemplate(false);

          setTemplateState(prev => ({
            ...prev,
            selectedTemplateId: undefined,
            selectTemplateAction: undefined,
          }));
        }}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({});

const mapDispatchToProps = dispatch => ({});

JourneyTactic.propTypes = {
  variantExtraData: PropTypes.object,
  setIsFetchData: PropTypes.func,
  onChangeOthers: PropTypes.func,
  // eslint-disable-next-line react/no-unused-prop-types
  isViewMode: PropTypes.bool,
  componentKey: PropTypes.string,
  configure: PropTypes.object,
  creatingJourneyInfo: PropTypes.object,
};

JourneyTactic.defaultProps = {
  variantExtraData: {
    design: 'json-template',
    type: 'json-template',
    template_settings: {
      rulesets: [],
    },
    properties: {},
    fe_config_id: null,
    views: {},
  },
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(JourneyTactic);
