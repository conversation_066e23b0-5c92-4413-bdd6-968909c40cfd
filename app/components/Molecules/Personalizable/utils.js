/* eslint-disable no-param-reassign */
// Libraries
import { cloneDeep, isArray, get, set, has, isFunction, pick } from 'lodash';
import produce from 'immer';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Constants
import { INCLUDE_TYPE } from './constants';
import { CONDITION_TYPE } from '../ConditionAllocatedCode/constants';
import { DATA_GROUPS } from '../../common/UIEditorPersonalization/WrapperPersonalization/constants';

// Utils
import { addMessageToQueue } from '../../../utils/web/queue';
import {
  STATUS_CREATE,
  STATUS_UPDATE,
} from 'components/common/UIEditorPersonalization/utils';
import { TAG_TYPE } from '@antscorp/antsomi-ui';
import { randomV2, serializeLabelToCodeAttr } from '../../../utils/web/utils';

const {
  CUSTOMER,
  VISITOR,
  EVENT,
  JOURNEY,
  CAMPAIGN,
  VARIANT,
  PROMOTION_CODE,
  CUSTOM_FN,
  OBJECT_WIDGET,
  CONTENT_SOURCE_GROUP,
  ALLOCATED_CODE,
  CUSTOM_TAG,
} = TAG_TYPE;
const { COUNT_ALL_CODES, ALL_UNIQUE_CODES, COUNT_UNIQUE_CODE } = INCLUDE_TYPE;

const PATH = 'app/components/Molecules/Personalizable/utils.js';

export const getAllocatedIncludeTypeTooltip = type => {
  switch (type) {
    case COUNT_ALL_CODES: {
      return translate(
        translations._PERSONALIZE_PROCESS_CODE_COUNT_TOOLTIP,
        'Counts the total number of codes issued during the process, including duplicates.',
      );
    }
    case ALL_UNIQUE_CODES: {
      return translate(
        translations._PERSONALIZE_PROCESS_CODE_ALL_TOOLTIP,
        'Retrieves the full list of unique codes issued during the process.',
      );
    }
    case COUNT_UNIQUE_CODE: {
      return translate(
        translations._PERSONALIZE_PROCESS_CODE_COUNT_DISTINCT_TOOLTIP,
        'Counts only the unique codes issued in the process, excluding duplicates.',
      );
    }
    default: {
      return '';
    }
  }
};

export const buildFormatAttributeKey = (property = {}) => {
  const { type, attribute } = property;

  switch (type) {
    case VISITOR:
    case CUSTOMER:
    case JOURNEY:
    case CAMPAIGN:
    case VARIANT:
    case OBJECT_WIDGET: {
      return [type, attribute].join('.');
    }
    case EVENT: {
      const { bo } = property;
      if (bo) {
        return [type, bo, attribute].join('.');
      }

      return [type, attribute].join('.');
    }
    case PROMOTION_CODE: {
      const { poolCode } = property;
      return [type, poolCode, attribute].join('.');
    }
    case CONTENT_SOURCE_GROUP: {
      const { groupId, groupIndex } = property;
      const group = `${groupId}[${groupIndex}]`;

      return [type, group, attribute].join('.');
    }
    default: {
      return null;
    }
  }
};

const getContentSourceType = (selectedProperties = {}) => {
  let contentSourceType = get(selectedProperties, 'personalType.value', '');
  const contentSources = get(selectedProperties, 'contentSources', []);

  // Re-assign tagType if it's content source
  if (contentSources && contentSources.length > 0) {
    const csIdx = contentSources.findIndex(
      cs => cs.groupId === contentSourceType,
    );

    if (csIdx !== -1) {
      contentSourceType = CONTENT_SOURCE_GROUP;
    }
  }

  return contentSourceType;
};

const buildContentSourceGroupProperty = (
  selectedProperties,
  contentSourceType,
  attributeCode,
) => {
  const { value: groupId } = selectedProperties.personalType;
  return {
    groupId,
    type: contentSourceType,
    attribute: attributeCode,
    groupIndex: selectedProperties?.csIndexAttribute?.value,
    defaultValue: '',
  };
};

const buildPromotionCodeProperty = (
  selectedProperties,
  contentSourceType,
  attributeCode,
) => {
  const { promotionCodeAttr = {} } = selectedProperties;
  const { bracketed_code: promoAttribute } = promotionCodeAttr;

  return {
    type: contentSourceType,
    poolCode: attributeCode,
    attribute: promoAttribute,
  };
};

const buildEventProperty = (
  attribute,
  contentSourceType,
  attributeCode,
  defaultValue,
) => {
  if (attribute.type === 'event') {
    return {
      type: contentSourceType,
      attribute: attributeCode,
      bo: null,
      defaultValue,
    };
  }
  if (attribute.type === 'item') {
    return {
      type: contentSourceType,
      attribute: attribute.propertyName,
      bo: attribute.itemTypeName,
      defaultValue,
    };
  }
  return null;
};

const buildTagProperty = (selectedProperties = {}) => {
  try {
    const {
      displayName,
      value: defaultValue,
      attribute = {},
    } = selectedProperties;

    const contentSourceType = getContentSourceType(selectedProperties);

    const { bracketed_code: attributeCode } = attribute;

    switch (contentSourceType) {
      case VISITOR:
      case CUSTOMER:
      case JOURNEY:
      case CAMPAIGN:
      case VARIANT:
      case OBJECT_WIDGET: {
        return {
          type: contentSourceType,
          attribute: attributeCode,
          defaultValue,
        };
      }
      case EVENT: {
        return buildEventProperty(
          attribute,
          contentSourceType,
          attributeCode,
          defaultValue,
        );
      }
      case CUSTOM_FN: {
        return {
          type: contentSourceType,
          fnCode: serializeLabelToCodeAttr(displayName),
        };
      }
      case PROMOTION_CODE: {
        return buildPromotionCodeProperty(
          selectedProperties,
          contentSourceType,
          attributeCode,
        );
      }
      case ALLOCATED_CODE: {
        const allocatedCode = cloneDeep(
          get(selectedProperties, 'allocatedCode', {}),
        );

        return {
          ...allocatedCode,
          type: contentSourceType,
        };
      }
      case CONTENT_SOURCE_GROUP: {
        return buildContentSourceGroupProperty(
          selectedProperties,
          contentSourceType,
          attributeCode,
        );
      }
      default: {
        throw new Error('Invalid content source type');
      }
    }
  } catch (error) {
    handleTagBuildError(error, { selectedProperties });
    return {};
  }
};

export const generateTagId = () => randomV2(10);

const getPersonalizeTagId = ({ design, selectedProperties } = {}) => {
  if (design === STATUS_CREATE) {
    return generateTagId();
  }

  if (design === STATUS_UPDATE) {
    return get(selectedProperties, 'tagId', null);
  }

  return null;
};

const hasPersonalizeDefaultValue = type =>
  [CUSTOMER, VISITOR, EVENT, JOURNEY, CAMPAIGN, VARIANT].includes(type);

const buildMergeCodeOldVersion = (property = {}) => {
  const { type, defaultValue } = property;
  const hasDefault = hasPersonalizeDefaultValue(type);
  const prefix = buildFormatAttributeKey(property);

  if (hasDefault) {
    return `#{${prefix}||"${defaultValue || ''}"}`;
  }

  return `#{${prefix}}`;
};

export const buildMergeCode = ({ tagId } = {}) => {
  return `#{${CUSTOM_TAG}.${tagId}}`;
};

export const getTagRegex = (prefix = CUSTOM_TAG) =>
  new RegExp(`#\\{${prefix}\\.(\\w+)\\}`, 'g');

export function updateTags(
  traverseObj = {},
  tagProperties = {},
  nodeTagProperties = {},
) {
  const newTagProperties = {};
  const newNodeTagProperties = {};
  const allocatedTagIds = new Map();

  function traverseAndReplace(obj) {
    try {
      if (typeof obj === 'string') {
        return obj.replace(getTagRegex(), (_match, tagId) => {
          if (allocatedTagIds.has(tagId)) {
            return buildMergeCode({ tagId: allocatedTagIds.get(tagId) });
          }

          const newTagId = generateTagId();
          newTagProperties[newTagId] = tagProperties[tagId] ?? {};
          newNodeTagProperties[newTagId] = nodeTagProperties[tagId] ?? {};
          allocatedTagIds.set(tagId, newTagId);

          return buildMergeCode({ tagId: newTagId });
        });
      }

      if (Array.isArray(obj)) {
        return obj.map(traverseAndReplace);
      }

      if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
          Object.entries(obj).map(([key, value]) => [
            key,
            traverseAndReplace(value),
          ]),
        );
      }
      return obj;
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'traverseAndReplace',
        data: {
          error: error.stack,
          args: { obj, listTags: tagProperties, nodeTagProperties },
        },
      });
      return obj;
    }
  }

  const updatedObject = traverseAndReplace(cloneDeep(traverseObj));

  return { updatedObject, newTagProperties, newNodeTagProperties };
}

export const buildPersonalizeTagOldVersion = ({
  design = 'create',
  selectedProperties = {},
}) => {
  try {
    const tagId = getPersonalizeTagId({ design, selectedProperties });
    const label = get(selectedProperties, 'displayName', '');
    const property = buildTagProperty(selectedProperties);

    if (!property?.type) {
      throw new Error('Error building personalize tag property');
    }

    const mergeCode = buildMergeCodeOldVersion(property);

    return {
      tagId,
      property,
      tag: { label, type: property.type, value: mergeCode },
    };
  } catch (error) {
    handleTagBuildError(
      error,
      { design, selectedProperties },
      'buildPersonalizeTagOldVersion',
    );
    return { property: null, tag: null, tagId: null };
  }
};

const injectGlobalTagProperty = (property, selectedProperties) => {
  set(
    property,
    'isReplaceSpecialChars',
    selectedProperties.isReplaceSpecialChars,
  );
};

export const buildPersonalizeTag = ({
  design = 'create',
  selectedProperties = {},
} = {}) => {
  try {
    const tagId = getPersonalizeTagId({ design, selectedProperties });
    const label = get(selectedProperties, 'displayName', '');
    const property = buildTagProperty(selectedProperties);

    injectGlobalTagProperty(property, selectedProperties);

    if (!property?.type) {
      throw new Error('Error building personalize tag property');
    }

    const mergeCode = buildMergeCode({
      tagId,
    });

    return {
      tagId,
      property,
      tag: {
        key: tagId,
        label,
        type: CUSTOM_TAG, // Refer for all tags
        value: mergeCode,
        bgColorPersonalizeType: property.type,
      },
    };
  } catch (error) {
    handleTagBuildError(error, { design, selectedProperties });
    return { property: null, tag: null, tagId: null };
  }
};

const handleTagBuildError = (
  error,
  context,
  funcName = 'buildPersonalizeTag',
) => {
  addMessageToQueue({
    path: PATH,
    func: funcName,
    data: {
      error: error.stack,
      args: context,
    },
  });
};

const initializeDraftState = ({
  draft,
  originalDraft,
  tagElement,
  customTagId,
  personalType,
  displayName,
  tagProperties,
  status,
  statusMsg,
}) => {
  try {
    const extraTagProperty = get(tagProperties, customTagId, {});
    const formatKey = buildFormatAttributeKey(extraTagProperty);
    const displayFormat = get(
      originalDraft,
      ['formatAttributes', formatKey, 'attribute', 'settingsFe'],
      {
        dataType: '',
        format: {},
      },
    );

    draft.isOpen = true;
    draft.design = STATUS_UPDATE;
    draft.cacheTag = tagElement;
    draft.selectedProperties.personalType = pick(personalType, [
      'label',
      'value',
    ]);
    draft.selectedProperties.displayName = displayName;
    draft.selectedProperties.value = extraTagProperty?.defaultValue || '';
    draft.selectedProperties.tagId = customTagId;
    draft.selectedProperties.displayFormat = displayFormat;
    draft.selectedProperties.isReplaceSpecialChars = !!extraTagProperty?.isReplaceSpecialChars;
    draft.selectedProperties.status = status;
    draft.selectedProperties.statusMsg = statusMsg;
  } catch (error) {
    handleTagBuildError(
      error,
      {
        draft,
        originalDraft,
        tagElement,
        customTagId,
        personalType,
        displayName,
        tagProperties,
        status,
        statusMsg,
      },
      'initializeDraftState',
    );
  }
};

const updateStandardProperties = (
  draft,
  personalizeType,
  extraTagProperty,
  personalizationData,
) => {
  const attribute = get(
    personalizationData,
    [personalizeType, 'map', extraTagProperty?.attribute],
    {
      label: extraTagProperty?.label,
      value: extraTagProperty?.attribute,
    },
  );
  draft.selectedProperties.attribute = attribute;
};

const updateCustomFunction = (
  draft,
  originalDraft,
  extraTagProperty,
  callbackCustomFn,
) => {
  const { fnCode } = extraTagProperty;
  const newCustomFn = cloneDeep(
    originalDraft.extraData?.customFnAttribute[fnCode],
  );

  draft.selectedProperties.personalType = DATA_GROUPS[CUSTOM_FN];
  if (newCustomFn) {
    draft.selectedProperties.customFunction = newCustomFn;
    if (isFunction(callbackCustomFn)) {
      callbackCustomFn(newCustomFn);
    }
  }
};

const updateEventProperties = (
  draft,
  personalizeType,
  extraTagProperty,
  personalizationData,
) => {
  const attributeCode = [extraTagProperty?.attribute];
  if (extraTagProperty?.bo) {
    attributeCode.unshift(extraTagProperty.bo);
  }
  const eventAttrKey = attributeCode.join('.');
  const attribute = get(
    personalizationData,
    [personalizeType, 'map', eventAttrKey],
    {
      label: extraTagProperty?.label,
      value: eventAttrKey,
    },
  );
  draft.selectedProperties.attribute = attribute;
};

const updatePromotionCodeProperties = (
  draft,
  personalizeType,
  extraTagProperty,
  personalizationData,
  promotionCodeAttr,
) => {
  const attribute = get(
    personalizationData,
    [personalizeType, 'map', extraTagProperty?.poolCode],
    {
      label: extraTagProperty?.label,
      value: extraTagProperty?.poolCode,
    },
  );
  const itemPromoCodeAttr = get(promotionCodeAttr, [
    'map',
    extraTagProperty?.attribute,
  ]);

  draft.selectedProperties.attribute = attribute;
  draft.selectedProperties.promotionCodeAttr = itemPromoCodeAttr;
};

const updateAllocatedCodeProperties = (draft, extraTagProperty) => {
  draft.selectedProperties.personalType = DATA_GROUPS[ALLOCATED_CODE];
  draft.selectedProperties.allocatedCode = pick(extraTagProperty, [
    'conditions',
    'delimiter',
    'includeType',
  ]);
};

const updateContentSourceGroupProperties = (
  draft,
  extraTagProperty,
  personalizationType,
  contentSources,
) => {
  const { groupId, groupIndex } = extraTagProperty;
  draft.selectedProperties.personalType = get(
    personalizationType,
    ['map', groupId],
    {},
  );
  draft.selectedProperties.contentSources = contentSources;
  draft.selectedProperties.csIndexAttribute = {
    label: groupIndex,
    value: groupIndex,
  };
};

const updateDraftProperties = ({
  draft,
  originalDraft,
  tagProperties,
  customTagId,
  personalizeType,
  dataState,
  displayName,
  callbackCustomFn,
}) => {
  try {
    const {
      personalizationData = {},
      personalizationType = {},
      promotionCodeAttr = {},
      contentSources = [],
    } = dataState;

    const extraTagProperty = {
      ...get(tagProperties, customTagId, {}),
      label: displayName,
    };

    switch (personalizeType) {
      case VISITOR:
      case CUSTOMER:
      case JOURNEY:
      case CAMPAIGN:
      case VARIANT:
      case OBJECT_WIDGET:
        updateStandardProperties(
          draft,
          personalizeType,
          extraTagProperty,
          personalizationData,
        );
        break;
      case CUSTOM_FN:
        updateCustomFunction(
          draft,
          originalDraft,
          extraTagProperty,
          callbackCustomFn,
        );
        break;
      case EVENT:
        updateEventProperties(
          draft,
          personalizeType,
          extraTagProperty,
          personalizationData,
        );
        break;
      case PROMOTION_CODE:
        updatePromotionCodeProperties(
          draft,
          personalizeType,
          extraTagProperty,
          personalizationData,
          promotionCodeAttr,
        );
        break;
      case ALLOCATED_CODE:
        updateAllocatedCodeProperties(draft, extraTagProperty);
        break;
      case CONTENT_SOURCE_GROUP:
        updateContentSourceGroupProperties(
          draft,
          extraTagProperty,
          personalizationType,
          contentSources,
        );
        break;
      default:
        throw new Error('Invalid personalize type');
    }
  } catch (error) {
    handleTagBuildError(
      error,
      {
        draft,
        originalDraft,
        tagProperties,
        customTagId,
        personalizeType,
        dataState,
        callbackCustomFn,
      },
      'updateDraftProperties',
    );
  }
};

export const editTagState = ({
  tagElement,
  state = {},
  tagData = {},
  dataState = {},
  tagProperties = {},
  callbackCustomFn = () => {},
}) => {
  try {
    const { key: customTagId, status = '', statusMsg = '' } = tagData;
    const { type: personalizeType, displayName } =
      tagProperties?.[customTagId] || {};

    const personalType = get(
      dataState.personalizationType,
      ['map', personalizeType],
      {},
    );

    const editedState = produce(state, draft => {
      // init base
      initializeDraftState({
        draft,
        originalDraft: state,
        tagElement,
        customTagId,
        personalType,
        displayName,
        status,
        statusMsg,
        tagProperties,
      });

      updateDraftProperties({
        draft,
        originalDraft: state,
        customTagId,
        personalizeType,
        dataState,
        displayName,
        callbackCustomFn,
        tagProperties,
      });
    });

    return editedState;
  } catch (error) {
    handleTagBuildError(
      error,
      {
        state,
        tagData,
        tagElement,
        tagProperties,
        dataState,
        callbackCustomFn,
      },
      'editTagState',
    );
    return state;
  }
};

export const getRemoveFormatKey = ({ tagId, tagProperties, newFormatKey }) => {
  if (!tagId || !has(tagProperties, tagId)) return null;

  try {
    const { type, fnCode } = tagProperties[tagId];
    const tagEntries = Object.entries(tagProperties);

    if (type === CUSTOM_FN) {
      // No need to remove if same new formatKey
      if (newFormatKey === fnCode) return null;

      return isAttributeUsed(tagEntries, tagId, fnCode, true) ? null : fnCode;
    }

    const removeFormatKey = buildFormatAttributeKey(tagProperties[tagId]);

    // No need to remove if same new formatKey
    if (removeFormatKey === newFormatKey) return null;
    return isAttributeUsed(tagEntries, tagId, removeFormatKey, false)
      ? null
      : removeFormatKey;
  } catch (error) {
    handleTagBuildError(error, { tagId, tagProperties }, 'getRemoveFormatKey');
    return null;
  }
};

const isAttributeUsed = (tagEntries, tagId, key, isFnCode) => {
  return tagEntries.some(([id, tag]) => {
    // Skip current tag
    if (id === tagId) return false;

    if (isFnCode) {
      return tag?.type === CUSTOM_FN && tag?.fnCode === key;
    }
    return tag?.type !== CUSTOM_FN && buildFormatAttributeKey(tag) === key;
  });
};

const isFilledConditions = (conditions = []) =>
  conditions.every(condition => {
    if (!condition?.type || !condition.operator || !condition?.value)
      return false;

    if (condition.type === CONDITION_TYPE.EVENT) {
      return condition.value?.value && condition.value?.attribute?.value;
    }

    if (condition.type === CONDITION_TYPE.DYNAMIC_CODE) {
      return condition.value?.value && condition.value?.nodeId;
    }

    return true;
  });

export const isValidateAllocatedCode = (allocated = {}) => {
  if (
    !allocated?.delimiter ||
    !allocated.includeType ||
    !isArray(allocated.conditions)
  )
    return false;

  if (allocated.conditions.length > 0) {
    return isFilledConditions(allocated.conditions);
  }

  return true;
};
