/* eslint-disable indent */
/* eslint-disable dot-notation */
/* eslint-disable no-else-return */
/* eslint-disable no-undef */
/* eslint-disable camelcase */
import { Map, OrderedMap } from 'immutable';

import { validateItemCondition } from 'containers/Segment/Content/Condition/utils';
import { toArrayApiWithDataType } from '../../../services/map';
import { generateKey, safeParse } from '../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../utils/constants';
import {
  safeParseArrayNumber,
  safeParseArrayString,
} from '../../../utils/web/attribute';
import { initNumberWithoutDecimal } from '../../../utils/web/portalSetting';
import { getStatusItemCode } from '../../../utils/web/properties';
import { addMessageToQueue } from '../../../utils/web/queue';
import { toRefinePropertiesAPI } from './BlockConditionPerformEvent/Refine/utils';
import { MAP_SEGMENT_TYPES, MAP_SEGMENT_VALUES } from './constants';

export const toAPIPerformEvent = (conditions, options = {}) => {
  const { getCustomProperty = () => ({}) } = options;

  if (Object.keys(safeParse(conditions, {})).length === 0) {
    return {};
  }

  let rules = [];

  conditions.forEach(condition => {
    const isInit = condition.get('isInit');

    if (isInit === true) {
      rules = condition.get('backup');
    } else {
      condition.forEach(item => {
        const conditionType = safeParse(item.get('conditionType'), {});
        if (conditionType.value === MAP_SEGMENT_VALUES.perf_event) {
          const tempt = toConditionPerfEventAPI(item, {
            getCustomProperty,
          });

          if (tempt !== null) {
            rules.push(tempt);
          }
        }
      });
    }
  });

  return rules;
};

const toConditionPerfEventAPI = (item, options) => {
  const { getCustomProperty = () => ({}) } = options;

  // console.log('validateItemCondition(item) ===>', validateItemCondition(item));
  // console.log('validateItemCondition(item) ===>', item.toJS());
  const isInit = item.get('isInit');
  if (isInit === true) {
    return item.get('backup');
  }
  if (validateItemCondition(item)) {
    // const value = item.get('value');

    const property = item.get('property');
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    let tempt = {};
    // const objValue = buildValueConditionFromUI(item);

    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      // let insight_property_ids = [-1];
      let insight_property_ids = [];

      const dataSources = safeParse(item.get('dataSources'), []);

      if (Array.isArray(dataSources) && dataSources.length > 0) {
        insight_property_ids = safeParseArrayNumber(dataSources);
      }
      // console.log('property', property);
      tempt = {
        condition_type: item.get('conditionType').value,
        conditionType: 'event_attribute',
        eventCategoryId: property.eventCategoryId,
        eventActionId: property.eventActionId,
        eventTrackingName: property.eventTrackingName,
        // data_type: 'number',
        insightPropertyIds: insight_property_ids,
        eventKey: item.get('eventKey'),
        // operator: item.get('operator').value,
        // ...objValue,
        filters: toRefinePropertiesAPI(item.get('refineWithProperties')),
        // time_window: toTimeWindowAPI(item.get('timeWindow')),
        ...getCustomProperty(item),
      };

      if (item?.has('havingAttributes')) {
        tempt.havingAttributes = item.get('havingAttributes');
      }
      // console.log('tempt ===>', tempt);
    } else {
      tempt = item.get('backup');

      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt };
        return tempt;
      }
    }

    // const tempt = {
    //   condition_type: item.get('conditionType').value,
    //   event_category_id: property.eventCategoryId,
    //   event_action_id: property.eventActionId,
    //   data_type: 'number',
    //   operator: item.get('operator').value,
    //   ...objValue,
    //   refine_with_properties: toRefinePropertiesAPI(
    //     item.get('refineWithProperties'),
    //   ),
    //   time_window: toTimeWindowAPI(item.get('timeWindow')),
    // };

    return tempt;
  }
  return null;
};

export const getInputLookupFromRule = rules => {
  const output = {
    perf_event: [],
    comp_attr: [],
    data_sources: [],
    map_refine_with_properties: {},
  };
  const arrDataSource = [];

  //   const rulesAND = safeParse(ruleOR.AND, []);
  if (rules.condition_type === 'perf_event') {
    output.perf_event.push({
      eventCategoryId: rules.eventCategoryId,
      eventActionId: rules.eventActionId,
    });
    // output.map_refine_with_properties.push(
    //   ...getRefineWithProperties(
    //     safeParse(ruleAND.map_refine_with_properties, {}),
    //   ),
    // );
    output.map_refine_with_properties[
      `${rules.eventCategoryId}-${rules.eventActionId}`
    ] = getRefineWithProperties(safeParse(rules.filters, {}));
    arrDataSource.push(
      ...getDataSource(safeParse(rules.insightPropertyIds, [])),
    );
  } else if (rules.condition_type === MAP_SEGMENT_VALUES.comp_attr) {
    output.comp_attr.push({
      itemTypeId: rules.item_type_id,
      itemPropertyName: rules.property_name,
    });
  }
  output.data_sources = [...new Set(arrDataSource)];
  return output;
};

const getRefineWithProperties = refine => {
  const res = [];
  const ruleOR = safeParse(refine.OR, []);

  if (ruleOR.length === 0) {
    return res;
  }

  const ruleAND = safeParse(ruleOR[0], {});
  if (Object.keys(ruleAND).length === 0) {
    return res;
  }

  if (safeParse(ruleAND.AND, []).length > 0) {
    ruleAND.AND.forEach(item => {
      res.push({
        eventPropertyName: item.column,
        itemTypeId: item.item_type_id,
      });
    });
  }
  return res;
};

function getDataSource(inputs = []) {
  if (inputs.length === 0) {
    return [];
  }
  if (inputs.length === 1 && +inputs[0] === -1) {
    return [];
  }
  return inputs;
}

export function toUIPerformEvent(rules) {
  // console.log('toConditionUI data', data);
  // let conditions = OrderedMap({});
  // console.log('rules', rules);

  if (rules && rules.length === 0) {
    return OrderedMap({});
  }

  const condition = OrderedMap({
    'data-init': OrderedMap({
      backup: safeParse(rules, []),
      isInit: true,
    }),
  });
  return condition;
}

export function toComponentPerformEvent(objRules, data) {
  let conditions = OrderedMap({});

  if (Object.keys(objRules).length === 0) {
    return conditions;
  }

  let condition = OrderedMap({});
  if (objRules.condition_type === MAP_SEGMENT_VALUES.perf_event) {
    const tempt = toConditionPerfEventUI(
      objRules,
      MAP_SEGMENT_TYPES.perf_event,
      data.map.eventSchema,
      data.info.eventSchema,
    );

    if (tempt !== null) {
      condition = condition.set(generateKey(), tempt);
    }
  }
  if (condition.size > 0) {
    conditions = conditions.setIn([generateKey()], condition);
  }
  return condition;
}

// const initData = {
//   event: {
//     conditionType: 'event_attribute',
//     condition_type: 'perf_event',
//     eventActionId: -102,
//     eventCategoryId: -11,
//     eventTrackingName: 'view product',
//     filters: {
//       OR: [
//         {
//           AND: [
//             {
//               column: 'name',
//               dataType: 'string',
//               item_type_id: 1,
//               operator: 'contains',
//               type: 'item',
//               value: 'Ổ CỨNG DI ĐỘNG SEAGATE 1TB',
//             },
//             {
//               column: 'name',
//               dataType: 'string',
//               item_type_id: 1,
//               operator: 'contains',
//               type: 'item',
//               value: 'ASUS ZENFONE 2 LASER ZE500KG 16GB',
//             },
//           ],
//         },
//       ],
//     },
//     insightPropertyIds: [554926188],
//     value: '1',
//   },
// };

const toConditionPerfEventUI = (
  item,
  conditionType,
  mapEventSchema,
  mapInfoEventSchema = {},
) => {
  const keySchema = item.nodeId
    ? `${item.eventCategoryId}-${item.eventActionId}-${item.nodeId}-${
        item.eventKey
      }`
    : `${item.eventCategoryId}-${item.eventActionId}`;

  let property = mapEventSchema[keySchema];

  if (property === undefined) {
    property = mapInfoEventSchema[keySchema];
  }

  if (property !== undefined) {
    // const objValue = buildValueConditionFromAPI(item, property);
    const { conversion_window } = item;

    const tempt = Map({
      conditionType,
      property,
      statusItemCode: property.statusItemCode,
      dataSources: toRuleDataSourceUI(item.insightPropertyIds),
      isInitDataSources: true,
      dataType: 'number',
      refineWithProperties: OrderedMap({
        'data-init': OrderedMap({
          backup: safeParse(item.filters, { OR: [{ AND: [] }] }),
          isInit: true,
        }),
      }),
      isResetProperty: false,
      eventKey: item.eventKey,
      groupOrId: item.groupOrId || generateKey(),
      conversionWindow: {
        ...conversion_window,
      },
      backup: item,
      havingAttributes: item?.havingAttributes || {},
    });

    return tempt;
  }

  return null;
};

function toRuleDataSourceUI(dataSources = []) {
  if (Array.isArray(dataSources) === false) {
    return [];
  }
  if (dataSources.length === 1 && +dataSources[0] === -1) {
    return [];
  }
  return safeParseArrayString(dataSources);
}

export const toEventTrackingFE = (events, use = 'info') => {
  const validatedData = safeParse(events, []);
  const data = {
    list: [],
    map: {},
  };
  if (Array.isArray(validatedData) && validatedData.length === 0) {
    return data;
  }
  validatedData.forEach(event => {
    const dataType = 'number';
    const temp = {
      // label: event.translateLabel,
      label: event.label,
      // value: {
      //   eventCategoryId: event.eventCategoryId,
      //   eventActionId: event.eventActionId,
      // },
      statusItemCode: getStatusItemCode(use, parseInt(event.status)),
      value: event.nodeId
        ? `${event.eventCategoryId}-${event.eventActionId}-${event.nodeId}-${
            event.eventKey
          }`
        : `${event.eventCategoryId}-${event.eventActionId}`,
      eventCategoryId: event.eventCategoryId,
      eventActionId: event.eventActionId,
      eventTrackingCode: event.eventTrackingCode,
      eventKey: event?.eventKey,
      // value: `${event.eventCategoryId}-${event.eventActionId}`,
      // displayFormat: safeParseDisplayFormat(
      //   safeParse(event.displayFormat, null),
      //   { dataType },
      // ),
      displayFormat: initNumberWithoutDecimal(),
      dataType,
    };
    data.list.push(temp);
    data.map[temp.value] = temp;
  });
  return data;
};

export const deleteRefinesBySourceId = (dataRefine, sourceId) => {
  // const dataKeyRefine = [];
  let ruleRefine = OrderedMap({});

  if (Object.keys(safeParse(dataRefine, {})).length === 0) {
    return dataRefine;
  }

  dataRefine.forEach(item => {
    const property = item.get('property');

    if (property) {
      const { sources } = property;
      if (!(sources.length === 1 && sources.indexOf(parseInt(sourceId)) > -1)) {
        ruleRefine = ruleRefine.setIn([generateKey()], item);
      }
    }
  });
  // console.log('dataKeyRefine ===>', dataKeyRefine);
  // dataKeyRefine.forEach(key => {
  //   dataRefine.delete(key);
  // });

  return ruleRefine;
};

export function safeParsePeformEventData(data = OrderedMap({})) {
  try {
    let isInit = false;
    let backup = {};
    if (data.size > 0) {
      isInit = data.first().get('isInit');
      backup = data.first().get('backup');
    }
    if (isInit === true) {
      return {
        eventActionId: backup.eventActionId,
        eventCategoryId: backup.eventCategoryId,
        insightPropertyIds: backup.insightPropertyIds,
      };
    }

    const parent = data.first();
    const child = parent.first();
    const property = child.get('property');
    return {
      eventActionId: property.eventActionId,
      eventCategoryId: property.eventCategoryId,
      insightPropertyIds: toArrayApiWithDataType(
        child.get('dataSources'),
        'number',
      ),
    };
    // return data.get()toJS();
  } catch (err) {
    addMessageToQueue({
      path: 'app/components/common/UIPerformEvent/utils.js',
      func: 'safeParsePeformEventData',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.error(err);
  }
  return {};
}
export const getDataDefautEvenActionBased = (
  data,
  rules,
  hiddeOption = fasle,
  defaultOption,
) => {
  let dataOut = {};

  if (data && data.length > 0) {
    data.forEach(item => {
      if (defaultOption.includes(item.eventTrackingCode) && !hiddeOption) {
        dataOut = { ...item };
      } else if (hiddeOption) {
        const value = getValueToRules(rules);
        if (
          value.length === 0 &&
          defaultOption.includes(item.eventTrackingCode)
        ) {
          dataOut = { ...item };
        } else if (
          value.length > 0 &&
          !value.includes(item.eventTrackingCode)
        ) {
          dataOut = { ...item };
        }
      }
    });
  }

  return dataOut;
};
export const getValueToRules = rules => {
  const dataOut = [];
  rules.forEach(item => {
    item.forEach(element => {
      const property = safeParse(element.get('property'), null);
      if (property) {
        dataOut.push(property.eventTrackingCode);
      }
    });
  });
  return dataOut;
};
export const addRemoveElementArray = (dataBackup, data) => {
  const dataOut = [...dataBackup];
  data.forEach(each => {
    if (each) {
      const index = dataOut.findIndex(item => item.eventTrackingCode === each);
      if (index !== -1) {
        dataOut.splice(index, 1);
      } else {
        const items = dataOut.find(item => item.eventTrackingCode === each);
        dataOut.push(items);
      }
    }
  });
  return dataOut;
};

export const categorizeEvents = (allEvents = [], eventRules) => {
  if (!eventRules || eventRules.isEmpty()) return allEvents;

  const usedEventSet = new Set();

  eventRules.forEach(rule => {
    rule.forEach(item => {
      const eventValue = item.getIn(['property', 'value']);
      if (eventValue) usedEventSet.add(eventValue);
    });
  });

  const usedEvents = [];
  const unusedEvents = [];

  allEvents.forEach(event => {
    if (usedEventSet.has(event.value)) {
      usedEvents.push(event);
    } else {
      unusedEvents.push(event);
    }
  });

  return { unusedEvents, usedEvents };
};

export const checkExistGroupOrId = (rules, groupOrId) => {
  let isExist = false;

  // Iterate over the Immutable Map
  rules.forEach((value, key) => {
    if (Map.isMap(value) || OrderedMap.isOrderedMap(value)) {
      // If the value is another Map, recurse into it
      isExist = isExist || checkExistGroupOrId(value, groupOrId);
    } else if (key === 'groupOrId' && value === groupOrId) {
      // Match the groupOrId
      isExist = true;
    }
  });

  return isExist;
};

export const toEventTracking = (event, use = 'info') => {
  const dataType = 'number';
  const temp = {
    eventTrackingName: event.eventTrackingName,
    eventTrackingCode: event.eventTrackingCode,
    label: event.translateLabel,
    // label: event.eventTrackingName,
    // value: {
    //   eventCategoryId: event.eventCategoryId,
    //   eventActionId: event.eventActionId,
    // },
    statusItemCode: getStatusItemCode(use, parseInt(event.status)),
    value: `${event.eventCategoryId}-${event.eventActionId}-${event.nodeId}`,
    eventCategoryId: event.eventCategoryId,
    eventActionId: event.eventActionId,
    // value: `${event.eventCategoryId}-${event.eventActionId}`,
    // displayFormat: safeParseDisplayFormat(
    //   safeParse(event.displayFormat, null),
    //   { dataType },
    // ),
    displayFormat: initNumberWithoutDecimal(),
    dataType,
    available: event.available,
    ttl: parseInt(event.ttl),
  };
  return temp;
};
