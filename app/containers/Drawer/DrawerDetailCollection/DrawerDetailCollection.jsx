/* eslint-disable no-unused-expressions */
/* eslint-disable indent */

// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import propsTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  Button,
  DRAWER_DETAIL_DIMENSION,
  DrawerDetail,
} from '@antscorp/antsomi-ui';
import { useHistory } from 'react-router-dom';
import { isEmpty } from 'lodash';

// Components
import CollectionDetailModule from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/Loadable';
import CollectionVersionHistoryDetail from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/Detail/Loadable';
import UIHelmet from '../../../components/Templates/LayoutContent/Helmet';
import EditableContent from '../../../components/common/EditableContent';

// Styled
import { Drawer<PERSON>eader, WrapperBody } from './styled';

// Utils
import { getTranslateMessage } from '../../Translate/util';

// Constants
import { SUB_TAB, TAB } from '../DrawerDetailDataObject/constants';
import { DEFAULT_MENU_ITEMS } from './constants';
import { MODULE_CONFIG } from '../../../modules/Dashboard/ApiHub/BusinessObject/Main/config';
import TRANSLATE_KEY from '../../../messages/constant';
import { BUTTON_COLLECTION_SAVE_ID } from '../DrawerCreateCollection/constants';
import { MODULE_CONFIG as MODULE_CONFIG_COLLECTION } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Create/config';
import { MODULE_CONFIG as MODULE_CONFIG_COLLECTION_LIST } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/List/config';
import { MODULE_CONFIG as MODULE_CONFIG_COLLECTION_DETAIL } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/config';

import { DRAWER_NAME_CACHE } from '../../../utils/constants';

// Selectors
import {
  makeSelectIsOpenDrawerDetailCollection,
  makeSelectStatusRenameBOCollection,
} from '../../../modules/Dashboard/ApiHub/BusinessObject/Main/selectors';
import { makeSelectVersionDetail } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/Detail/selectors';
import { makeSelectConfigCreateSegment } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Create/selectors';
import {
  makeSelectActiveRow,
  makeSelectSegmentDetailDomainMain,
} from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/selectors';

// Actions
import { getList, updateValue } from '../../../redux/actions';

// Services
import BusinessObject from 'services/BusinessObject';

// Hooks
import { useUpdateObjectName } from '../../../hooks/useUpdateObjectName';
import { makeSelectPermission } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/selectors';

const MAP_TITLE = {
  restoreThisVersion: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Restore this version',
  ),
};
const DrawerDetailCollection = props => {
  const searchParams = new URLSearchParams(window.location.search);
  const [browserTitle, setBrowserTitle] = useState('');
  const history = useHistory();
  const {
    configCollection,
    dispatchChangeName,
    fetchData,
    updateStatusRenameCollection,
    isRenameCollectionSuccess,
    activeRowCollection,
    mainDetailCollection,
    dispatchChangeNameDetail,
    permissionBO,
  } = props;
  const {
    dataAPIS: { versionHistoryListing },
    checkPermision = {},
  } = props.versionDetail;

  const { isEdit = true } = checkPermision;

  const { tab, subTab, versionId, collectionId, itemTypeId } = useMemo(() => {
    return {
      tab: searchParams.get('tab'),
      subTab: searchParams.get('subTab'),
      versionId: searchParams.get('versionId'),
      collectionId: searchParams.get('id'),
      itemTypeId: searchParams.get('itemTypeId'),
    };
  }, [searchParams]);

  useEffect(() => {
    setBrowserTitle(activeRowCollection.segment_display);
  }, [activeRowCollection.segment_display]);

  const isShowHeader = !isEmpty(activeRowCollection);

  const renderContentDrawerSubLayer = () => {
    switch (tab) {
      case TAB.COLLECTION.key:
        if (subTab === SUB_TAB.VERSION_HISTORY.key && versionId) {
          return (
            <CollectionVersionHistoryDetail
              versionId={versionId}
              collectionId={collectionId}
              itemTypeId={itemTypeId}
              isUIV2
            />
          );
        }
        return (
          <CollectionDetailModule
            subTab={subTab}
            collectionId={collectionId}
            isUIV2
          />
        );

      default:
        return null;
    }
  };

  const onCloseDrawer = () => {
    if (isRenameCollectionSuccess) {
      updateStatusRenameCollection(false);
      fetchData();
    }

    searchParams.delete('subTab');
    searchParams.delete('versionId');
    searchParams.delete('id');

    props.changeDrawerDOInfo({ isOpenDrawerDetailCollection: false });
    history.push({ search: searchParams.toString() });
  };

  const onChangeTab = newTab => {
    if (newTab === subTab) return;
    searchParams.set('subTab', newTab);

    if (newTab === SUB_TAB.VERSION_HISTORY.key) {
      searchParams.set(
        'versionId',
        props.activeRowCollection.collection_version?.version || 1,
      );
    } else {
      searchParams.delete('versionId');
    }

    history.push({ search: searchParams.toString() });
  };

  const onRestore = () => {
    searchParams.set('versionId', props.versionDetail.versionId);
    searchParams.set('subTab', SUB_TAB.SETTINGS.key);

    history.push({ search: searchParams.toString() });
  };

  const {
    isLoading: isLoadingCollectionName,
    error: errorCollectionName,
    updateName: updateCollectionName,
    isSuccess: isSuccessCollectionName,
  } = useUpdateObjectName({
    serviceFunc: BusinessObject.BOCollection.updateName,
    options: {},
  });

  useEffect(() => {
    if (isSuccessCollectionName) {
      updateStatusRenameCollection(true);
      setBrowserTitle(configCollection?.main?.name);
    }
  }, [isSuccessCollectionName]);

  const handleBlurChangeName = () => {
    updateCollectionName({
      segmentId: collectionId,
      data: {
        segmentDisplay:
          subTab === SUB_TAB.ITEMS.key
            ? mainDetailCollection.name
            : configCollection.main.name,
        itemTypeId: +itemTypeId,
      },
    });
  };

  return (
    <DrawerDetail
      name={DRAWER_NAME_CACHE.COLLECTION_DETAIL}
      open={props.isOpenDrawerDetailCollection}
      maxWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
      onClose={onCloseDrawer}
      destroyOnClose
      menuProps={{
        items: isShowHeader ? DEFAULT_MENU_ITEMS : [],
        selectedKeys: [subTab],
        onClick: item => onChangeTab(item.key),
      }}
      headerProps={{
        showBorderBottom: true,
        height: '50px',
        style: {
          padding: '0 15px',
        },
        children: (
          <>
            {isShowHeader && (
              <DrawerHeader>
                <div className="left-content">
                  <EditableContent
                    defaultValue={
                      subTab === SUB_TAB.ITEMS.key
                        ? mainDetailCollection.name
                        : configCollection?.main?.name
                    }
                    required
                    onChange={name => {
                      subTab === SUB_TAB.ITEMS.key
                        ? dispatchChangeNameDetail(name)
                        : dispatchChangeName(name);
                    }}
                    onBlur={handleBlurChangeName}
                    isLoading={isLoadingCollectionName}
                    error={errorCollectionName}
                    readonly={!permissionBO.isEdit}
                  />
                </div>
                <div id={BUTTON_COLLECTION_SAVE_ID}>
                  {subTab === SUB_TAB.VERSION_HISTORY.key &&
                    versionId &&
                    isEdit && (
                      <Button
                        disabled={
                          +versionHistoryListing.data[0]?.version === +versionId
                        }
                        onClick={onRestore}
                        type="primary"
                      >
                        {MAP_TITLE.restoreThisVersion}
                      </Button>
                    )}
                </div>
              </DrawerHeader>
            )}
          </>
        ),
      }}
    >
      <UIHelmet title={browserTitle} />
      <WrapperBody>{renderContentDrawerSubLayer()}</WrapperBody>
    </DrawerDetail>
  );
};

const mapStateToProps = createStructuredSelector({
  permissionBO: makeSelectPermission(),
  isOpenDrawerDetailCollection: makeSelectIsOpenDrawerDetailCollection(),
  versionDetail: makeSelectVersionDetail(),
  activeRowCollection: makeSelectActiveRow(),
  configCollection: makeSelectConfigCreateSegment(),
  isRenameCollectionSuccess: makeSelectStatusRenameBOCollection(),
  mainDetailCollection: makeSelectSegmentDetailDomainMain(),
});

const mapDispatchToProps = dispatch => {
  return {
    changeDrawerDOInfo: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CHANGE_DRAWER_DO_INFO@@`, params),
      );
    },
    dispatchChangeName: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG_COLLECTION.key}@@SEGMENT_NAME@@`, value),
      ),
    dispatchChangeNameDetail: value =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG_COLLECTION_DETAIL.key}@@SEGMENT_NAME@@`,
          value,
        ),
      ),
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG_COLLECTION_LIST.key, params));
    },
    updateStatusRenameCollection: status =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@UPDATE_STATUS_RENAME_BO_COLLECTION@@`,
          status,
        ),
      ),
  };
};

DrawerDetailCollection.propTypes = {
  isOpenDrawerDetailCollection: propsTypes.bool,
  isRenameCollectionSuccess: propsTypes.bool,
  changeDrawerDOInfo: propsTypes.func,
  versionDetail: propsTypes.object,
  activeRowCollection: propsTypes.object,
  configCollection: propsTypes.object,
  dispatchChangeName: propsTypes.func,
  fetchData: propsTypes.func,
  updateStatusRenameCollection: propsTypes.func,
  mainDetailCollection: propsTypes.func,
  dispatchChangeNameDetail: propsTypes.func,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(DrawerDetailCollection);
