/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
// Libraries
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useImmer } from 'use-immer';
import { original } from 'immer';
import {
  cloneDeep,
  isArray,
  isFunction,
  isMap,
  isObject,
  isString,
} from 'lodash';
import PropTypes from 'prop-types';

// Services
import JourneyServices from 'services/Journey';

// Hooks
import { useIsMounted } from '../../../hooks';

// Locales
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Styled
import ErrorBoundary from 'components/common/ErrorBoundary';
import { BoxPairData, WrapperFields } from './styled';
import {
  Button,
  Col,
  Flex,
  PopoverSelect,
  Row,
  Scrollbars,
  Spin,
  Typography,
  useDeepCompareEffect,
} from '@antscorp/antsomi-ui';
import { DynamicFields } from './_UI';
import { AddIcon, CloseIcon } from '@antscorp/antsomi-ui/es/components/icons';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { DIMENSIONS, HEIGHT_CONFIG } from './constants';

// Utils
import { addMessageToQueue } from '../../../utils/web/queue';
import {
  initialCustomFields,
  serializeFieldToFE,
  stringifyDataToAPI,
} from './utils';
import { random } from '../UIEditorPersonalization/utils.3rd';

const {
  LABEL_W,
  LABEL_H,
  ADD_BTN_H,
  OFFSET_M,
  FIELD_FLEX_GAP,
  REMOVE_BTN_MAX_W,
} = DIMENSIONS;
const { Text } = Typography;

const { bw0, bw7, colorIcon, red8 } = globalToken;

const MAP_TRANSLATE = {
  label: getTranslateMessage(TRANSLATE_KEY._CONTENT_TABL_KEY, 'Label'),
  content: getTranslateMessage(TRANSLATE_KEY._CONTENT_TABL_VALUE, 'Content'),
  addRow: getTranslateMessage(TRANSLATE_KEY._ZALO_BUTTON_ADD_ROW, 'Add row'),
  unknownLabel: getTranslateMessage(
    TRANSLATE_KEY.FIELD_REMOVE_BY_3RD_LABEL,
    'Unknown',
  ),
};

const PATH = 'app/components/common/CustomFields/CustomFields.jsx';

function CustomFields(props) {
  // Props
  const {
    initValue,
    errors,
    label,
    isViewMode,
    isRequired,
    destinationId,
    groupCodes,
    enableShortLink,
    componentKey,
    isForceHideBtnPersonalization,
    useAllocatedCode,
    journeyNodeTagProperties,
    isNewCustomTag,
    moduleConfig,
    otherData,
    onTagRemove,
    onChangeExtraDataTagProperty,
    onChange,
    onChangeOthers,
  } = props;

  // State
  const [dataOutState, setDataOutState] = useImmer(
    initialCustomFields(initValue),
  );
  const [inputConfigState, setInputConfigState] = useImmer({
    isFetchFieldInfoDone: false,
    isResetDataOutState: false,
    fieldConfig: new Map(),
  });
  const [refresh, setRefresh] = useState(random(5));
  const { selectedKeys, mapKeys } = dataOutState;
  const {
    isFetchFieldInfoDone,
    fieldConfig,
    isResetDataOutState,
  } = inputConfigState;

  const checkIsMounted = useIsMounted();
  // console.count(' ----- render ---------');
  // console.log('dataOutState :>', dataOutState);

  // Memoized
  const addRowOpts = useMemo(() => Array.from(fieldConfig.values()), [
    fieldConfig,
  ]);

  const isShowBtnAdd = useMemo(() => {
    if (isViewMode) return false;

    return (
      selectedKeys.length === 0 || selectedKeys.length !== addRowOpts.length
    );
  }, [selectedKeys, addRowOpts, isViewMode]);

  const autoHeightMax = useMemo(() => {
    const deductions = [
      errors[0] && HEIGHT_CONFIG.error,
      isShowBtnAdd && HEIGHT_CONFIG.button,
    ].filter(Boolean);

    const totalHeight =
      HEIGHT_CONFIG.base - deductions.reduce((sum, val) => sum + val, 0);

    return `${totalHeight}px`;
  }, [isShowBtnAdd, errors]);

  const handleChangeDataOut = useCallback(
    newDataOut => {
      try {
        if (isObject(newDataOut) && isFunction(onChange)) {
          const stringifiedData = stringifyDataToAPI(newDataOut);

          onChange(stringifiedData);
        }
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'handleChangeDataOut',
          data: {
            error: error.stack,
            newDataOut,
          },
        });
      }
    },
    [onChange],
  );

  const handleApplyAddRows = useCallback(
    selectedKeyList => {
      if (isArray(selectedKeyList)) {
        setDataOutState(draft => {
          draft.selectedKeys = selectedKeyList;

          selectedKeyList.forEach(selectedKey => {
            if (!draft.mapKeys.has(selectedKey)) {
              draft.mapKeys.set(selectedKey, ''); // Initial selected key if not exist in map
            }
          });

          const mapKeysClone = cloneDeep(draft.mapKeys);

          handleChangeDataOut({
            selectedKeys: selectedKeyList,
            mapKeys: Object.fromEntries(mapKeysClone),
          });
        });
      }
    },
    [handleChangeDataOut],
  );

  const handleRemoveRow = useCallback(
    deleteKey => {
      setDataOutState(draft => {
        if (draft.mapKeys.has(deleteKey)) {
          draft.mapKeys.delete(deleteKey);

          const newSelectedKeys = draft.selectedKeys.filter(
            selectedKey => selectedKey !== deleteKey,
          );
          draft.selectedKeys = newSelectedKeys;

          const mapKeysClone = cloneDeep(draft.mapKeys);

          handleChangeDataOut({
            selectedKeys: newSelectedKeys,
            mapKeys: Object.fromEntries(mapKeysClone),
          });
        }
      });
    },
    [handleChangeDataOut],
  );

  const handleDynamicFieldChange = useCallback(
    newFieldChange => {
      try {
        if (isObject(newFieldChange)) {
          const { changeKey, value: newValue } = newFieldChange;

          setDataOutState(draft => {
            if (draft.mapKeys.has(changeKey)) {
              draft.mapKeys.set(changeKey, newValue);

              // Change data out
              const mapKeysClone = cloneDeep(draft.mapKeys);

              handleChangeDataOut({
                selectedKeys: original(draft.selectedKeys),
                mapKeys: Object.fromEntries(mapKeysClone),
              });
            }
          });
        }
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'handleDynamicFieldChange',
          data: {
            error: error.stack,
            args: { newFieldChange },
          },
        });
      }
    },
    [handleChangeDataOut],
  );

  useEffect(() => {
    const isMounted = checkIsMounted();

    // If initial value is empty, need to re-initial
    if (isMounted && componentKey && !initValue) {
      setDataOutState(() => initialCustomFields());

      return () => {
        const initState = initialCustomFields();
        setDataOutState(() => initState);
      };
    }
  }, [componentKey, initValue]);

  useDeepCompareEffect(() => {
    const isMounted = checkIsMounted();

    if (isString(initValue) && initValue.trim().length && isMounted) {
      const reNewState = initialCustomFields(initValue);

      setDataOutState(() => reNewState);
      // Need to refresh to update componentFieldKey to re-init life cycle of the input tag
      setRefresh(random(5));

      return () => {
        const initState = initialCustomFields();
        setDataOutState(() => initState);
      };
    }
  }, [initValue]);

  // Reset data when change destination
  useDeepCompareEffect(() => {
    if (isResetDataOutState) {
      // Reset flag
      setInputConfigState(draft => {
        draft.isResetDataOutState = false;
      });

      const initialState = initialCustomFields();

      // Reset local state data
      setDataOutState(() => initialState);

      // Call data to parent
      handleChangeDataOut(initialState);
    }
  }, [isResetDataOutState, handleChangeDataOut]);

  useEffect(() => {
    if (destinationId) {
      let ignore = false;

      const getCustomFieldList = async () => {
        try {
          setInputConfigState(draft => {
            draft.isFetchFieldInfoDone = false;
          });

          const payloads = {
            destinationId,
            body: {
              objectType: 'tickets',
              destinationId,
            },
          };

          const response = await JourneyServices.variant.getCustomFieldListCaresoft(
            payloads,
          );

          if (response?.code === 200 && isArray(response?.data)) {
            const serializedResponse = serializeFieldToFE(response.data);

            if (isMap(serializedResponse) && !ignore) {
              setInputConfigState(draft => {
                const originalInputConfig = original(draft);

                if (originalInputConfig.fieldConfig?.size > 0) {
                  draft.isResetDataOutState = true;
                }

                draft.fieldConfig = serializedResponse;
              });
            }
          }
        } catch (error) {
          addMessageToQueue({
            path: PATH,
            func: 'getCustomFieldList',
            data: {
              error: error.stack,
            },
          });
        } finally {
          setInputConfigState(draft => {
            draft.isFetchFieldInfoDone = true;
          });
        }
      };

      getCustomFieldList();

      return () => {
        ignore = true;
      };
    }
  }, [destinationId]);

  const renderButtonAddRow = useCallback(() => {
    const isDisabledAdd = !isFetchFieldInfoDone || addRowOpts.length === 0;

    return (
      <Row style={{ height: ADD_BTN_H, marginTop: OFFSET_M }}>
        <Col span={24}>
          <PopoverSelect
            selected={selectedKeys}
            options={addRowOpts}
            onApply={handleApplyAddRows}
          >
            <Button disabled={isDisabledAdd} type="text" icon={<AddIcon />}>
              {MAP_TRANSLATE.addRow}
            </Button>
          </PopoverSelect>
        </Col>
      </Row>
    );
  }, [addRowOpts, selectedKeys, isFetchFieldInfoDone, handleApplyAddRows]);

  const renderDynamicFields = () => {
    const content = selectedKeys.map(selectedKey => {
      const componentInfo = fieldConfig.get(selectedKey);

      const inputValue = mapKeys.get(selectedKey);
      const rowKey = `${selectedKey}-${destinationId}`;
      const componentFieldKey = `${refresh}-${rowKey}`;

      return (
        <Row
          key={rowKey}
          wrap={false}
          align="middle"
          style={{ marginTop: OFFSET_M }}
        >
          <Col flex={`${LABEL_W}px`}>
            <Text
              style={{
                maxWidth: LABEL_W,
                height: LABEL_H,
                lineHeight: `${LABEL_H}px`,
              }}
              ellipsis={{ tooltip: true }}
            >
              {componentInfo?.label || MAP_TRANSLATE.unknownLabel}
            </Text>
          </Col>
          <Col flex={`1 1 calc(100% - ${LABEL_W}px)`}>
            <Flex justify="space-between" gap={FIELD_FLEX_GAP}>
              <DynamicFields
                name={selectedKey}
                componentKey={componentFieldKey}
                useAllocatedCode={useAllocatedCode}
                isNewCustomTag={isNewCustomTag}
                journeyNodeTagProperties={journeyNodeTagProperties}
                type={componentInfo?.type}
                options={componentInfo?.options}
                groupCodes={groupCodes}
                mapOptions={componentInfo?.mapOptions}
                moduleConfig={moduleConfig}
                isViewMode={isViewMode}
                enableShortLink={enableShortLink}
                isForceHideBtnPersonalization={isForceHideBtnPersonalization}
                otherData={otherData}
                value={inputValue}
                onTagRemove={onTagRemove}
                onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
                onChange={handleDynamicFieldChange}
                onChangeOthers={onChangeOthers}
              />
              {!isViewMode && (
                <Button
                  type="text"
                  icon={<CloseIcon />}
                  style={{ marginRight: 5, maxWidth: REMOVE_BTN_MAX_W }}
                  onClick={() => handleRemoveRow(selectedKey)}
                />
              )}
            </Flex>
          </Col>
        </Row>
      );
    });

    return (
      <Scrollbars autoHeight autoHeightMax={autoHeightMax}>
        {content}
      </Scrollbars>
    );
  };

  return (
    <ErrorBoundary path={PATH}>
      <WrapperFields>
        <Text style={{ color: colorIcon }}>
          {label}
          {isRequired && !isViewMode && (
            <span
              style={{
                color: red8,
                display: 'inline-block',
                textIndent: '5px',
              }}
            >
              *
            </span>
          )}
        </Text>
        <BoxPairData $isError={!!errors[0]} $isViewMode={isViewMode}>
          <Spin spinning={!isFetchFieldInfoDone} style={{ background: bw0 }}>
            <Row gutter={OFFSET_M}>
              <Col flex={`${LABEL_W}px`}>
                <Text style={{ color: bw7 }}>{MAP_TRANSLATE.label}</Text>
              </Col>
              <Col flex={`1 1 calc(100% - ${LABEL_W}px)`}>
                <Text style={{ color: bw7 }}>{MAP_TRANSLATE.content}</Text>
              </Col>
            </Row>

            {renderDynamicFields()}

            {!!errors[0] && (
              <Row style={{ height: LABEL_H, marginTop: OFFSET_M }}>
                <Col span={24}>
                  <Text type="danger">{errors[0]}</Text>
                </Col>
              </Row>
            )}

            {isShowBtnAdd && renderButtonAddRow()}
          </Spin>
        </BoxPairData>
      </WrapperFields>
    </ErrorBoundary>
  );
}

CustomFields.propTypes = {
  destinationId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    .isRequired,
  initValue: PropTypes.string,
  componentKey: PropTypes.string,
  errors: PropTypes.array,
  groupCodes: PropTypes.array,
  isViewMode: PropTypes.bool,
  enableShortLink: PropTypes.bool,
  isRequired: PropTypes.bool,
  label: PropTypes.string,
  useAllocatedCode: PropTypes.bool,
  isNewCustomTag: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  journeyNodeTagProperties: PropTypes.object,
  moduleConfig: PropTypes.object,
  otherData: PropTypes.object,
  onTagRemove: PropTypes.func,
  onChangeExtraDataTagProperty: PropTypes.func,
  onChange: PropTypes.func,
  onChangeOthers: PropTypes.func,
};
CustomFields.defaultProps = {
  componentKey: '',
  initValue: '',
  isForceHideBtnPersonalization: false,
  errors: [],
  groupCodes: ['visitor', 'customer', 'event', 'story', 'campaign', 'variant'],
  isViewMode: false,
  useAllocatedCode: false,
  moduleConfig: {},
  isNewCustomTag: true,
  isRequired: false,
  label: 'Additional Content',
  otherData: {},
  onChange: () => {},
  onChangeOthers: () => {},
};
export default memo(CustomFields);
