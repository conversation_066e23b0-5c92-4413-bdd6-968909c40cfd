/* eslint-disable react/prop-types */
/* eslint-disable indent */
/* eslint-disable no-shadow */
// Libraries
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Chip } from '@material-ui/core';
import {
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { Flex, Icon, Spin } from '@antscorp/antsomi-ui';

// Components
import SelectTree from 'components/form/UISelectCondition';
import ConditionError from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import ConditionValue from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionValue';
import HeaderCondition from 'containers/Segment/Content/Condition/_UI/HeaderCondition';

// Styled
import { StyleWrapperInputValue } from 'containers/Segment/Content/Condition/_UI/styled';
import {
  ButtonWithIcon,
  ItemConditionWrapper,
  SelectedConditionTextMode,
  SubContentTooltip,
  VisibleWrapper,
} from '../../styled';
import { LabelAnd, WrapperCondition } from './styled';

// Utils
import { formatValueItemByDataType } from '../../../../../containers/Filters/ItemRule/Preview/utils';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { safeParse } from '../../../../../utils/common';
import {
  filterDeep,
  makeArrayToLabelFilter,
} from '../../../../../utils/web/utils';
import {
  getLabelFull,
  getLabelMulti,
} from '../../../../form/AutoSuggestion/utils';

// Selectors
import { makeSelectConfigureRulesFlow } from '../../../../../modules/Dashboard/MarketingHub/Journey/Detail/Dashdoard/FlowChart/selectors';

// Constants
import TRANSLATE_KEY from '../../../../../messages/constant';

import {
  ARCHIVE_STATUS,
  REMOVE_STATUS,
  STATUS_ITEM_CODE,
  TYPE_ATTRIBUTE,
} from '../../../../../utils/constants';

// service
import ObjectServices from '../../../../../services/Object';

const labelSelectAtrr = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_ATTRIBUTE,
  'Select attribute',
);
const labelSelectOperator = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_OPERATOR,
  'Select operator',
);
const labelSelectAtrrArchive = getTranslateMessage(
  TRANSLATE_KEY._COMPUTATION_ATTRIBUTE_PROPERTY_ARCHIVED,
  'The object used in the property is being archived',
);

const ItemCondition = props => {
  const { item, options, groupIndex, itemIndex, version, itemSize } = props;

  // console.log(item);
  const filteredOptions = useMemo(
    () =>
      filterDeep(
        options,
        (_value, _key, option) => {
          const { status, type } = option;

          return (
            ![ARCHIVE_STATUS, REMOVE_STATUS].includes(Number(status)) ||
            type !== TYPE_ATTRIBUTE.COMPUTED
          );
        },
        {
          childrenPath: 'options',
        },
      ),
    [options],
  );

  const [labelCus, setLabelCus] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const gridRef = useRef(0);

  const changeProperty = value => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };
    props.callback('COMP_PROP_CHANGE_PROPERTY', { version, data });
  };

  const changeOperator = value => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };
    // props.changeOperator({ version, data });
    props.callback('COMP_PROP_CHANGE_OPERATOR', { version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  // eslint-disable-next-line
  const changeValue = (value, label, extend) => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
      label,
      extendValue: extend,
    };
    // props.changeValue({ version, data });
    props.callback('COMP_PROP_CHANGE_VALUE', { version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  const changeOtherValue = (name, value) => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
      name,
    };
    // props.changeValue({ version, data });
    props.callback('COMP_PROP_CHANGE_VALUE', { version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  //   const deleteItem = useCallback(() => {
  //     props.callback('DELETE_ITEM', { groupIndex, itemIndex });
  //   }, []);

  const deleteItem = () => {
    props.callback('DELETE_ITEM', { groupIndex, itemIndex });
  };

  //   const onClick = useCallback(() => {
  //     props.callback('ADD_ITEM', { groupIndex });
  //   }, []);
  const onClick = () => {
    props.callback('ADD_ITEM', { groupIndex });
  };
  const property = safeParse(item.get('property'), null);
  const value = safeParse(item.get('value'), null);
  const operator = item.get('operator');

  const error = item.get('error');
  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  const divDisabled =
    statusItemCode !== STATUS_ITEM_CODE.ACTIVE &&
    statusItemCode !== STATUS_ITEM_CODE.DISABLED;

  const getSelectedConditionTextMode = item => {
    const initValue = formatValueItemByDataType(item, true, true);
    const value = Array.isArray(initValue)
      ? makeArrayToLabelFilter(initValue)
      : initValue;

    if (
      operator &&
      [
        'after_date',
        'before_date',
        'contains',
        'doesnt_contain',
        'start_with',
        'not_start_with',
        'end_with',
        'not_end_with',
        'equals',
        'not_equals',
      ].includes(operator.value)
    ) {
      return (
        <div>
          <Chip
            className="view-mode-value"
            variant="outlined"
            label={
              <SelectedConditionTextMode>
                <span className="property-text">{property.label}&nbsp;</span>
                <span className="operator-text">{operator.label}&nbsp;</span>
                <span className="property-text">{`"${value}"`}</span>
                <span style={{ position: 'relative', top: '2px' }}>
                  {/* <ConditionError
                  style={{ width: '100%' }}
                  statusItemCode={statusItemCode}
                  item={props.item}
                  isErrorIcon
                /> */}
                </span>
              </SelectedConditionTextMode>
            }
          />
          <div
            style={{ marginLeft: '10px' }}
            className="is--text--error p-top-1"
          >
            <ConditionError
              style={{ width: '100%' }}
              statusItemCode={statusItemCode}
              item={props.item}
              isErrorIcon
            />
          </div>
        </div>
      );
    }

    if (operator && ['matches', 'not_matches'].includes(operator.value)) {
      const valueMulti = getLabelMulti(
        labelCus && labelCus.length ? labelCus : value,
        gridRef && gridRef.current && gridRef.current.offsetWidth,
        true,
        ['array_string', 'string'].includes(property.dataType),
      );

      const valueTooltip = getLabelFull(
        labelCus.length ? labelCus : value,
        true,
        ['array_string', 'string'].includes(property.dataType),
      );

      if (isLoading) {
        return (
          <Flex justify="center" flex={1}>
            <Spin spinning />
          </Flex>
        );
      }

      return (
        <Chip
          className="view-mode-value"
          variant="outlined"
          label={
            <SelectedConditionTextMode>
              <span className="property-text">{`${
                filteredOptions[0].label
              } >> ${property.label} `}</span>
              <span className="operator-text">{operator.label}&nbsp;</span>
              <span className="property-text">{valueMulti.labels}&nbsp;</span>
              {valueMulti.labelMore && (
                <>
                  <span className="operator-text">and</span>
                  <UITippy
                    content={valueTooltip.labels}
                    showPopupWhenClick
                    subContent={
                      <SubContentTooltip>
                        Click <span>{valueMulti.labelMore}</span> to view more
                      </SubContentTooltip>
                    }
                  >
                    <span className="text-more">
                      {valueMulti.labelMore}&nbsp;
                    </span>
                  </UITippy>
                </>
              )}
              <span style={{ position: 'relative', top: '2px' }}>
                <ConditionError
                  style={{ width: '100%' }}
                  statusItemCode={statusItemCode}
                  item={props.item}
                  isErrorIcon
                />
              </span>
            </SelectedConditionTextMode>
          }
        />
      );
    }

    if (!property) {
      return null;
    }

    return (
      <div>
        <Chip
          className="view-mode-value"
          variant="outlined"
          label={
            <SelectedConditionTextMode>
              <span className="property-text">{property.label}&nbsp;</span>
              <span className="operator-text">
                {operator.label.replace(/\bis\b/gi, '').trim()}&nbsp;
              </span>
              <span className="property-text">{value}</span>
              {/* <span style={{ position: 'relative', top: '2px' }}>
                <ConditionError
                  style={{ width: '100%' }}
                  statusItemCode={statusItemCode}
                  item={props.item}
                  isErrorIcon
                />
              </span> */}
            </SelectedConditionTextMode>
          }
        />
        <div>
          <ConditionError
            style={{ width: '100%' }}
            statusItemCode={statusItemCode}
            item={props.item}
            isErrorIcon
          />
        </div>
        {property && Number(property.status) === 4 && (
          <div
            style={{ marginLeft: '10px' }}
            className="is--text--error p-top-1"
          >
            This attribute is archived
          </div>
        )}
      </div>
    );
  };

  const fetchLabelData = async (feServices, valueIds, property) => {
    const params = {
      data: {},
    };
    const result = [];
    params.data = {
      ids: valueIds,
      decryptFields: property ? [] : [`${property.encryptCode}`],
      itemTypeId: property && property.itemTypeId,
      propertyCode: property && property.encryptCode,
      itemTypeName: property && property.itemTypeName,
      config: property && property.configSuggestion,
      itemPropertyName: property && property.encryptCode,
      scope: property && property.configSuggestion.scope,
    };
    const res = await ObjectServices[feServices].lookupByIds(params);
    if (res && res.list) {
      setLabelCus(res.list.map(item => item.label));
    }

    setIsLoading(false);

    return result;
  };

  useEffect(() => {
    const feServices = 'suggestion';
    if (!value) {
      setLabelCus([]);
    } else if (props.isViewMode && value && property) {
      fetchLabelData(feServices, value, property);
    }
  }, [value]);

  const buildOption = options => {
    if (options && options.length) {
      const labelParent = options && options[0].label;
      const result =
        options && options[0].options.map(item => ({ ...item, labelParent }));
      return result;
    }

    return null;
  };
  return (
    <>
      <WrapperCondition>
        <WrapperDisable disabled={props.disabled}>
          <HeaderCondition
            label="Have Attributes"
            showButtonClose={props.showDelIcon}
            onClick={deleteItem}
            isViewMode={props.isViewMode}
          />
        </WrapperDisable>

        <ItemConditionWrapper
          isViewMode={props.isViewMode}
          ref={gridRef}
          data-test="item-condition"
        >
          {props.isViewMode ? (
            getSelectedConditionTextMode(item)
          ) : (
            <>
              <WrapperDisable disabled={divDisabled || props.disabled}>
                <div
                  className="item-condition-block property"
                  data-test="item-condition-property"
                >
                  <SelectTree
                    use="tree"
                    // isViewMode
                    onlyParent={props.onlyParent}
                    displayFormat
                    // isMulti
                    // isSearchable
                    options={filteredOptions && buildOption(filteredOptions)}
                    isParentOpen
                    value={property}
                    onChange={changeProperty}
                    placeholder={labelSelectAtrr}
                    errors={
                      error === 1
                        ? [
                            getTranslateMessage(
                              TRANSLATE_KEY._NOTI_FIELD_REQUIRED,
                              'Property is required',
                            ),
                          ]
                        : []
                    }
                  />
                  <ConditionError
                    style={{ width: '100%' }}
                    statusItemCode={statusItemCode}
                    item={props.item}
                    isErrorIcon
                  />
                  {/* <ConditionError keyError={1} error={error} /> */}
                  {/* <div>Test Error</div> */}
                </div>
              </WrapperDisable>
              <WrapperDisable disabled={divDisabled || props.disabled}>
                <div
                  className="item-condition-block operator"
                  data-test="item-condition-operator"
                >
                  <SelectTree
                    onlyParent={props.onlyParent}
                    use="tree"
                    // isViewMode
                    // isMulti
                    isSearchable={false}
                    options={item.get('operators').list}
                    value={item.get('operator')}
                    onChange={changeOperator}
                    placeholder={labelSelectOperator}
                    errors={
                      error === 2
                        ? [
                            getTranslateMessage(
                              TRANSLATE_KEY._NOTI_FIELD_REQUIRED,
                              'Property is required',
                            ),
                          ]
                        : []
                    }
                  />
                  <ConditionError keyError={2} error={error} />
                  {/* <div>Test Error</div> */}
                </div>
              </WrapperDisable>

              <WrapperDisable disabled={divDisabled || props.disabled}>
                <div
                  className="item-condition-block value"
                  data-test="item-condition-value"
                >
                  <StyleWrapperInputValue>
                    {property !== null && (
                      <ConditionValue
                        maxWidth="100%"
                        width="100%"
                        item={item}
                        // disabled={divDisabled}
                        dataType={property.dataType}
                        changeValue={changeValue}
                        changeOtherValue={changeOtherValue}
                        error={error}
                        isShowLabel={false}
                        isUseLabel={false}
                        onChangeEmptyString
                      />
                    )}
                  </StyleWrapperInputValue>
                </div>
                {/* <ConditionError
                style={{ width: '100%' }}
                statusItemCode={statusItemCode}
                item={props.item}
                isErrorIcon
              /> */}
              </WrapperDisable>
            </>
          )}
        </ItemConditionWrapper>
      </WrapperCondition>
      {itemSize !== +props.keyItem + 1 && <LabelAnd>AND</LabelAnd>}
      <Flex justify="end" className="m-top-3">
        <WrapperDisable disabled={divDisabled || props.disabled}>
          <div
            className="item-condition-block "
            style={{ display: !props.showAdd && 'none' }}
            data-test="item-condition-btn-and"
          >
            <VisibleWrapper $isHidden={!props.showAdd || props.isViewMode}>
              <ButtonWithIcon
                onClick={onClick}
                icon={<Icon type="icon-ants-add" />}
                data-test="item-condition-btn-or"
                className="btn-or-condition"
              >
                {`${getTranslateMessage(TRANSLATE_KEY._ACT_AND, 'AND')}`}
              </ButtonWithIcon>
            </VisibleWrapper>
          </div>
        </WrapperDisable>
      </Flex>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  rules: makeSelectConfigureRulesFlow(),
});

export default connect(mapStateToProps)(ItemCondition);
