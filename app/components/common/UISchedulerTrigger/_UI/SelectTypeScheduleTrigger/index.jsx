/* eslint-disable no-shadow */
/* eslint-disable indent */
/* eslint-disable dot-notation */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import SelectTree from 'components/form/UISelectCondition';
import Grid from '@material-ui/core/Grid';
import { useImmer } from 'use-immer';
import FormControl from '@material-ui/core/FormControl';

import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import RadioGroup from '../../../../Molecules/RadioGroup';
import {
  initDataScheduleTrigger,
  MAP_LABEL_TEXT_TIME,
  OPTION_TRIGGER_EVERY,
} from '../../utils';
import { useStyles, WrapperToggle, WrapperLayout } from '../../styled';
import TRANSLATE_KEY from '../../../../../messages/constant';
import SelectDaysOfWeek from '../SelectDaysOfWeek';
import SelectTime from '../SelectTime';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { safeParse } from '../../../../../utils/common';
import useUpdateEffect from '../../../../../hooks/useUpdateEffect';
import SelectDaysOfMonth from '../SelectDaysOfMonth';

const SelectTypeScheduleTrigger = props => {
  const classes = useStyles();
  const initData = safeParse(props.initData, {});
  const [state, setState] = useImmer(initDataScheduleTrigger);
  const [stateOnce, setStateOnce] = useImmer({
    hiddenDateTime: false,
  });

  const onChangeTypeTrigger = data => {
    setState(draft => {
      draft.frequency.value = data;
      draft.repeatInterval.options = OPTION_TRIGGER_EVERY[data.value];
      draft.repeatInterval.value = OPTION_TRIGGER_EVERY[data.value][0];
      draft.triggerType = 'specific_date';

      // Case trigger once, reset endCondition to never
      if (data.value === 'once') {
        draft.frequencyTime.selectEndDate.value =
          draft.frequencyTime.selectEndDate.options[0];
        draft.frequencyTime.endDate = null;
        draft.frequencyTime.endTime = null;
      }
    });
    setStateOnce(draft => {
      draft.hiddenDateTime = false;
    });
  };

  const onChangeFrequency = data => {
    setState(draft => {
      draft.repeatInterval.value = data;
    });
  };

  const onChangeSelectTime = data => {
    setState(draft => {
      draft.frequencyTime = data;
    });
  };

  const onChangeDaysOfWeek = data => {
    setState(draft => {
      draft.selectDaysOfWeek.optionsSelected = data;
    });
  };

  const onChangeDaysOfMonth = data => {
    setState(draft => {
      draft.selectDaysOfMonth.optionsSelected = data;
    });
  };

  const onChangeOnce = () => {
    const tmp =
      state.triggerType === 'after_activate'
        ? 'specific_date'
        : 'after_activate';
    setState(draft => {
      draft.triggerType = tmp;
    });
    setStateOnce(draft => {
      // draft.triggerType = tmp;
      if (tmp === 'after_activate') {
        draft.hiddenDateTime = true;
      } else {
        draft.hiddenDateTime = false;
      }
    });
  };

  useUpdateEffect(() => {
    // send data for callback onChange -> node schedule is parent use data
    let stateTmp = {};
    if (state.frequency.value.value === 'once') {
      stateTmp = {
        ...state,
        frequencyTime: {
          ...state.frequencyTime,
          endDate: null,
          endTime: null,
        },
      };
    } else {
      stateTmp = { ...state };
    }
    props.onChange({ ...stateTmp });
  }, [state]);

  useEffect(() => {
    if (Object.keys(safeParse(initData, {})).length > 0) {
      if (initData.triggerType === 'after_activate') {
        setStateOnce(draft => {
          draft.hiddenDateTime = true;
        });
      }
      setState(() => initData);
      if (initData.triggerType === undefined) {
        setState(draft => {
          draft.triggerType = 'specific_date';
        });
      }
    }
  }, [props.componentId]);

  return (
    <>
      <Grid container item sm={12} className={classes.marginBottom}>
        <Grid
          item
          sm={props.isBlastCampaign && !props.isViewMode ? 2 : 'auto'}
          data-test="trigger-journey"
        >
          <WrapperDisable
            disabled={props.disabled || props.isForceDisableFrequency}
          >
            <SelectTree
              onlyParent={props.onlyParent}
              // displayFormat={displayFormat}
              use="tree"
              // isMulti
              label={getTranslateMessage(
                TRANSLATE_KEY._INFO_STORY_FREQUENCY,
                'Send',
              )}
              isViewMode={props.isViewMode}
              isSearchable={false}
              isParentOpen={props.isParentOpen}
              options={state.frequency.options}
              value={state.frequency.value}
              onChange={onChangeTypeTrigger}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
            />
          </WrapperDisable>
        </Grid>
        {/* <Grid item sm={2} className={classes.paddingLeft}>
          <Title>Starting at</Title>
          <MuiPickersUtilsProvider utils={DateFnsUtils}>
            <KeyboardTimePicker
              keyboardIcon={<AccessAlarmIcon />}
              margin="normal"
              id="time-picker"
              //   label="Time picker"
              value={state.startingAt}
              onChange={onChangeTime}
              KeyboardButtonProps={{
                'aria-label': 'change time',
              }}
            />
          </MuiPickersUtilsProvider>
        </Grid> */}
        {state.frequency.value.value === 'once' && (
          <Grid
            item
            sm={props.isBlastCampaign && !props.isViewMode ? 10 : 12}
            style={{
              display: 'flex',
              alignItems: 'flex-end',
              marginTop: !props.isViewMode && !props.isBlastCampaign ? 18 : '',
              paddingLeft: !props.isViewMode && props.isBlastCampaign ? 20 : '',
            }}
          >
            <WrapperLayout>
              <WrapperDisable disabled={props.disabled}>
                <FormControl data-test="trigger-time">
                  <RadioGroup
                    row
                    label="Trigger time"
                    value={state.triggerType}
                    onChange={onChangeOnce}
                    name="trigger-type"
                    fs="12px !important"
                    isHiddenTitleViewMode
                    isViewMode={props.isViewMode}
                    options={[
                      {
                        value: 'specific_date',
                        label: getTranslateMessage(
                          TRANSLATE_KEY._TITL_SPECIFIC_DATE_TIME,
                          'At a specific date & time',
                        ),
                      },
                      {
                        value: 'after_activate',
                        label: getTranslateMessage(
                          TRANSLATE_KEY._TITL_SPECIFIC_JOURNEY_ACTIVATED,
                          'Right after the journey activated',
                        ),
                      },
                    ]}
                  />
                  {/* <FormControlLabel
                      value="specific_date"
                      control={
                        <Radio
                          color="primary"
                          size="small"
                          checked={state.triggerType === 'specific_date'}
                        />
                      }
                      label={getTranslateMessage(
                        TRANSLATE_KEY._TITL_SPECIFIC_DATE_TIME,
                        'At a specific date & time',
                      )}
                    />
                    <FormControlLabel
                      value="after_activate"
                      control={
                        <Radio
                          color="primary"
                          size="small"
                          checked={state.triggerType === 'after_activate'}
                        />
                      }
                      label={getTranslateMessage(
                        TRANSLATE_KEY._TITL_SPECIFIC_JOURNEY_ACTIVATED,
                        'Right after the journey activated',
                      )}
                    /> */}
                  {/* </RadioGroup> */}
                </FormControl>
              </WrapperDisable>
            </WrapperLayout>
          </Grid>
        )}
        {state.frequency.value.value !== 'once' && (
          <>
            <Grid
              item
              sm={3}
              style={{ maxWidth: '80px' }}
              className={classes.paddingLeft}
            >
              <WrapperDisable disabled={props.disabled}>
                <div style={{ position: props.isViewMode && 'relative' }}>
                  <SelectTree
                    options={state.repeatInterval.options}
                    onlyParent={props.onlyParent}
                    // displayFormat={displayFormat}
                    use="tree"
                    label={getTranslateMessage(
                      TRANSLATE_KEY._INFO_STORY_EVERY,
                      'Every',
                    )}
                    isViewMode={props.isViewMode}
                    // isMulti
                    isSearchable={false}
                    isParentOpen={props.isParentOpen}
                    value={state.repeatInterval.value}
                    onChange={onChangeFrequency}
                    placeholder={getTranslateMessage(
                      TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                      'Select an item',
                    )}
                    fullWidthPopover
                  />
                  {props.isViewMode && (
                    <span
                      style={{
                        color: '#000000',
                        position: 'absolute',
                        top: '53%',
                        right: '-28px',
                      }}
                    >
                      &nbsp;{MAP_LABEL_TEXT_TIME[state.frequency.value.value]}
                    </span>
                  )}
                </div>
              </WrapperDisable>
            </Grid>
            {!props.isViewMode && (
              <Grid item sm="auto" className={classes.textTime}>
                {MAP_LABEL_TEXT_TIME[state.frequency.value.value]}
              </Grid>
            )}
          </>
        )}
      </Grid>
      <WrapperToggle
        visibilityWrapper={state.frequency.value.value === 'weekly'}
      >
        <SelectDaysOfWeek
          onChange={onChangeDaysOfWeek}
          initData={
            Object.keys(initData).length === 0
              ? []
              : initData.selectDaysOfWeek.optionsSelected
          }
          componentId={props.componentId}
          validateKey={props.validateKey}
          disabled={props.disabled}
          isViewMode={props.isViewMode}
        />
      </WrapperToggle>
      <WrapperToggle
        visibilityWrapper={state.frequency.value.value === 'monthly'}
      >
        <SelectDaysOfMonth
          onChange={onChangeDaysOfMonth}
          initData={
            Object.keys(initData).length === 0
              ? []
              : initData.selectDaysOfMonth.optionsSelected
          }
          componentId={props.componentId}
          validateKey={props.validateKey}
          disabled={props.disabled}
          isViewMode={props.isViewMode}
        />
      </WrapperToggle>
      <WrapperToggle
        visibilityWrapper={
          state.frequency.value.value === 'monthly' ||
          state.frequency.value.value === 'weekly' ||
          state.frequency.value.value === 'daily' ||
          state.frequency.value.value === 'hourly' ||
          state.frequency.value.value === 'once'
        }
        style={{
          ...(props.isViewMode && { marginLeft: 22, display: 'block' }),
        }}
      >
        <SelectTime
          // callback={callback}
          onChange={onChangeSelectTime}
          initData={
            Object.keys(initData).length === 0 ? {} : initData.frequencyTime
          }
          typeFrequency={state.frequency.value.value}
          hiddenEndDate={state.frequency.value.value === 'once'}
          componentId={props.componentId}
          isViewMode={props.isViewMode}
          isCollapsed={props.isCollapsed}
          design={props.design}
          validateKey={props.validateKey}
          disabledEndDate={props.disabledEndDate}
          disabledStartDate={props.disabledStartDate}
          disabledPastDate={props.disabledPastDate}
          hiddenDateTime={stateOnce.hiddenDateTime}
          dataSchedule={state}
          isValidTimeScheduled={props.isValidTimeScheduled}
          isBlastCampaign={props.isBlastCampaign}
          isShowTimeZoneRight={props.isShowTimeZoneRight}
          // isHiddenAt={state.frequency.value.value === 'once'}
        />
      </WrapperToggle>
    </>
  );
};

SelectTypeScheduleTrigger.defaultProps = {
  initData: {},
  disabled: false,
  disabledPastDate: false,
};

export default SelectTypeScheduleTrigger;
