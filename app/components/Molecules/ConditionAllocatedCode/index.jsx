// Libraries
import React, { memo, useCallback } from 'react';
import { connect } from 'react-redux';
import { isArray, cloneDeep } from 'lodash';
import { createStructuredSelector } from 'reselect';
import PropTypes from 'prop-types';

// Locales
import { translations, translate } from '@antscorp/antsomi-locales';

// Selectors
import { makeCollectAllocatedPersonalizeTagAndEvents } from '../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import { Button, Flex, Typography } from '@antscorp/antsomi-ui';
import { AddIcon } from '@antscorp/antsomi-ui/es/components/icons';
import Condition from './Condition';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { LIMIT_CONDITION } from './constants';

// Utils
import { initAllocatedCode } from '../../common/UIEditorPersonalization/components/PopupPersonalization/utils';
import { initNewCondition } from './utils';

const { Text } = Typography;

const PATH = 'app/components/Molecules/ConditionAllocatedCode/index.jsx';

const MAP_TITLE = {
  labelHead: translate(
    translations._PERSONALIZE_PROCESS_CODE_CONDITION_LABEL,
    'Only codes with condition',
  ),
  btnCondition: translate(translations._ACT_ADD_CONDITION, 'Add condition'),
};

function ConditionAllocatedCode(props) {
  const {
    allocatedCode,
    allocatedPersTagAndEvents,
    disabledAdd,
    onChangeAllocatedCode,
  } = props;

  const { conditions } = allocatedCode;

  const handleAddCondition = useCallback(() => {
    const initValue =
      cloneDeep(allocatedPersTagAndEvents?.events?.list[0]) || {};

    initValue.attribute = {};

    const newCondition = initNewCondition(initValue);

    onChangeAllocatedCode('ADD_CONDITION', newCondition);
  }, [allocatedPersTagAndEvents?.events?.list, onChangeAllocatedCode]);

  const renderConditionList = () => {
    if (!isArray(conditions) || conditions.length === 0) return null;

    const content = conditions.map((condition, idx) => {
      const { type } = condition;
      const key = `${type}-${idx}`;

      return (
        <ErrorBoundary key={key} path={PATH}>
          <Condition
            index={idx}
            condition={condition}
            allocatedPersTagAndEvents={allocatedPersTagAndEvents}
            onChangeAllocatedCode={onChangeAllocatedCode}
          />
        </ErrorBoundary>
      );
    });

    return (
      <Flex vertical gap={10}>
        {content}
      </Flex>
    );
  };

  const renderAddConditionBtn = () => {
    if (conditions?.length >= LIMIT_CONDITION) return null;

    return (
      <Button
        shape="round"
        style={{
          height: 22,
          maxWidth: 112,
          gap: 0,
          borderColor: disabledAdd ? globalToken.bw4 : globalToken.colorPrimary,
        }}
        icon={<AddIcon size={20} />}
        disabled={disabledAdd}
        onClick={handleAddCondition}
      >
        {MAP_TITLE.btnCondition}
      </Button>
    );
  };

  return (
    <ErrorBoundary path={PATH}>
      <Flex vertical gap={15}>
        {conditions?.length ? (
          <div>
            <Text
              style={{
                display: 'block',
                marginBottom: 5,
              }}
            >
              {MAP_TITLE.labelHead}
            </Text>

            {renderConditionList()}
          </div>
        ) : null}

        {renderAddConditionBtn()}
      </Flex>
    </ErrorBoundary>
  );
}

ConditionAllocatedCode.propTypes = {
  // eslint-disable-next-line react/no-unused-prop-types
  moduleConfig: PropTypes.object,
  allocatedPersTagAndEvents: PropTypes.shape({
    events: PropTypes.shape({ list: PropTypes.array, map: PropTypes.object }),
    tags: PropTypes.shape({ list: PropTypes.array, map: PropTypes.object }),
  }),
  disabledAdd: PropTypes.bool,
  allocatedCode: PropTypes.object,
  onChangeAllocatedCode: PropTypes.func,
};
ConditionAllocatedCode.defaultProps = {
  moduleConfig: {},
  allocatedPersTagAndEvents: {
    events: { list: [], map: {} },
    tags: { list: [], map: {} },
  },
  allocatedCode: initAllocatedCode(),
  onChangeAllocatedCode: () => {},
};

const mapStateToProps = createStructuredSelector({
  allocatedPersTagAndEvents: makeCollectAllocatedPersonalizeTagAndEvents(),
});

export default connect(
  mapStateToProps,
  null,
)(memo(ConditionAllocatedCode));
