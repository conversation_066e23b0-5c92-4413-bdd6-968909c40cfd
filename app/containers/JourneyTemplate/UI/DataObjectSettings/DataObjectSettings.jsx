import React, { useMemo } from 'react';
import { get } from 'lodash';

import BODetail from 'modules/Dashboard/ApiHub/BusinessObject/Detail/Settings';

import { JOURNEY_OBJECT_TYPE, MODE } from '../../constant';
import { useActiveObj } from '../../hooks/useActiveObj';
import { useSelector } from 'react-redux';
import { selectMode } from '../../selector';

import { ERROR_CODE } from '../../utils/config';

export const DataObjectSettings = () => {
  const mode = useSelector(selectMode);

  const { activeObj, info, saveInfo } = useActiveObj();

  const isEventAttrObject =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.dataObject;

  const getErrors = useMemo(() => {
    const itemTypeNameExist = info.errors?.find(
      i => i.code === ERROR_CODE.BO_CODE_EXIST,
    );

    if (itemTypeNameExist) {
      return [
        {
          code: 'objectCode',
          message: 'This code has been taken, please use another one',
        },
      ];
    }
    return [];
  }, [info.errors]);

  if (!isEventAttrObject) return null;

  const getComponentProperties = () => {
    const properties = {};

    if (!info.isExist) {
      properties.settings = {
        ...saveInfo.settings,
        custom_input_attributes: [],
        positions: [],
      };
    }

    if (info.isExist && mode === MODE.Use) {
      properties.settings = {
        ...info.settings,
        custom_input_attributes: [],
        positions: [],
      };
    }

    if (info.isExist && mode === MODE.Save) {
      properties.itemTypeId = info.itemTypeId;
    }

    return properties;
  };

  // console.log({ info });
  // console.log({ activeObj });
  // console.log({ saveInfo });
  // console.log({ mode });
  // console.log(getComponentProperties());

  return (
    <BODetail isViewMode errors={getErrors} {...getComponentProperties()} />
  );
};
