/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import SelectTree from 'components/form/UISelectCondition';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import classnames from 'classnames';

import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import ConditionError from '../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import { safeParse } from '../../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../../utils/constants';
import { getDataDefautEvenActionBased } from '../utils';
import { DataSourceBlockContent } from '../styled';
import { Flex } from '@antscorp/antsomi-ui';
import { keyBy } from 'lodash';

const labelPerfEvent = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_EVENT,
  'Perform event',
);
const labelEvent = getTranslateMessage(TRANSLATE_KEY._TITL_EVENT, 'Events');

const labelSelectEvent = getTranslateMessage(
  TRANSLATE_KEY._TITL_PERFORM_EVENT,
  'Select event',
);

const SelectEvent = props => {
  const {
    item,
    options,
    property,
    groupIndex,
    itemIndex,
    version,
    rules,
    hiddeOption,
    defaultOption,
  } = props;

  const changeProperty = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  useEffect(() => {
    if (!property && !props.isViewMode && !item.get('isResetProperty')) {
      const mapOptionByEventTrackingCode = keyBy(options, 'eventTrackingCode');

      const initDefaultOption = mapOptionByEventTrackingCode[defaultOption]
        ? defaultOption
        : options[0]?.eventTrackingCode;

      const dataDefault = getDataDefautEvenActionBased(
        options,
        rules,
        hiddeOption,
        initDefaultOption,
      );

      const data = {
        groupIndex,
        itemIndex,
        value: { ...dataDefault },
        isInit: true,
      };

      props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', {
        version,
        data,
      });
      props.callback('UPDATE_CONDITIONS', data);
    }
  }, []);

  useEffect(() => {
    const keyOptionByValue = keyBy(options, 'value');
    if (property && !keyOptionByValue[property.value]) {
      const data = {
        groupIndex,
        itemIndex,
        value: null,
      };
      props.callback('COMP_PROP_CONDITION_RESET_PROPERTY', {
        version,
        data,
      });
      props.callback('UPDATE_CONDITIONS', data);
    }
  }, [property, groupIndex, itemIndex]);

  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  if (props.hidden) {
    return null;
  }

  return (
    <DataSourceBlockContent isViewMode={props.isViewMode}>
      <div className="title select-event">{labelPerfEvent}</div>
      <div className="content">
        <Flex vertical gap={5}>
          <WrapperDisable disabled={props.disabledEvent}>
            <div
              style={{ maxWidth: '16.438rem' }}
              className={classnames({
                'm-top-5': statusItemCode !== STATUS_ITEM_CODE.ACTIVE,
              })}
            >
              <SelectTree
                onlyParent={props.onlyParent}
                // displayFormat={displayFormat}
                use="tree"
                // isMulti
                // isSearchable
                options={options}
                isParentOpen={props.isParentOpen}
                value={property}
                onChange={changeProperty}
                placeholder={labelSelectEvent}
                isViewMode={props.isViewMode}
                errors={
                  Number(props.item.get('error')) === 1 && [
                    'This field is required',
                  ]
                }
              />
            </div>
          </WrapperDisable>

          <ConditionError
            keyError={1}
            error={props.item.get('error')}
            objectLabel={labelEvent}
            statusItemCode={statusItemCode}
            item={item}
          />
        </Flex>
      </div>
    </DataSourceBlockContent>
  );
};

export default React.memo(SelectEvent);
