/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { connect } from 'react-redux';

import { UIButton as Button } from '@xlab-team/ui-components';
import parse from 'html-react-parser';

import styled from 'styled-components';
import ModalFooter from 'components/Molecules/ModalFooter/index';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { addNotification } from '../../../../../redux/actions';
import { ModalV2 } from '@antscorp/antsomi-ui';

const WrapperBody = styled.div``;

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;

const WrapperAttrs = styled.div`
  max-height: 50px;
  overflow-y: auto;
  margin-top: 12px;
`;

export const ModalFooterStyle = styled(ModalFooter)`
  justify-content: flex-end;
  padding-right: 2rem;
`;

function ModalConfirmDeleteSource(props) {
  useEffect(() => {
    if (props.isOpen) {
      // console.log('....', array, arrayBranch);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.toggle(false);
  };

  const onApply = () => {
    props.toggle(false);
    props.callback('CONFIRM_DELETE_SOURCE', true);
  };

  // const classes = useStyles();
  return (
    <ModalV2
      open={props.isOpen}
      title={
        <div style={{ padding: 0 }}>
          {getTranslateMessage(TRANSLATE_KEY._WARNING, 'Warning!')}
        </div>
      }
      okText={getTranslateMessage(TRANSLATE_KEY._ACT_DELETE, 'Remove')}
      cancelText={getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel')}
      onOk={onApply}
      onCancel={onCancel}
      zIndex={1400}
      // closeIcon={null}
      styles={{ header: { marginBottom: 0 }, footer: { marginTop: 0 } }}
      centered
    >
      <WrapperBody className="p-bottom-2">
        {getTranslateMessage(
          TRANSLATE_KEY._WARN_REMOVE_ASSOCIATED_CONDITION,
          `Removing the source "${
            props.sourceName
          }" also results in removing the associated attribute conditions which only exisit in the source, particularly:
            `,
          { source_name: props.sourceName },
        )}
        <br />
        <WrapperAttrs>{parse(props.conditionRefine.join('<br>'))}</WrapperAttrs>
      </WrapperBody>
    </ModalV2>
  );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalConfirmDeleteSource.propTypes = {
  isOpen: PropTypes.bool,
  toggle: PropTypes.func,
  conditionRefine: PropTypes.array,
};
ModalConfirmDeleteSource.defaultProps = {
  isOpen: false,
  toggle: () => {},
  conditionRefine: [],
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalConfirmDeleteSource);
