/* eslint-disable react/prop-types */
/* eslint-disable indent */
/* eslint-disable import/order */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

// Services
import JourneyServices from 'services/Journey';

import Icon from '@material-ui/core/Icon';

import { WrapperChildren } from '../LayoutHeader/styles';

import {
  ContainerCustomHeader,
  WrapperCustomerHeaderContent,
  WrapperCloseIcon,
} from './styles';

import {
  makeSelectDashboard,
  makeSelectDashboardOwnerId,
  makeSelectDashboardUpdateHeaderKey,
} from '../../../modules/Dashboard/selector';
import { switchPortal } from '../../../modules/Dashboard/actions';
import Breadcrumb from '../../Molecules/Breadcrumb';
import BreadcrumbItem from '../../Atoms/BreadcrumbItem';
import { createStructuredSelector } from 'reselect';
import { updateValue } from '../../../redux/actions';
import { makeSelectStoryDetailDomainMain } from '../../../modules/Dashboard/MarketingHub/Journey/Detail/selectors';
import {
  makeSelectConfigureCreateWorkflow,
  makeSelectMainCreateWorkflow,
} from '../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';
import { getPrefixCreate } from '../../../modules/Dashboard/MarketingHub/Journey/Detail/VersionHistory2/Detail/utils';
import { getCurrentOwnerId, setWindowParam } from '../../../utils/web/cookie';
import { getStoryRoleActions } from '../../../modules/Dashboard/MarketingHub/Journey/Create/utils.story.rules';
import { CALLBACK_TYPE } from './utils';

function CustomHeader(props) {
  const [customHeaderPosition, setCustomHeaderPosition] = useState({
    left: 0,
    right: 0,
  });
  const { dashboard, ownerId = {}, maxWidthBreadcrum, optionalConfig } = props;
  const { userInfo } = dashboard;
  const { portal, user } = userInfo;
  const params = useParams();

  const activeId = props.activeId || params.activeId;

  useEffect(() => () => setWindowParam('breadcrums', []), []);

  useEffect(() => {
    const eleLayoutHeaderLeft =
      document.getElementById('layout-header-left-content') || {};
    const eleLayoutHeaderRight =
      document.getElementById('layout-header-right-content') || {};
    const left = eleLayoutHeaderLeft.clientWidth;
    const right = eleLayoutHeaderRight.clientWidth;

    setCustomHeaderPosition({
      left,
      right: right < 353 ? 353 : right,
    });
  }, [props.updateHeaderKey, ownerId.value]);

  useEffect(() => {
    const element = document.getElementById('app');
    if (props.showCancel) {
      // hidden sidebar
      element.classList.add('hidden-menu-sidebar');
    } else {
      // open sidebar
      element.classList.remove('hidden-menu-sidebar');
    }
  }, [props.showCancel]);

  useEffect(() => {
    setWindowParam('breadcrums', props.breadcrums);
  }, [props.breadcrums]);

  const onCancel = useCallback(() => {
    props.callback(CALLBACK_TYPE.ON_CANCEL, true);
  }, []);

  const activeRow =
    optionalConfig && optionalConfig.main ? optionalConfig.main.activeRow : {};
  const design =
    (props.nameEditConfig && props.nameEditConfig.design) ||
    (optionalConfig && optionalConfig.mainCreate
      ? optionalConfig.mainCreate.design
      : '');

  const roleActions = useMemo(
    () => props.roleActions || getStoryRoleActions(activeRow.accepted_actions),
    [props.roleActions, activeRow.status, activeRow.accepted_actions],
  );

  const handleChangeName = value => {
    if (design === 'create') {
      props.onChangeName(value);
    } else {
      props.onChangeNameDesignUpdate(value);
      props.onChangeName(value);
    }
  };

  const nameEditConfig =
    design && props.nameEditConfig.moduleConfigKey && props.onChangeName
      ? {
          onChange: handleChangeName,
          configure: optionalConfig.configure,
          service: JourneyServices.data.updateName,
          paramsUpdate: {
            objectId: activeId,
            data: {},
            _owner_id: getCurrentOwnerId(),
          },
          design,
          roleActions,
          ...props.nameEditConfig,
        }
      : null;

  return (
    <>
      {props.showCancel && (
        <WrapperCloseIcon onClick={onCancel}>
          <div className="_xlab_contain_icon">
            <Icon>close</Icon>
          </div>
        </WrapperCloseIcon>
      )}
      <ContainerCustomHeader
        isInit={props.isInit}
        left={customHeaderPosition.left}
        right={customHeaderPosition.right}
        isFitContent={props.isFitContent}
        isJourneyV2={props.isJourneyV2}
        isBlastCampaign={props.isBlastCampaign}
      >
        <WrapperCustomerHeaderContent showCancel={props.showCancel}>
          <div className="template-breadcrumb p-right-2">
            <Breadcrumb>
              {(props.breadcrums || []).map(item => (
                <BreadcrumbItem
                  item={item}
                  portalId={portal.portalId}
                  userId={user.userId}
                  // href={item.link}
                  key={`breadcrumb-${item.key}`}
                  maxWidth={maxWidthBreadcrum}
                  nameEditConfig={nameEditConfig}
                  isJourneyV2={props.isJourneyV2}
                >
                  {item.display}
                </BreadcrumbItem>
              ))}
            </Breadcrumb>
          </div>
        </WrapperCustomerHeaderContent>
        {props.children && <WrapperChildren>{props.children}</WrapperChildren>}
      </ContainerCustomHeader>
    </>
  );
}

CustomHeader.defaultProps = {
  showCancel: false,
  isInit: false,
  breadcrums: [],
  // maxWidthBreadcrum : set data nếu có children
};

const mapStateToProps = createStructuredSelector({
  dashboard: makeSelectDashboard(),
  ownerId: makeSelectDashboardOwnerId(),
  updateHeaderKey: makeSelectDashboardUpdateHeaderKey(),
  optionalConfig: (state, props) => ({
    ...(props.nameEditConfig &&
      props.nameEditConfig.moduleConfigKey && {
        main: makeSelectStoryDetailDomainMain()(state, {
          moduleConfig: {
            key: getPrefixCreate(
              props.nameEditConfig.moduleConfigKey,
              props.isBlastCampaign,
            ),
          },
        }),
        mainCreate: makeSelectMainCreateWorkflow()(state, {
          moduleConfig: {
            key: getPrefixCreate(
              props.nameEditConfig.moduleConfigKey,
              props.isBlastCampaign,
            ),
          },
        }),
        configure: makeSelectConfigureCreateWorkflow()(state, {
          moduleConfig: {
            key: getPrefixCreate(
              props.nameEditConfig.moduleConfigKey,
              props.isBlastCampaign,
            ),
          },
        }),
      }),
  }),
});

const mapDispatchToProps = (dispatch, props) => ({
  switchPortal: value => {
    dispatch(switchPortal(value));
  },
  ...(props.nameEditConfig &&
    props.nameEditConfig.moduleConfigKey && {
      onChangeName: value =>
        dispatch(
          updateValue(
            `${props.nameEditConfig.moduleConfigKey}@@STORY_NAME@@`,
            value,
          ),
        ),
      onChangeNameDesignUpdate: value =>
        dispatch(
          updateValue(
            `${
              props.nameEditConfig.moduleConfigKey
            }@@STORY_NAME_DESIGN_UPDATE@@`,
            value,
          ),
        ),
    }),
});
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(React.memo(CustomHeader));
