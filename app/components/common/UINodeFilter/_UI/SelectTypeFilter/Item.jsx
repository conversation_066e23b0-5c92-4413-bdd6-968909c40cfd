/* eslint-disable react/prop-types */
import React from 'react';
import { Flex } from '@antscorp/antsomi-ui';

import { FilterIcon, WrapperFilterItem } from '../../styled';

export const FilterItem = props => {
  const { item, onSelect = () => {} } = props;

  return (
    <WrapperFilterItem onClick={() => onSelect(item)}>
      <FilterIcon>{item.icon}</FilterIcon>
      <Flex vertical gap={5}>
        <div className="title">{item.label}</div>
        <div className="description">{item.description}</div>
      </Flex>
    </WrapperFilterItem>
  );
};
