$colorPrimary: #005eb8;
$colorDisabled: #a2a2a2;
$colorBlack: #000000;

.MuiButton-textPrimary {
  font-size: 14px;
  font-weight: 500 !important;
  // color: #005eb8 !important;
}
.MuiMenuItem-root {
  font-size: 13px;
}
.MuiTooltip-tooltip {
  font-size: 0.725rem !important;
  background-color: rgb(81, 81, 81) !important;
}
.MuiInputBase-root {
  padding: 0 5px;
  &.MuiInputBase-formControl {
    height: 30px !important;
    font-size: 12px;
    line-height: 14px;
    color: $colorBlack;

    & .MuiInputBase-input {
      padding: 0 !important;
    }

    &:hover:not(.Mui-disabled) {
      background-color: #f2f9ff;
    }
  }
}

.MuiButtonBase-root {
  &.MuiIconButton-root {
    padding: 0;
  }
}

.MuiRadio-root {
  &.MuiRadio-colorPrimary {
    padding: 5px !important;
    color: #005eb8;
  }
}
.MuiDialogActions-root {
  flex-direction: row-reverse;
  gap: 15px !important;
  & button {
    margin: 0 !important;
  }
  > div[class^='styles__StyleFooter-'] {
    flex-direction: row-reverse;
    gap: 15px !important;
    & button {
      margin: 0 !important;
    }
  }
}
