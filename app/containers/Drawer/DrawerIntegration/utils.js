/* eslint-disable indent */
// Libraries
import _ from 'lodash';

// Constants
import { CHANNEL_IDS } from '../../../modules/Dashboard/MarketingHub/Journey/constant';
import { MAP_TRANSLATE } from './constants.translate';

// Utils
import { addMessageToQueue } from '../../../utils/web/queue';
import { getUntitledName } from 'utils/web/properties';

const PATH = 'app/containers/Drawer/DrawerIntegration/utils.js';

export const initialState = () => ({
  isLoading: false,
  isLoadingChannels: false,
  isInitDone: false,
  refreshListing: 1, // to determine if should refresh listing channel integration or not
  hasUpdateName: false, // check if destination name has been update
  fullScreen: false,
  /*
   * destinationName
   *
   * Because when user don't select any catalog yet
   * => don't know what name should be store in which reducer
   * => using to update destination name when select a catalog
   * => and used for DELIVERY_LOG tab because this tab don't have destinationName
   * */
  destinationName: getUntitledName('Untitled Destination'),
  accessInfo: {
    general: {},
    listAccess: [],
  },
  currentProcess: null,
  currentStep: 0,
  channelId: null, // Channel Id active
  catalogCode: null, // Catalog Code active
  channels: {
    list: [],
    map: {},
  },
  destCatalogs: {
    list: [],
    map: {},
  },
});

export const normalizeArray = (arr = [], uniqueKey = 'id') => {
  const init = {
    list: [],
    map: {},
  };
  const normalizeArr = arr.reduce((acc, cur) => {
    const { list = [], map = {} } = acc;
    const uniqueId = cur[uniqueKey];

    list.push(cur);
    map[uniqueId] = cur;

    return acc;
  }, init);

  return normalizeArr;
};

export const checkHideInfoFields = name =>
  ['destinationCatalog', 'destinationName'].includes(name);

const CATALOG_CODE = Object.freeze({
  ANTSOMI_APP_PUSH: 'antsomi_app_push',
  ANTSOMI_WEB_PUSH: 'antsomi_web_push',
  ANTSOMI_EMAIL: 'antsomi_email',
  SMART_INBOX: 'smart_inbox_app',
  LINE: 'line_app',
  LINE_RICH_MENU: 'line_rich_menu',
  ZALO_OA: 'zalo_official_account',
  ZNS: 'zalo_notification_service',
  ESMS_ADVERTISING: 'esms_advertising',
});

/**
 * Validates whether the "Next" button should be enabled or disabled in the Antsomi App Push configuration wizard.
 *
 * @param {Object} params - The parameters object.
 * @param {number} [params.currentStep=0] - The current step in the configuration wizard.
 * @param {Object} [params.dataConfig={}] - The data configuration object containing validation states.
 * @returns {boolean} - Returns `true` if the "Next" button should be disabled, `false` otherwise.
 *
 * @example
 * // Example usage:
 * const isDisabled = validateBtnNextAntsomiAppPush({
 *   currentStep: 0,
 *   dataConfig: {
 *     configFields: ['field1', 'field2'],
 *     field1: { isValidate: true },
 *     field2: { isValidate: false }
 *   }
 * });
 * console.log(isDisabled); // Output: true
 */
export const validateBtnNextAntsomiAppPush = ({
  currentStep = 0,
  dataConfig = {},
}) => {
  let disabledNextBtn = false;

  try {
    // General setting step
    if (currentStep === 0) {
      const configFields = _.get(dataConfig, 'configFields', []);

      if (configFields) {
        disabledNextBtn = !configFields.every(
          each => dataConfig[each].isValidate,
        );
      }
    } else if (currentStep === 1) {
      // Configure app setting step
      const APNFields = _.get(dataConfig, 'APNFields', []);
      const FCMFields = _.get(dataConfig, 'FCMFields', []);
      const authFields = _.get(dataConfig, 'authFields', []);

      disabledNextBtn = ![...APNFields, ...authFields, ...FCMFields].every(
        each => dataConfig[each].isValidate,
      );
    }

    return disabledNextBtn;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'validateBtnNextAntsomiAppPush',
      data: {
        error: error.stack,
      },
    });
    return disabledNextBtn;
  }
};

/**
 * Generates a list of steps for different channels and catalogs in the configuration wizard.
 *
 * @param {Object} params - The parameters object.
 * @param {string} [params.channelId=''] - The ID of the channel being configured.
 * @param {string} [params.catalogCode=''] - The code of the catalog being configured.
 * @param {string} [params.design=''] - The design mode, such as 'create' or other modes.
 * @param {number} [params.currentStep=0] - The current step in the configuration wizard.
 * @param {Object} [params.dataConfig={}] - The data configuration object containing validation states.
 * @param {boolean} [params.isEmptyCatalog=false] - Whether the catalog is empty or not.
 * @returns {Object} - Returns an object containing the list of steps and the maxWidth property.
 *
 * @example
 * // Example usage:
 * const stepsConfig = getListAppendSteps({
 *   channelId: 1,
 *   catalogCode: 'antsomi_app_push',
 *   design: 'create',
 *   currentStep: 2,
 *   dataConfig: {
 *     isValidate: true,
 *     configFields: ['field1', 'field2'],
 *     field1: { isValidate: true },
 *     field2: { isValidate: false }
 *   }
 * });
 * console.log(stepsConfig);
 * // Output: { steps: [...], maxWidth: 'calc(100% - 300px)' }
 */
export const getListAppendSteps = ({
  channelId = '',
  catalogCode = '',
  design = '',
  currentStep = 0,
  dataConfig = {},
  isEmptyCatalog = false,
} = {}) => {
  const isValidate = _.get(dataConfig, 'isValidate', true);
  switch (+channelId) {
    case CHANNEL_IDS.APP_PUSH: {
      if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
        let disabledStepConfigure = isEmptyCatalog;

        // In case CREATE
        // Step 0: choose catalog => All catalogs need to be selected
        if (design === 'create' && currentStep === 0) {
          disabledStepConfigure = isEmptyCatalog || !isValidate;
        } else {
          // CurrentStep is increases by 1 step
          // because there is an additional step of choose catalog
          const step = design === 'create' ? currentStep - 1 : currentStep;

          disabledStepConfigure = validateBtnNextAntsomiAppPush({
            currentStep: step,
            dataConfig,
          });
        }

        const steps = [
          {
            title: MAP_TRANSLATE.steps.configureApp,
            disabled: disabledStepConfigure,
          },
          {
            title: MAP_TRANSLATE.steps.chooseTemplate,
            disabled: !isValidate,
          },
          {
            title: MAP_TRANSLATE.steps.sdk,
            disabled: !isValidate,
          },
          {
            title: MAP_TRANSLATE.steps.install,
            disabled: !isValidate,
          },
        ];

        return {
          steps,
          maxWidth: 'calc(100% - 300px)',
        };
      }
      break;
    }
    case CHANNEL_IDS.SMART_INBOX: {
      if (catalogCode === CATALOG_CODE.SMART_INBOX) {
        const steps = [
          {
            title: MAP_TRANSLATE.steps.notification,
            disabled: !isValidate,
          },
          {
            title: MAP_TRANSLATE.steps.inbox,
            disabled: !isValidate,
          },
          {
            title: MAP_TRANSLATE.steps.authentication,
            disabled: !isValidate,
          },
        ];
        const maxWidth = design !== 'create' ? '970px' : 'calc(100% - 150px)';

        return {
          steps,
          maxWidth,
        };
      }
      break;
    }
    case CHANNEL_IDS.LINE: {
      if ([CATALOG_CODE.LINE].includes(catalogCode)) {
        const steps = [
          {
            title: MAP_TRANSLATE.steps.chooseTemplate,
            disabled: !isValidate,
          },
        ];

        return {
          steps,
          maxWidth: '700px',
        };
      }
      break;
    }
    case CHANNEL_IDS.ZALO: {
      if (catalogCode === CATALOG_CODE.ZALO_OA) {
        const steps = [
          {
            title: MAP_TRANSLATE.steps.chooseTemplate,
            disabled: !isValidate,
          },
        ];

        return {
          steps,
          maxWidth: '700px',
        };
      }
      break;
    }
    default: {
      break;
    }
  }
  return {
    steps: [],
  };
};

/**
 * Determines the payload for saving a destination based on the provided parameters.
 *
 * @param {Object} params - The parameters for determining the payload.
 * @param {Object} [params.activeRow={}] - The active row data.
 * @param {string} [params.channelId=''] - The ID of the channel.
 * @param {string} [params.catalogCode=''] - The catalog code.
 * @param {string} [params.design=''] - The design type.
 * @param {string} [params.isNewUI=false] - Whether the destination is new or not.
 * @returns {Object|null} The payload for saving the destination or null if no payload is applicable.
 *
 * @example
 * // Example usage:
 * const payload = getPayloadSaveDestination({
 *   activeRow: { accessInfo: { userId: 1 } },
 *   channelId: '4',
 *   catalogCode: 'antsomi_app_push',
 *   design: 'update',
 * });
 * // payload will be { accessInfo: { userId: 1 } }
 */
export const getPayloadSaveDestination = ({
  activeRow = {},
  channelId = '',
  catalogCode = '',
  design = '',
  isNewUI = false,
  accessInfo = { general: {}, listAccess: [] },
}) => {
  try {
    const accessInfoData =
      design === 'create' ? accessInfo : _.get(activeRow, 'accessInfo', {});

    switch (+channelId) {
      case CHANNEL_IDS.APP_PUSH: {
        if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
          if (design === 'create') {
            return null;
          }
          return { accessInfo: accessInfoData };
        }

        return null;
      }
      case CHANNEL_IDS.WEB_PUSH: {
        if (catalogCode === CATALOG_CODE.ANTSOMI_WEB_PUSH) {
          return { accessInfo: accessInfoData, isNewUI };
        }
        return null;
      }
      case CHANNEL_IDS.EMAIL: {
        if (catalogCode === CATALOG_CODE.ANTSOMI_EMAIL) {
          return { accessInfo: accessInfoData, isNewUI };
        }
        return null;
      }
      case CHANNEL_IDS.SMART_INBOX: {
        if (catalogCode === CATALOG_CODE.SMART_INBOX) {
          return { accessInfo: accessInfoData };
        }
        return null;
      }
      case CHANNEL_IDS.LINE:
      case CHANNEL_IDS.ZALO: {
        return { accessInfo: accessInfoData };
      }
      default: {
        return null;
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'getPayloadSaveDestination',
      data: {
        error: err.stack,
      },
    });
  }
  return null;
};

/**
 * Determines the status of the "Next" button based on the current configuration state and channel.
 *
 * @param {Object} params - The parameters object.
 * @param {number} [params.channelId=''] - The ID of the channel being configured.
 * @param {string} [params.catalogCode=''] - The code of the catalog being configured.
 * @param {number} [params.currentStep=0] - The current step in the configuration wizard.
 * @param {string} [params.design='create'] - The design mode, such as 'create' or other modes.
 * @param {Object} [params.dataConfig={}] - The data configuration object containing validation states.
 * @param {boolean} [params.isValidateAll=false] - A flag indicating whether all fields are validated.
 * @param {boolean} [params.isEmptyCatalog=false] - A flag indicating whether the catalog is empty.
 * @returns {boolean} - Returns `true` if the "Next" button should be disabled, `false` otherwise.
 *
 * @example
 * // Example usage:
 * const isDisabled = getStatusNextBtnByChannel({
 *   channelId: 1,
 *   catalogCode: 'antsomi_app_push',
 *   currentStep: 1,
 *   design: 'create',
 *   dataConfig: {
 *     configFields: ['field1', 'field2'],
 *     field1: { isValidate: true },
 *     field2: { isValidate: false }
 *   },
 *   isValidateAll: true,
 *   isEmptyCatalog: false
 * });
 * console.log(isDisabled); // Output: true or false based on the logic
 */
export const getStatusNextBtnByChannel = ({
  channelId = null,
  catalogCode = '',
  currentStep = 0,
  design = 'create',
  dataConfig = {},
  isValidateAll = false,
  isEmptyCatalog = false,
}) => {
  let disabledNextBtn = false;

  try {
    // In case CREATE
    // Step 0: choose catalog => All catalogs need to be selected
    if (currentStep === 0 && design === 'create') {
      disabledNextBtn = isEmptyCatalog;
    } else {
      switch (+channelId) {
        case CHANNEL_IDS.APP_PUSH: {
          if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
            // CurrentStep is increases by 1 step
            // because there is an additional step of choose catalog
            const step = design === 'create' ? currentStep - 1 : currentStep;

            disabledNextBtn = validateBtnNextAntsomiAppPush({
              currentStep: step,
              dataConfig,
            });
          } else {
            // Rest of channels need to be validated by isValidateAll
            disabledNextBtn = !isValidateAll;
          }
          break;
        }
        default: {
          // in default case, all channel need to be validated
          disabledNextBtn = !isValidateAll;
        }
      }
    }

    return disabledNextBtn;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'getStatusNextBtnByChannel',
      data: {
        error: err.stack,
      },
    });
    return disabledNextBtn;
  }
};

export const isBroadcastSize = (catalogCode, isBroadcast) =>
  catalogCode === CATALOG_CODE.ESMS_ADVERTISING && isBroadcast;
