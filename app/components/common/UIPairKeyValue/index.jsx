/* eslint-disable consistent-return */
/* eslint-disable react/no-unused-prop-types */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import React, {
  memo,
  useEffect,
  useMemo,
  useState,
  useRef,
  useCallback,
} from 'react';
import {
  isEmpty,
  has,
  pick,
  mapValues,
  omit,
  isObject,
  cloneDeep,
  get,
  isEqual,
} from 'lodash';
import { useImmer } from 'use-immer';
import PropTypes from 'prop-types';
import { Grid, Paper } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import FormHelperText from '@material-ui/core/FormHelperText';
import UIIconXlab from 'components/common/UIIconXlab';
import styled from 'styled-components';

// Services
import TranslateServices from 'services/Object';

// Utils
import { generateKey } from '../../../utils/common';
import { addMessageToQueue } from '../../../utils/web/queue';
import { getObjectPropSafely } from '../UIEditorPersonalization/utils.3rd';
import { REQUIRED_KEYS_TEXT, handleBuildPairKeyValue } from './utils';
// eslint-disable-next-line import/no-cycle

// Components
import ItemKeyValue from './ItemKeyValue';
import { Button, Icon, Input, Spin } from '@antscorp/antsomi-ui';
import InputPreview from '../../Atoms/InputPreview';

// Translations
import TRANSLATE_KEY from '../../../messages/constant';
import { getTranslateMessage } from '../../../containers/Translate/util';

// Hooks
import useUpdateEffect from '../../../hooks/useUpdateEffect';

// Constants
import {
  CATALOG_CODES,
  TEMPLATE_ZALO_OA_TYPES,
} from '../../../modules/Dashboard/MarketingHub/Destination/CreateV2/Design/Templates/constants';
import { CATALOG_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import { StyledPaper, WrapperContent, WrapperInputTracking } from './styled';
import { useDeepCompareEffect, usePrevious } from '../../../hooks';
import { useFetchGetFormZNS } from './useFetchGetFormZNS';

const PATH = 'app/components/common/UIPairValue/index.jsx';

const headingStyles = {
  margin: '0px 0px 5px 5px',
  fontSize: '11px',
  color: 'rgba(127, 127, 127, 1)',
};

const initItem = (key = '', value = '') => ({
  key,
  value,
  errors: [],
  initValue: value,
});
const useStyles = makeStyles(() => ({
  wrapper: {
    width: '100%',
  },
  tableBox: {
    width: '100%',
    padding: '15px',
    marginTop: '15px',
    borderRadius: '10px',

    '& .block-view-mode-input-atom': {
      paddingTop: 0,
    },
  },
  button: {
    marginTop: '20px',
    margin: 'auto',
    fontWeight: 'bold',
    textTransform: 'unset',
    fontSize: '12px',
    lineHeight: '18px',
    padding: '5px 10px',
    borderRadius: '2px',
    border: 'solid 1px rgba(0, 0, 0, 0.12)',
  },
  paper: {
    borderLeft: '4px solid rgb(0, 94, 184)',
    padding: '10px',
    margin: '10px 0px',
    width: '100%',
  },
  paperFormSample: {
    borderLeft: '4px solid rgb(0, 94, 184) !important',
    padding: '10px',
    margin: '10px 0px',
    width: '100%',
    gap: '15px',
    display: 'flex',
    flexDirection: 'column',
  },
  icon: {
    color: 'rgb(112, 112, 112)',
    position: 'absolute',
    top: '-10px',
  },
  inputKey: {
    marginLeft: '5px',
    position: 'relative',
    top: '4px',
    // maxWidth: '25%',
  },
  height40: {
    height: '40px',
  },
  inputValue: {
    maxWidth: '90%',
    position: 'relative',
    top: '4px',
  },
  iconClose: {
    color: '#005eb8',
    position: 'absolute',
    top: '-0.45rem',
    left: '-0.57rem',
    cursor: 'pointer',
    fontSize: '20px',
  },
}));

const TRACK_URL = {
  KEY: 'tracking_url',
  VALUE: '#{tracking_url || ""}',
};

export const TYPE_RENDER_PAIR = {
  KEY_VALUE: 'key_value',
  TABLE: 'table_row',
};

const PairKeyValue = props => {
  const classes = useStyles();
  const [data, setData] = useImmer({
    [generateKey()]: initItem(),
  });
  const {
    initData,
    onChange,
    errors = [],
    isRequired,
    global,
    isViewMode,
    isSimplifyUI,
    labelButton,
    typeRender,
    messageType,
    disabledAddLength,
    isTrackingTime,
    showPersonalizationInKey,
    isOutputArray,
    templateId,
    onChangeOthers,
    templateSelectedZNS,
    isInitFirstRow,
    keyAsText,
    mapExtraAttributes,
  } = props;
  const [disabledAdd, setDisabledAdd] = useState(false);
  const [isSampleFormMode, setIsSampleFormMode] = useState(false);
  const [isHasUpdateFormZNS, setIsHasUpdateFormZNS] = useState(false);
  const previousFlag = usePrevious(messageType?.flag);
  const [isLoading, setIsLoading] = useState(false);
  const isTableMode = useMemo(() => typeRender === TYPE_RENDER_PAIR.TABLE, [
    typeRender,
  ]);

  const isTransactionTemplate = useMemo(
    () => templateId === TEMPLATE_ZALO_OA_TYPES.TRANSACTION,
    [templateId],
  );
  const isZaloOA = getObjectPropSafely(
    () => props.catalogCode === CATALOG_CODES.ZALO_OA,
  );

  const isZaloZNS = getObjectPropSafely(
    () => props.catalogCode === CATALOG_CODE.ZNS,
  );

  const isZaloPayZNS = getObjectPropSafely(
    () => props.catalogCode === CATALOG_CODE.ZALO_PAY_ZNS,
  );

  const isPNJZNSV2 = getObjectPropSafely(
    () => props.catalogCode === CATALOG_CODE.PNJ_ZNS_V2,
  );

  const templateIdZNS = useMemo(
    () => get(templateSelectedZNS, 'data.template.templateId', ''),
    [templateSelectedZNS],
  );

  const { formData } = useFetchGetFormZNS({
    templateId: templateIdZNS,
    destinationId: get(templateSelectedZNS, 'catalogInfo.destinationId', ''),
  });

  const handleTranslateKeys = async ({ textList = [], flag = '' }) => {
    try {
      const params = {
        data: {
          text: textList,
          toLanguageCode: flag,
          fromLanguageCode: flag === 'VI' ? 'EN' : 'VI',
        },
      };
      const {
        code = '',
        data: dataResponse = [],
      } = await TranslateServices.suggestion.translate(params);

      if (code === 200 && !isEmpty(dataResponse)) {
        const keyList = Object.keys(data);

        keyList.forEach((eachKey, index) => {
          if (isObject(dataResponse[index]) && REQUIRED_KEYS_TEXT[flag]) {
            const { textConverted = '' } = dataResponse[index];
            const isSerialized =
              textConverted.toLowerCase() ===
              REQUIRED_KEYS_TEXT[flag].NAME.toLowerCase();

            setData(draft => {
              if (has(draft[eachKey], 'key')) {
                draft[eachKey].key = isSerialized
                  ? REQUIRED_KEYS_TEXT[flag].NAME
                  : textConverted;
              }
            });
          }
        });
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleTranslateKeys',
        data: err.stack,
      });
    }
  };

  const getItemErrors = useCallback(
    (item, itemIndex, isGlobal) => {
      if (isGlobal) {
        if (isZaloPayZNS) return [];
        if (item?.key && Array.isArray(mapExtraAttributes?.[item.key]?.errors))
          return mapExtraAttributes[item.key].errors;

        return global?.errors;
      }

      if (!isZaloPayZNS && isSampleFormMode)
        return [errors[itemIndex]].filter(Boolean);

      if (item?.key && Array.isArray(mapExtraAttributes?.[item.key]?.errors))
        return mapExtraAttributes[item.key].errors;

      return [];
    },
    [global, isZaloPayZNS, mapExtraAttributes, isSampleFormMode, errors],
  );

  useEffect(() => {
    if (isTableMode && !isEmpty(data) && disabledAddLength) {
      const { length } = Object.keys(data);
      setDisabledAdd(length === +disabledAddLength);
    }
  }, [isTableMode, data, disabledAddLength]);

  useUpdateEffect(() => {
    const listKey = Object.keys(data);

    // -> translate key in case catalog zalo OA
    if (
      isZaloOA &&
      previousFlag &&
      messageType?.flag &&
      isTableMode &&
      isTransactionTemplate &&
      listKey.length &&
      !isEmpty(data[listKey[0]].key)
    ) {
      const textList = Object.values(data).map(each => each.key);

      handleTranslateKeys({ textList, flag: messageType?.flag });
    }
  }, [messageType?.flag]);

  // Init data for case is transactionTemplate of catalog ZaloOA
  useEffect(() => {
    const listKey = Object.keys(data);
    const firstId = listKey[0];

    if (
      isTableMode &&
      isZaloOA &&
      !isEmpty(messageType) &&
      isTransactionTemplate &&
      listKey.length <= 1 &&
      isEmpty((data[firstId] && data[firstId].key) || {}) &&
      isEmpty((data[firstId] && data[firstId].value) || {})
    ) {
      const { flag = '' } = messageType;
      const initDataState = {};

      const newId = generateKey();
      const newIdSecond = generateKey();

      if (flag && REQUIRED_KEYS_TEXT[flag]) {
        initDataState[newId] = initItem(REQUIRED_KEYS_TEXT[flag].NAME);
        initDataState[newIdSecond] = initItem(REQUIRED_KEYS_TEXT[flag].CODE);

        if (!isEmpty(initDataState)) {
          setData(() => initDataState);
        }
      }
    }
  }, [isTransactionTemplate, messageType]);

  useEffect(() => {
    if (
      isOutputArray &&
      !!initData &&
      Array.isArray(initData) &&
      initData.length > 0
    ) {
      const newData = {};

      initData.forEach(eachInit => {
        const newId = generateKey();
        const { key = '', value = '' } = eachInit;
        newData[newId] = initItem(key, value);
      });

      setData(() => newData);
    } else if (
      !!initData &&
      Object.keys(initData).length > 0 &&
      !isOutputArray
    ) {
      const newData = {};
      Object.keys(initData)
        .filter(key => key !== TRACK_URL.KEY)
        .forEach(each => {
          const newId = generateKey();
          newData[newId] = initItem(each, initData[each]);
        });

      // newData[generateKey()] = initItem(); // add placeholder for new row
      setData(() => newData);
      // console.log('useEffect', initData, newData);
    } else if (isViewMode) {
      setData(() => ({}));
    } else if (
      isObject(initData) &&
      isEmpty(initData) &&
      isInitFirstRow &&
      !isZaloZNS
    ) {
      setData(() => ({
        [generateKey()]: initItem(),
      }));
    }
  }, [initData]);

  useEffect(() => {
    return () => {
      setData(() => ({
        [generateKey()]: initItem(),
      }));
    };
  }, [props.catalogCode]);

  useEffect(() => {
    // handle more info field zalo ZNS
    if (isZaloZNS && formData.listParams && formData.listParams.length) {
      const isMatchKeyFormData = Object.keys(initData || {}).every(key =>
        formData.listParams.map(params => params.name).includes(key),
      );
      if (isMatchKeyFormData) {
        const newData = {};
        Object.keys(data).forEach(key => {
          const rowInfo = formData.listParams.find(
            form => form.name === data[key].key,
          );
          if (rowInfo) {
            newData[key] = {
              ...data[key],
              require: rowInfo.require,
            };
            if (rowInfo.type === 'DATE') {
              newData[key].placeHolder = 'DD/MM/YYYY';
            }
          }
        });
        setData(() => newData);
      }
    }
  }, [formData]);

  useEffect(() => {
    if (isOutputArray) {
      const dataOut = [];
      const dataList = Object.values(data || {});

      dataList.forEach(eachData => {
        const item = pick(eachData, ['key', 'value']);
        const itemTrimmed = mapValues(item, each => each.trim());

        dataOut.push(itemTrimmed);
      });

      onChange(dataOut);
    } else {
      const out = {};
      Object.keys(data).forEach(each => {
        // if (data[each].key.trim() !== '' && data[each].value.trim() !== '')
        if (
          data[each].key &&
          data[each].value &&
          data[each].key.trim?.() &&
          data[each].value.trim?.()
        ) {
          out[data[each].key.trim()] = data[each].value.trim();
        } else if (isZaloZNS && data[each].key && data[each].key.trim()) {
          out[data[each].key.trim()] = data[each].value
            ? data[each].value.trim()
            : '';
        }
      });

      if (isTrackingTime) {
        out[TRACK_URL.KEY] = TRACK_URL.VALUE;
      }

      onChange(out);
    }
  }, [data, isTrackingTime, isOutputArray]);

  const cloneDeepFormValueZNS = cloneDeep(props.value);

  useEffect(() => {
    if (isZaloZNS && props.componentKey) {
      onChange(props.initData);
    }
    return () => {
      setIsHasUpdateFormZNS(false);
    };
  }, [props.componentKey]);

  const timerRef = useRef(null);

  useEffect(() => {
    // when change template ==> reset value zalo ZNS
    if (isHasUpdateFormZNS) {
      onChange({});
    }
  }, [templateIdZNS]);

  useDeepCompareEffect(() => {
    if (isZaloZNS) {
      setIsSampleFormMode(true);
      const { catalogInfo } = templateSelectedZNS;
      if (templateIdZNS && catalogInfo && Object.keys(formData).length) {
        if (
          cloneDeepFormValueZNS &&
          props.initValue &&
          isEqual(cloneDeepFormValueZNS, props.initValue)
        ) {
          clearTimeout(timerRef.current);
          timerRef.current = setTimeout(() => {
            setIsHasUpdateFormZNS(true);
          }, 500);
          return;
        }

        if (formData && Object.keys(formData)) {
          const newPairKeyValueData = handleBuildPairKeyValue(formData);
          if (Object.keys(newPairKeyValueData).length) {
            if (
              !Object.values(newPairKeyValueData).every(each =>
                Object.keys(cloneDeepFormValueZNS || {}).includes(each.key),
              )
            ) {
              setData(() => newPairKeyValueData);
            }
          }
        }
      }
    }

    return () => {
      setIsSampleFormMode(false);
    };
  }, [
    isZaloZNS,
    templateIdZNS,
    props.initValue,
    cloneDeepFormValueZNS,
    formData,
  ]);

  // console.log('....', props);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'UPDATE_ITEM': {
        const { name, id, value } = dataIn;
        setData(draft => {
          if (has(draft, [id, name])) {
            draft[id][name] = value;
          }
        });
        break;
      }
      case 'UPDATE_ITEM_DEBOUNCE_KEY': {
        const { id, value } = dataIn;

        if (
          isTableMode &&
          isZaloOA &&
          isTransactionTemplate &&
          messageType &&
          REQUIRED_KEYS_TEXT[messageType.flag]
        ) {
          const { flag = '' } = messageType;
          const firstKey = Object.keys(data)[0];
          const secondKey = Object.keys(data)[1];
          const { key: firstItemKey = '' } = getObjectPropSafely(
            () => data[firstKey] || {},
          );
          const { key: secondItemKey = '' } = getObjectPropSafely(
            () => data[secondKey] || {},
          );

          const isSameFirstKey = value === REQUIRED_KEYS_TEXT[flag].NAME;
          const isValidFirstItem =
            firstItemKey === REQUIRED_KEYS_TEXT[flag].NAME;
          const hasCodeFirstItem = firstItemKey.includes(
            REQUIRED_KEYS_TEXT[flag].RAW_CODE,
          );
          const hasCodeSecondItem = secondItemKey.includes(
            REQUIRED_KEYS_TEXT[flag].RAW_CODE,
          );

          if (
            !isValidFirstItem &&
            (isSameFirstKey ||
              (!hasCodeFirstItem &&
                value.includes(REQUIRED_KEYS_TEXT[flag].RAW_CODE)))
          ) {
            const { value: valueCurrent = '' } = data[id] || {};
            const newData = omit(
              {
                [generateKey()]: initItem(value, valueCurrent),
                ...(data || {}),
              },
              [id],
            );

            setData(() => newData);
          } else if (
            isValidFirstItem &&
            !hasCodeSecondItem &&
            value.includes(REQUIRED_KEYS_TEXT[flag].RAW_CODE)
          ) {
            const { value: valueCurrent = '' } = data[id] || {};
            const newData = omit(
              {
                [firstKey]: data[firstKey],
                [generateKey()]: initItem(value, valueCurrent),
                ...(data || {}),
              },
              [id],
            );

            setData(() => newData);
          }
        }
        break;
      }
      case 'DELETE_ITEM': {
        if (isRequired && Object.keys(data).length <= 1) break;
        const { id } = dataIn;

        if (
          isTableMode &&
          isZaloOA &&
          isTransactionTemplate &&
          messageType &&
          REQUIRED_KEYS_TEXT[messageType.flag]
        ) {
          const listKeys = Object.keys(data);
          const isDeleteFirstRequired = listKeys[0].includes(id);
          const isDeleteSecondRequired = listKeys[1].includes(id);

          if (isDeleteFirstRequired || isDeleteSecondRequired) {
            const { flag = '' } = messageType;
            const startIndex = isDeleteFirstRequired ? 1 : 2;
            const restKeys = listKeys.slice(startIndex);

            const existsIndex = restKeys.findIndex(eachItem => {
              const item = data[eachItem];

              if (isDeleteFirstRequired)
                return item.key === REQUIRED_KEYS_TEXT[flag].NAME;
              if (isDeleteSecondRequired)
                return item.key.includes(REQUIRED_KEYS_TEXT[flag].RAW_CODE);
              return false;
            });

            if (existsIndex === -1) {
              setData(draft => {
                delete draft[id];
              });
            } else {
              const newKey = generateKey();
              const key = restKeys[existsIndex];
              const firstKey = listKeys[0];
              const itemCloned = cloneDeep(data[key]);

              const omitKeys = [id, key];
              if (isDeleteSecondRequired) omitKeys.unshift(firstKey);

              const restData = omit(data, omitKeys);

              let newData = {
                [newKey]: {
                  ...(itemCloned || {}),
                  initValue: itemCloned.initValue || itemCloned.value || '',
                },
                ...(restData || {}),
              };

              if (isDeleteSecondRequired) {
                newData = {
                  [firstKey]: data[firstKey],
                  [newKey]: {
                    ...(itemCloned || {}),
                    initValue: itemCloned.initValue || itemCloned.value || '',
                  },
                  ...(restData || {}),
                };
              }

              setData(() => newData);
            }
          } else {
            setData(draft => {
              delete draft[id];
            });
          }
        } else {
          setData(draft => {
            delete draft[id];
          });
        }

        break;
      }
      case 'ADD_ITEM': {
        const newId = generateKey();
        setData(draft => {
          draft[newId] = initItem();
        });
        break;
      }
      default:
        break;
    }
  };

  const renderBtnAdd = forceHideAddMore => {
    if (isViewMode || keyAsText || isSampleFormMode || forceHideAddMore)
      return null;

    return (
      <Grid container>
        <Grid item xs={7}>
          <Button
            disabled={disabledAdd}
            className={classes.button}
            style={
              isTableMode || isSimplifyUI
                ? { border: 'none', marginTop: isSimplifyUI ? 10 : 0 }
                : {}
            }
            icon={<Icon type="icon-ants-plus-slim" size={12} />}
            onClick={() => callback('ADD_ITEM')}
          >
            {labelButton || (isTableMode ? 'Add row' : 'Add New Pair')}
          </Button>
        </Grid>
      </Grid>
    );
  };

  const renderHeadingText = () => (
    <>
      <Grid container spacing={1}>
        <Grid
          item
          xs={5}
          style={{
            ...headingStyles,
            marginRight: 10,
            width: 200,
            maxWidth: 200,
          }}
        >
          {getTranslateMessage(TRANSLATE_KEY._ACT_ADD_LABEL, 'Label')}
        </Grid>
        <Grid item xs={5} style={headingStyles}>
          {getTranslateMessage(TRANSLATE_KEY._, 'Content')}
        </Grid>
      </Grid>
    </>
  );

  const renderContent = () => {
    const renderItemKeyValue = (each, index, dataLength) => {
      const item = data[each];
      const { key } = item;

      return (
        <ItemKeyValue
          {...props}
          global={{ ...global, errors: getItemErrors(item, index, true) }}
          dataLength={dataLength}
          key={each}
          data={data}
          item={item}
          callback={callback}
          itemId={each}
          index={index}
          showPersonalizationInKey={showPersonalizationInKey}
          messageType={messageType}
          isTransactionTemplate={isTransactionTemplate}
          hasSuggestions={isTableMode && isTransactionTemplate && isZaloOA}
          isForceHideBtnPersonalization={props.isForceHideBtnPersonalization}
          canAddPersonalization={props.canAddPersonalization}
          isViewMode={isViewMode}
          isUsePairKeyValue={props.isUsePairKeyValue}
          onChangeOthers={onChangeOthers}
          isSampleFormMode={isSampleFormMode}
          keyAsText={keyAsText}
          placeHolder={
            item?.placeHolder || mapExtraAttributes?.[key]?.placeholder
          }
          required={item?.require || mapExtraAttributes?.[key]?.required}
          errors={getItemErrors(item, index)}
        />
      );
    };

    const pairsRenderer = () => {
      const listKeys = Object.keys(data);
      return listKeys.map((k, i) => renderItemKeyValue(k, i, listKeys.length));
    };

    return (
      <>
        {!!isTrackingTime && !isTableMode && renderTrackingTime()}
        {isTableMode && renderHeadingText()}
        {!!isTrackingTime && isTableMode && (
          <div style={{ position: 'relative', margin: '10px 0px' }}>
            <Grid container>
              <WrapperInputTracking
                item
                xs={5}
                style={{
                  marginRight: 20,
                  width: 200,
                  maxWidth: 200,
                  padding: isViewMode ? '0px 10px' : '0px 0px 0px 4px',
                }}
              >
                <InputPreview
                  value={TRACK_URL.KEY}
                  isViewMode={isViewMode}
                  type="input"
                >
                  <Input disabled value={TRACK_URL.KEY} />
                </InputPreview>
              </WrapperInputTracking>
              <WrapperInputTracking
                item
                xs={5}
                style={{
                  padding: isViewMode ? '0px 5px' : '0px 0px 0px 4px',
                  flex: '1',
                  maxWidth: '100%',
                }}
              >
                <InputPreview
                  value={TRACK_URL.VALUE}
                  isViewMode={isViewMode}
                  type="input"
                >
                  <Input disabled value={TRACK_URL.VALUE} />
                </InputPreview>
              </WrapperInputTracking>
              {isTableMode && Object.keys(data).length >= 1 && !isViewMode && (
                <Grid item style={{ width: 30 }} />
              )}
            </Grid>
          </div>
        )}

        {pairsRenderer()}

        {renderBtnAdd(!isTableMode)}
      </>
    );
  };

  const IconWrapper = styled.div`
    position: relative;
    display: inline-block;

    span.icon-xlab {
      font-size: 20px;
      color: #666;
    }
  `;

  const renderTrackingTime = () => (
    <div style={{ position: 'relative' }}>
      <Grid container>
        <Paper className={classes.paper} elevation={2}>
          <Grid container spacing={1} className={classes.height40}>
            <>
              <Grid
                item
                xs={1}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <IconWrapper style={{ display: 'flex' }}>
                  {/* <Icon className={classes.icon}>vpn_key</Icon> */}
                  <UIIconXlab name="key" />
                </IconWrapper>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.inputKey}>{TRACK_URL.KEY}</div>
              </Grid>
              <Grid
                item
                xs={1}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <IconWrapper style={{ display: 'flex' }}>
                  <UIIconXlab name="outline-right-arrow" />
                </IconWrapper>
              </Grid>
              <Grid item xs={5}>
                <div className={classes.inputValue}>{TRACK_URL.VALUE}</div>
              </Grid>
            </>
          </Grid>
        </Paper>
      </Grid>
    </div>
  );

  return (
    <Spin spinning={isLoading}>
      <WrapperContent
        className={`${isTableMode ? classes.tableBox : classes.wrapper}`}
        isError={!isEmpty(errors?.filter?.(Boolean))}
        isShowBorder={isTableMode}
      >
        {renderContent()}
        {!isZaloZNS && !isZaloPayZNS && (
          <FormHelperText id="component-helper-text" error={!!errors[0]}>
            {errors}
          </FormHelperText>
        )}
      </WrapperContent>
      {isZaloPayZNS && (
        <FormHelperText id="component-helper-text" error={!!errors[0]}>
          {errors}
        </FormHelperText>
      )}

      {renderBtnAdd(isTableMode)}
    </Spin>
  );
};

PairKeyValue.propTypes = {
  initData: PropTypes.object,
  onChange: PropTypes.func,
  delayOutput: PropTypes.number,
  disabledAddLength: PropTypes.number,
  isSimplifyUI: PropTypes.bool,
  labelButton: PropTypes.string,
  typeRender: PropTypes.string,
  isOutputArray: PropTypes.bool,
  messageType: PropTypes.object,
  isRequired: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  showPersonalizationInKey: PropTypes.bool,
  errors: PropTypes.array,
  isTrackingTime: PropTypes.bool,
  templateId: PropTypes.string,
  isInitFirstRow: PropTypes.bool,
};
PairKeyValue.defaultProps = {
  initData: {},
  isForceHideBtnPersonalization: false,
  showPersonalizationInKey: false,
  typeRender: TYPE_RENDER_PAIR.KEY_VALUE,
  isOutputArray: false,
  isSimplifyUI: false,
  labelButton: '',
  disabledAddLength: null,
  messageType: {},
  delayOutput: 500,
  isRequired: true,
  errors: [],
  isTrackingTime: false,
  templateId: '',
  isInitFirstRow: false,
};
export default memo(PairKeyValue);
