/* eslint-disable react/prop-types */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
// Libraries
import React, { memo, useMemo, useCallback, useState } from 'react';
import _ from 'lodash';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';

// Services
import SegmentServices from 'services/Segment';

// Locales
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import {
  DrawerDetail,
  EditableName,
  Spin,
  DRAWER_DETAIL_DIMENSION,
  Button,
  ModalV2,
  ItemNotFound,
} from '@antscorp/antsomi-ui';
import { BoxActionRight, Header, ObjectNameBox, Workspace } from './styled';
import Design from 'modules/Dashboard/Profile/Segment/Create/DesignV2';
import PageNotFound from 'components/Templates/LayoutContent/PageNotFound';
import MemberList from 'modules/Dashboard/Profile/Segment/Detail/MemberList';
import ComputationHistory from 'modules/Dashboard/Profile/Segment/Detail/ListComputationHistory';
import ComponentRendering from 'components/common/ComponentRendering';
import VersionHistoryDetail from 'modules/Dashboard/Profile/Segment/Detail/Detail/Loadable';
import RequestAccessView from 'modules/Dashboard/Profile/Segment/Create/_UI/RequestAccessView';

// Hooks
import {
  useUpdateQueryParams,
  useUpdateObjectName,
  useDeepCompareEffect,
} from 'hooks';
import { useSegmentJourneyDone } from './hooks';

// Actions
import { updateValue, init, resetData, reset } from 'redux/actions';

// Reducers
import reducer from './reducer';
import reducerDetailSegment from 'modules/Dashboard/Profile/Segment/Detail/reducer';

// Saga
import saga from './saga';
import sagaDetailSegment from 'modules/Dashboard/Profile/Segment/Detail/saga';

// Selectors
import { makeSelectSegmentWorkspace } from './selectors';
import {
  makeSelectActiveRow,
  makeSelectSegmentDetailDomainMain,
  makeSelectActiveRowCurrentVersion,
} from 'modules/Dashboard/Profile/Segment/Detail/selectors';
import {
  makeSelectConfigCreateSegment,
  makeSelectMainCreateSegment,
} from 'modules/Dashboard/Profile/Segment/Create/selectors';

// Constants
import {
  DEFAULT_DESIGN,
  STEP,
  BO_CUSTOMER,
  BO_VISITOR,
  ID_BOX_RIGHT,
  SETTINGS,
  SHARE,
  MEMBERS,
  COMPUTE_HISTORY,
  VERSION_HISTORY,
  TAB,
  DESIGN,
  UI,
  ITEM_TYPE_ID,
  SEGMENT_ID,
  KEY,
  VERSION_ID,
  INSIGHT_TABS,
  USE_SEGMENT_IN,
  JOURNEY,
  SEGMENT,
  LATEST_VERSION_ID,
  DEFAULT_MENU_ITEMS,
  MENU_ITEMS,
  RESTORING,
} from './constants';
import { DRAWER_NAME_CACHE } from 'utils/constants';

// Configs
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from 'modules/Dashboard/Profile/Segment/Detail/config';
import { MODULE_CONFIG as MODULE_CONFIG_CREATE } from 'modules/Dashboard/Profile/Segment/Create/config';

// HOCs
import { withIframe } from 'modules/Dashboard/Profile/Segment/Detail/hocs/withIframe';

// Utils
import injectReducer, { useInjectReducer } from 'utils/injectReducer';
import injectSaga, { useInjectSaga } from 'utils/injectSaga';
import {
  getUrlSegment,
  isValidItemTypeId,
  isValidTab,
  isValidUI,
} from './utils';
import { parseSearchParamsToObject } from 'utils/web/utils';

const MAP_TRANSLATE = {
  createCustomer: getTranslateMessage(
    TRANSLATE_KEY._TITL_CREATE_CUTSOMER,
    'Create customer',
  ),
  createVisitor: getTranslateMessage(
    TRANSLATE_KEY._TITL_CREATE_VISITOR,
    'Create visitor',
  ),
  createSegment: getTranslateMessage(
    TRANSLATE_KEY._BTN_CREATE_SEG,
    'Create segment',
  ),
  labelBackToList: getTranslateMessage(
    TRANSLATE_KEY._ACT_BACK_TO_SEGMENT,
    'Back to Segments',
  ),
  labelNotFound: getTranslateMessage(
    TRANSLATE_KEY._NOTI_SEGMENT_NOT_FOUND,
    'Segment Not Found',
  ),
  close: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Close'),
  modalWarning: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Segment is only changed when you save change',
  ),
};

const PATH = 'app/containers/Drawer/DrawerSegment/index.jsx';
const PREFIX = MODULE_CONFIG.key;
const PREFIX_DETAIL_SEGMENT = MODULE_CONFIG_DETAIL.key;
const PREFIX_CREATE_SEGMENT = MODULE_CONFIG_CREATE.key;

const spinStyle = {
  margin: 'auto',
  position: 'absolute',
  top: 0,
  right: 0,
  left: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(255, 255, 255, 0.3)',
  zIndex: 99,
};
const DrawerSegment = memo(props => {
  // Injectors
  useInjectReducer({
    key: PREFIX_DETAIL_SEGMENT,
    reducer: reducerDetailSegment,
  });
  useInjectSaga({
    key: PREFIX_DETAIL_SEGMENT,
    saga: sagaDetailSegment,
    args: PREFIX_DETAIL_SEGMENT,
  });

  // Props
  const {
    afterSave,
    isDisplayExplore,
    hiddenTabs = [],
    onBeforeCancel,
    onCancel,
    fullscreen = false,
    onClose,
  } = props;

  // Router
  const { history, location } = props;

  // Selectors
  const {
    workspace,
    mainDetail = {},
    mainCreate = {},
    configure = {},
    activeRow = {},
    activeRowCurrentVersion = {},
  } = props;
  const { isDoing = false } = mainCreate;
  const { main: mainConfigure = {} } = configure;
  const { activeTab, isOpen, isInitDone } = workspace;
  const { permissions = {} } = mainDetail;
  const {
    isExist = true,
    isChecking = false,
    hasViewPermission = false,
    hasEditPermission = false,
  } = permissions;

  // Dispatch Actions
  const {
    dispatchInitWorkspace,
    dispatchResetWorkspace,
    dispatchResetConfigDetailSegment,
    dispatchResetConditions,
    dispatchResetCreateConfig,
    dispatchCancelCheckStatusForecast,
    dispatchChangeTab,
    dispatchChangeUI,
    dispatchChangeItemTypeId,
    dispatchChangeDesign,
    // dispatchReInitDashboard,
    dispatchChangeName,
    dispatchValidate,
  } = props;

  // Hooks
  const { updateSegmentDone } = useSegmentJourneyDone();
  const { updateQuery } = useUpdateQueryParams();
  const {
    isLoading: isLoadingSegmentName,
    isError: isErrorSegmentName,
    error: errorSegmentName,
    updateName: updateSegmentName,
    reset: resetSegmentNameState,
  } = useUpdateObjectName({
    serviceFunc: SegmentServices.data.rename,
    options: {
      disabled: workspace.design === 'create',
    },
  });

  // Memoized
  const searchParams = useMemo(() => new URLSearchParams(location.search), [
    location.search,
  ]);
  const {
    tab,
    ui,
    itemTypeId,
    design,
    segmentId,
    segmentKey,
    versionId,
    useSegmentInto,
    latestVersionId,
    restoring,
  } = useMemo(
    () => ({
      tab: searchParams.get(TAB),
      design: searchParams.get(DESIGN) || DEFAULT_DESIGN,
      ui: searchParams.get(UI),
      itemTypeId: searchParams.get(ITEM_TYPE_ID),
      segmentId: searchParams.get(SEGMENT_ID),
      segmentKey: searchParams.get(KEY),
      versionId: searchParams.get(VERSION_ID), // flag to determine current version in history of restore mode
      useSegmentInto: searchParams.get(USE_SEGMENT_IN), // to determine if open to CREATE segment is in another menu like: journey, segment...
      latestVersionId: searchParams.get(LATEST_VERSION_ID),
      restoring: +searchParams.get(RESTORING), // only used flag to determine if segment is restoring mode
    }),
    [searchParams],
  );

  const activeRowMemoized = useMemo(() => {
    // In case user change tab between SETTING - Version -> activeRowCurrentVersion is up-to-date
    if (!restoring && !_.isEmpty(activeRowCurrentVersion))
      return activeRowCurrentVersion;

    // Because in case restore a version mode it not support share_access history -> always get latest version share_access
    if (restoring && !_.has(activeRow, 'share_access')) {
      const shareAccessLatestVersion = _.get(
        activeRowCurrentVersion,
        'share_access',
        {},
      );

      _.set(activeRow, 'share_access', shareAccessLatestVersion);
    }

    return activeRow;
  }, [restoring, activeRow, activeRowCurrentVersion]);
  const isSegmentMenu = useMemo(() => useSegmentInto === SEGMENT, [
    useSegmentInto,
  ]);

  const steps = useMemo(() => {
    const result = [
      {
        title: (
          <span data-test="create-segment">{MAP_TRANSLATE.createSegment}</span>
        ),
      },
    ];

    if (+itemTypeId === BO_CUSTOMER) {
      result.unshift({
        title: (
          <span data-test="create-customer">
            {MAP_TRANSLATE.createCustomer}
          </span>
        ),
      });
    }

    if (+itemTypeId === BO_VISITOR) {
      result.unshift({
        title: (
          <span data-test="create-visitor">{MAP_TRANSLATE.createVisitor}</span>
        ),
      });
    }

    return result;
  }, [itemTypeId]);

  const allowViewShareAccess = useMemo(() => {
    if (design === 'create' || useSegmentInto === JOURNEY) return true;

    return hasEditPermission && !hiddenTabs.includes('shareAccess');
  }, [hasEditPermission, design, isExist, useSegmentInto]);

  const isSpinning = useMemo(() => {
    if (isDoing) return isDoing;

    if (design !== 'create' && isSegmentMenu) {
      return (mainDetail.isLoading === true || isChecking) && isExist;
    }

    return !isInitDone;
  }, [
    isInitDone,
    isDoing,
    isChecking,
    design,
    mainDetail.isLoading,
    isSegmentMenu,
  ]);

  const isRequestAccessView = useMemo(() => !hasViewPermission && isExist, [
    hasViewPermission,
    isExist,
  ]);

  const editableNameError = useMemo(
    () => _.get(mainConfigure, 'errors.name[0]'),
    [mainConfigure],
  );
  const maxWidth = useMemo(() => {
    if (useSegmentInto === JOURNEY)
      return DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth;
    return undefined;
  }, [useSegmentInto]);

  const segmentName = useMemo(() => {
    if ([COMPUTE_HISTORY, VERSION_HISTORY, MEMBERS].includes(activeTab)) {
      return mainDetail?.name;
    }

    return mainConfigure?.name;
  }, [activeTab, mainConfigure?.name, mainDetail?.name]);

  const menuItemsMemoized = useMemo(() => {
    if (isRequestAccessView || !isExist) return [];

    // in case create
    if (
      (design === 'create' && [SETTINGS, SHARE].includes(tab)) ||
      useSegmentInto === JOURNEY
    ) {
      return DEFAULT_MENU_ITEMS;
    }

    // in case update
    let items = MENU_ITEMS;
    if (_.isArray(hiddenTabs) && hiddenTabs.length > 0) {
      // exclude hidden tabs
      items = items.filter(item => !hiddenTabs.includes(item.key));
    }

    // Check if it is not allowed to view share access
    if (
      isExist &&
      !hasEditPermission &&
      (design !== 'create' || [MEMBERS, COMPUTE_HISTORY].includes(tab))
    ) {
      items = items.filter(item => item.key !== SHARE);
    }

    return items;
  }, [
    design,
    tab,
    isExist,
    hasEditPermission,
    hiddenTabs,
    isRequestAccessView,
    useSegmentInto,
  ]);

  const browserTitleMemoized = useMemo(() => {
    if (!isValidTab(tab)) {
      return getTranslateMessage(TRANSLATE_KEY._MENU_SUB_SEGMENT, 'Segments');
    }

    // in case setting segment
    if ([SETTINGS, SHARE].includes(tab)) {
      let fallbackName = getTranslateMessage(
        TRANSLATE_KEY._BREADCRUMB_CREATE_NEW_SEGMENT,
        'Create new segment',
      );
      if (design !== 'create') {
        fallbackName = _.get(activeRowMemoized, 'segment_display', segmentName);

        // if error name -> using raw name segment
        if (errorSegmentName) {
          return fallbackName;
        }
      }

      // case empty name
      if (!segmentName) {
        return fallbackName;
      }

      return segmentName;
    }

    // other tabs
    return segmentName;
  }, [
    design,
    tab,
    segmentName,
    activeRowMemoized,
    searchParams,
    errorSegmentName,
  ]);

  // State
  const isUIStep = workspace?.ui === STEP;
  const [isOpenModalWarning, setIsOpenModalWarning] = useState(false);

  const toggleModalWarning = () => {
    setIsOpenModalWarning(!isOpenModalWarning);
  };

  const handleClickMenuItem = newTabInfo => {
    if (newTabInfo && newTabInfo.key !== activeTab) {
      if (useSegmentInto === JOURNEY) {
        dispatchChangeTab({ newTab: newTabInfo.key });
        return;
      }

      const newTab = newTabInfo.key;
      const newSearchParams = new URLSearchParams({ [TAB]: newTab });
      const isInsight =
        ['update', 'preview'].includes(design) ||
        INSIGHT_TABS.includes(activeTab);

      /*
       * INFO: Route patterns
       *
       * MEMBERS: .../profile/segments?tab=members&segment-id=5648143&oid=1600085679&use-segment-into=segment-menu&latest-version-id=1
       *
       * CONFIGURE:
       *  - EDIT: .../profile/segments?tab=configure&segment-id=5648143&oid=1600085679&design=update&use-segment-into=segment-menu&latest-version-id=1
       *
       * SHARE:
       *  - EDIT: .../profile/segments?tab=share-access&segment-id=5648143&oid=1600085679&design=update&use-segment-into=segment-menu&latest-version-id=1
       *
       * COMPUTE_HISTORY: .../profile/segments?tab=computation-history&segment-id=5648143&oid=1600085679&use-segment-into=segment-menu&latest-version-id=1
       *
       * VERSION_HISTORY: .../profile/segments?tab=version-history&segment-id=5648143&oid=1600085679&design=update&version-id=2&use-segment-into=segment-menu&latest-version-id=1
       *
       * */

      switch (newTab) {
        case VERSION_HISTORY: {
          const versionSegmentId = _.get(
            activeRowMemoized,
            'segment_version.version',
            latestVersionId,
          );

          newSearchParams.set(UI, undefined);
          newSearchParams.set(ITEM_TYPE_ID, undefined);
          newSearchParams.set(DESIGN, 'update');
          newSearchParams.set(VERSION_ID, versionSegmentId);
          newSearchParams.set(RESTORING, undefined);
          break;
        }
        case MEMBERS:
        case COMPUTE_HISTORY: {
          [UI, KEY, DESIGN, VERSION_ID, ITEM_TYPE_ID, RESTORING].forEach(
            key => {
              newSearchParams.set(key, undefined);
            },
          );

          const segmentInfoId = _.get(activeRowMemoized, 'segment_id', '');
          newSearchParams.set(SEGMENT_ID, segmentInfoId);
          break;
        }
        case SETTINGS:
        case SHARE: {
          if (isInsight) {
            [UI, KEY, ITEM_TYPE_ID].forEach(key => {
              newSearchParams.set(key, undefined);
            });
          }

          const newDesign = isInsight ? 'update' : 'create';
          newSearchParams.set(DESIGN, newDesign);
          break;
        }
        default: {
          break;
        }
      }

      const queryParamsParsed = parseSearchParamsToObject(newSearchParams);
      updateQuery(queryParamsParsed);
    }
  };

  const handleCloseDrawer = () => {
    if (useSegmentInto === JOURNEY) {
      updateSegmentDone();
    } else {
      dispatchResetWorkspace();
      resetSegmentNameState();
      dispatchResetCreateConfig();
      dispatchCancelCheckStatusForecast();

      if (design !== 'create') {
        dispatchResetConditions();
        dispatchResetConfigDetailSegment();
      }

      const url = getUrlSegment({ isReset: true });
      history.push(url);
      // dispatchReInitDashboard();
    }

    if (onClose) {
      onClose();
    }
  };

  const handleBackToList = useCallback(() => {
    dispatchResetWorkspace({ isGoToListing: true });
  }, [dispatchResetWorkspace]);

  const handleBlurChangeName = () => {
    const newName = _.trim(mainConfigure.name);
    const rawName = _.get(activeRowMemoized, 'segment_display', '');

    if (newName && !_.isEqual(newName, rawName)) {
      dispatchValidate();

      updateSegmentName({
        segmentId,
        body: {
          segmentDisplay: newName,
        },
      });
    }
  };

  // Effects
  useDeepCompareEffect(() => {
    // NOTE: Init Workspace
    const isValidateTab = isValidTab(tab);

    if (isValidateTab && isSegmentMenu) {
      dispatchInitWorkspace({
        ui,
        design,
        itemTypeId,
        tab,
        segmentId,
        segmentKey,
        versionId,
      });
    }

    return () => {
      if (isSegmentMenu && isValidateTab) {
        dispatchResetWorkspace();
      }
    };
  }, [
    ui,
    design,
    itemTypeId,
    tab,
    segmentId,
    segmentKey,
    versionId,
    dispatchInitWorkspace,
    dispatchResetWorkspace,
    isSegmentMenu,
  ]);

  // Redirect to tab setting when user go from link and not permission edit
  useDeepCompareEffect(() => {
    if (
      tab === SHARE &&
      isExist &&
      !isChecking &&
      hasViewPermission &&
      !hasEditPermission &&
      design === 'update'
    ) {
      searchParams.set(TAB, SETTINGS);
      history.push({ search: searchParams.toString() });
    }
  }, [
    tab,
    design,
    history,
    isExist,
    isChecking,
    searchParams,
    hasViewPermission,
    hasEditPermission,
  ]);

  useDeepCompareEffect(() => {
    if (tab === SETTINGS && !!restoring) {
      setIsOpenModalWarning(true);
    }
  }, [restoring]);

  useDeepCompareEffect(() => {
    if (isValidTab(tab) && isInitDone && isSegmentMenu) {
      dispatchChangeTab({ newTab: tab });
    }
  }, [tab, isInitDone, dispatchChangeTab, isSegmentMenu]);

  useDeepCompareEffect(() => {
    if (isValidItemTypeId(itemTypeId) && isInitDone && isSegmentMenu) {
      dispatchChangeItemTypeId({ itemTypeId });
    }
  }, [itemTypeId, isInitDone, dispatchChangeItemTypeId, isSegmentMenu]);

  useDeepCompareEffect(() => {
    if (isValidUI(ui) && isInitDone && isSegmentMenu) {
      dispatchChangeUI({ ui });
    }
  }, [ui, isInitDone, dispatchChangeUI, isSegmentMenu]);

  useDeepCompareEffect(() => {
    if (design && isInitDone && isSegmentMenu) {
      dispatchChangeDesign({ design });
    }
  }, [design, isInitDone, dispatchChangeDesign, isSegmentMenu]);

  const renderDesign = useCallback(
    () => (
      <ErrorBoundary path={PATH}>
        <Design
          isVersion={false}
          activeRow={activeRowMemoized}
          steps={steps}
          afterSave={afterSave}
          hasEditPermission={hasEditPermission}
          setIsCheckOnChangeOuter={() => {}}
          onBeforeCancel={onBeforeCancel}
          isDisplayExplore={isDisplayExplore}
          onCancel={onCancel}
        />
      </ErrorBoundary>
    ),
    [
      steps,
      activeRowMemoized,
      afterSave,
      hasEditPermission,
      isDisplayExplore,
      onBeforeCancel,
      onCancel,
    ],
  );

  const renderMembers = useCallback(() => {
    return (
      <MemberList
        itemTypeId={mainDetail.itemTypeId}
        segmentId={mainDetail.segmentId}
        activeTab={mainDetail.activeTab}
        segmentName={mainDetail.name}
      />
    );
  }, [
    mainDetail.itemTypeId,
    mainDetail.segmentId,
    mainDetail.activeTab,
    mainDetail.name,
  ]);

  const renderComputationHistory = useCallback(() => {
    return (
      <ComputationHistory
        segmentId={mainDetail.segmentId}
        activeTab={mainDetail.activeTab}
      />
    );
  }, [mainDetail.segmentId, mainDetail.activeTab]);

  const renderVersionHistory = useCallback(() => {
    return (
      <ComponentRendering>
        <VersionHistoryDetail versionId={versionId} segmentId={segmentId} />
      </ComponentRendering>
    );
  }, [versionId, segmentId]);

  const renderWorkspaceFactory = useCallback(
    tabType => {
      if (!isInitDone) return null;

      if (isRequestAccessView) {
        return <RequestAccessView segmentId={segmentId} />;
      }

      switch (tabType) {
        case SETTINGS:
        case SHARE: {
          if (tabType === SHARE && !allowViewShareAccess)
            return <RequestAccessView segmentId={segmentId} />;

          return renderDesign();
        }
        case MEMBERS: {
          return renderMembers();
        }
        case COMPUTE_HISTORY: {
          return renderComputationHistory();
        }
        case VERSION_HISTORY: {
          return renderVersionHistory();
        }
        default: {
          return (
            <PageNotFound
              containerStyle={{ width: '100%' }}
              labelButton={MAP_TRANSLATE.labelBackToList}
              labelPageNotFound={MAP_TRANSLATE.labelNotFound}
              onClick={handleBackToList}
            />
          );
        }
      }
    },
    [
      isRequestAccessView,
      segmentId,
      isInitDone,
      allowViewShareAccess,
      renderMembers,
      renderDesign,
      handleBackToList,
      renderComputationHistory,
      renderVersionHistory,
    ],
  );

  if (!isValidTab(tab) && isSegmentMenu) return null;

  return (
    <DrawerDetail
      key="segment-layer-first"
      name={DRAWER_NAME_CACHE.SEGMENT}
      keyboard
      anchor="right"
      maxWidth={maxWidth}
      fullScreen={fullscreen}
      destroyOnClose
      open={isOpen}
      menuProps={{
        show: true,
        items: menuItemsMemoized,
        selectedKeys: activeTab,
        onClick: handleClickMenuItem,
        showExpandButton: true,
      }}
      closeIconProps={{ show: false }}
      onClose={handleCloseDrawer}
    >
      <UIHelmet title={browserTitleMemoized} />
      <ErrorBoundary path={PATH}>
        {!isRequestAccessView && isExist && (
          <Header>
            <ObjectNameBox>
              {isInitDone ? (
                <EditableName
                  className="input-name"
                  value={segmentName}
                  isLoading={isLoadingSegmentName}
                  error={
                    isErrorSegmentName ? errorSegmentName : editableNameError
                  }
                  readonly={
                    (!hasEditPermission && design === 'update') ||
                    ![SETTINGS, SHARE].includes(activeTab)
                  }
                  onBlur={() => handleBlurChangeName()}
                  onChange={nameOut => {
                    dispatchChangeName(nameOut);
                  }}
                  required
                  maxLength={255}
                />
              ) : null}
            </ObjectNameBox>
            <BoxActionRight id={ID_BOX_RIGHT} />
          </Header>
        )}
      </ErrorBoundary>
      {isExist ? (
        <Workspace isUIStep={isUIStep}>
          {isSpinning && !isRequestAccessView ? (
            <Spin
              size="large"
              spinning={isSpinning && !isRequestAccessView}
              style={spinStyle}
            />
          ) : null}
          {renderWorkspaceFactory(activeTab)}
        </Workspace>
      ) : (
        <ItemNotFound />
      )}

      <ModalV2
        open={isOpenModalWarning}
        onCancel={toggleModalWarning}
        title="Warning"
        cancelText={MAP_TRANSLATE.close}
        footer={
          <Button theme="outline" onClick={toggleModalWarning}>
            {MAP_TRANSLATE.close}
          </Button>
        }
      >
        {MAP_TRANSLATE.modalWarning}
      </ModalV2>
    </DrawerDetail>
  );
});

DrawerSegment.propTypes = {};
DrawerSegment.defaultProps = {};
const withReducer = injectReducer({
  key: PREFIX,
  reducer: reducer(MODULE_CONFIG),
});

const withSaga = injectSaga({ key: PREFIX, saga });

const mapStateToProps = createStructuredSelector({
  workspace: makeSelectSegmentWorkspace(),
  mainDetail: makeSelectSegmentDetailDomainMain(),
  mainCreate: makeSelectMainCreateSegment(),
  configure: makeSelectConfigCreateSegment(),
  activeRow: makeSelectActiveRow(),
  activeRowCurrentVersion: makeSelectActiveRowCurrentVersion(),
});

const mapDispatchToProps = (dispatch, _ownProps) => ({
  dispatchInitWorkspace: payload => {
    dispatch(init(`${PREFIX}@@INIT_WORKSPACE`, payload));
  },
  dispatchResetWorkspace: payload => {
    dispatch(resetData(`${PREFIX}@@RESET_WORKSPACE`, payload));
  },
  dispatchChangeTab: data => {
    dispatch(updateValue(`${PREFIX}@@SET_TAB`, data));
  },
  dispatchChangeUI: data => {
    dispatch(updateValue(`${PREFIX}@@SET_UI`, data));
  },
  dispatchChangeItemTypeId: data => {
    dispatch(updateValue(`${PREFIX}@@SET_ITEM_TYPE_ID`, data));
  },
  dispatchChangeDesign: data => {
    dispatch(updateValue(`${PREFIX}@@SET_DESIGN`, data));
  },
  dispatchReInitDashboard: () => {
    dispatch(updateValue(`${PREFIX}@@RE_INIT_DASHBOARD`));
  },

  // Dispatch to MODULE_CONFIG_CREATE
  dispatchResetCreateConfig: () => {
    dispatch(reset(`${PREFIX_CREATE_SEGMENT}@@RESET_INIT_DEFAULT@@`));
  },
  dispatchValidate: data => {
    dispatch(updateValue(`${PREFIX_CREATE_SEGMENT}@@VALIDATE`, data));
  },
  dispatchChangeName: value => {
    dispatch(updateValue(`${PREFIX_CREATE_SEGMENT}@@SEGMENT_NAME@@`, value));
  },
  dispatchResetConditions: () => {
    dispatch(reset(`${PREFIX_CREATE_SEGMENT}@@SEGMENT_RESET_CONDITIONS`));
  },
  dispatchCancelCheckStatusForecast: () => {
    dispatch({
      type: `${PREFIX_CREATE_SEGMENT}@@CANCEL_POLLING_GET_BUILD_SEGMENT_S`,
      payload: { isCancel: true },
    });
  },

  // Dispatch to MODULE_CONFIG_DETAIL
  dispatchResetConfigDetailSegment: () => {
    dispatch(reset(`${PREFIX_DETAIL_SEGMENT}@@RESET@@`));
  },
});

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
  withIframe,
)(DrawerSegment);
