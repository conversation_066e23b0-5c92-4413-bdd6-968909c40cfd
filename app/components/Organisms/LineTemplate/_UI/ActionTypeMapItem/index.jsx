/* eslint-disable import/order */
/* eslint-disable indent */
// Libraries
import React, { Fragment, memo, useCallback } from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';
import DateFnsUtils from '@date-io/date-fns';
import { format, isValid as isValidDate } from 'date-fns';

// Translations
import TRANSLATE_KEY from '../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../containers/Translate/util';

// Constants
import {
  ACTION_TYPES,
  FORMAT_DATE_TIME,
  FORMAT_DATE_TIME_OPTIONS,
  GROUP_CODES,
  MAPPING_FIELDS,
  URI_TYPES,
  URI_TYPE_OPTIONS,
} from '../../constants';

// Styled
import InputPreview from '../../../../Atoms/InputPreview';
import {
  GridContainer,
  PaddingElement,
  Title,
  WrapperCenterFlexCenter,
} from '../../styled';

// Components
import { Grid } from '@material-ui/core';
import AccessTimeIcon from '@material-ui/icons/AccessTime';
import UISelect from 'components/form/UISelectCondition';
import {
  UIInputCalendar,
  UIInputTime,
  UITextField,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
// eslint-disable-next-line import/no-cycle
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import {
  DateOrTimeWrapper,
  DateTimeWrapper,
  DateWrapper,
  LabelQuote,
  TimeWrapper,
} from '../ButtonItem/styled';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';

// Utils
import { getObjectPropSafely } from '../../../../../utils/web/utils';
import { handleResetAndChangeData } from '../../utils';
import { DYNAMIC_KEY } from '../../../../common/UIEditorPersonalization/Input/utils';

const MIN_DATE = format(new Date(1900, 0, 1), 'yyyy-MM-dd');
const MAX_DATE = format(new Date(2100, 11, 31), 'yyyy-MM-dd');
const TIMES_MIN = new Date(1900, 0, 1).getTime();
const TIMES_MAX = new Date(2100, 11, 31).getTime();

function ActionTypeMapItem({
  actionTypeActive,
  infoData,
  buttonId,
  isViewMode,
  typeHandler,
  callback,
  renderError,
  getErrMsgByType,
  getLabelDateTime,
  useAllocatedCode,
  moduleConfig,
  isNewCustomTag,
  onTagRemove,
  onChangeExtraDataTagProperty,
  handleChangeDataItem,
}) {
  const isDefaultAct = typeHandler === 'DEFAULT_ACTION';
  const getAdjustColumn = useCallback(
    (direction = '') => {
      if (!isDefaultAct) return '';

      if (direction === 'left') return 'grid-col-left';
      if (direction === 'right') return 'grid-col-right';
      return '';
    },
    [isDefaultAct],
  );

  if (isEmpty(actionTypeActive) || isEmpty(infoData)) return null;
  const UPDATE_KEY =
    typeHandler === 'DEFAULT_ACTION'
      ? 'ON_CHANGE_DATA_ACTIONS_FIELD'
      : 'ON_CHANGE_DATA_BTN_ITEM';

  const renderDateTimeFormatType = (formatActive = '') =>
    ({
      [FORMAT_DATE_TIME.DATE]: (
        <div style={{ width: '100%' }}>
          <DateOrTimeWrapper>
            <DateWrapper>
              <LabelQuote>From</LabelQuote>
              {isViewMode &&
              !(
                infoData[MAPPING_FIELDS.DATE_RANGE] &&
                infoData[MAPPING_FIELDS.DATE_RANGE].from
              ) ? null : (
                <UIInputCalendar
                  value={
                    infoData[MAPPING_FIELDS.DATE_RANGE] &&
                    infoData[MAPPING_FIELDS.DATE_RANGE].from
                  }
                  onChange={valueOut => {
                    let tmp = valueOut;
                    if (
                      isValidDate(valueOut) &&
                      (valueOut < TIMES_MIN || valueOut > TIMES_MAX)
                    )
                      tmp = new Date().getTime();

                    handleChangeDataItem(UPDATE_KEY, {
                      name: MAPPING_FIELDS.DATE_RANGE,
                      value: {
                        ...(infoData[MAPPING_FIELDS.DATE_RANGE] || {}),
                        from: isValidDate(tmp) ? tmp : '',
                      },
                    });

                    if (
                      infoData[MAPPING_FIELDS.DEFAULT_DATE] &&
                      infoData[MAPPING_FIELDS.DEFAULT_DATE] < tmp
                    ) {
                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.DEFAULT_DATE,
                        value: isValidDate(tmp) ? tmp : '',
                      });
                    }
                  }}
                  minDate={MIN_DATE}
                  maxDate={MAX_DATE}
                  isShowLabel={false}
                  isViewMode={isViewMode}
                  placeholder="MM/DD/YYYY"
                />
              )}
            </DateWrapper>
            <DateWrapper style={{ gap: '0px' }}>
              <LabelQuote>To</LabelQuote>
              {isViewMode &&
              !(
                infoData[MAPPING_FIELDS.DATE_RANGE] &&
                infoData[MAPPING_FIELDS.DATE_RANGE].to
              ) ? null : (
                <UIInputCalendar
                  value={
                    infoData[MAPPING_FIELDS.DATE_RANGE] &&
                    infoData[MAPPING_FIELDS.DATE_RANGE].to
                  }
                  onChange={valueOut => {
                    let tmp = valueOut;
                    if (
                      isValidDate(valueOut) &&
                      (valueOut < TIMES_MIN || valueOut > TIMES_MAX)
                    )
                      tmp = new Date().getTime();

                    handleChangeDataItem(UPDATE_KEY, {
                      name: MAPPING_FIELDS.DATE_RANGE,
                      value: {
                        ...(infoData[MAPPING_FIELDS.DATE_RANGE] || {}),
                        to: isValidDate(tmp) ? tmp : '',
                      },
                    });

                    if (
                      isValidDate(tmp) &&
                      infoData[MAPPING_FIELDS.DEFAULT_DATE] &&
                      infoData[MAPPING_FIELDS.DEFAULT_DATE] > tmp
                    ) {
                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.DEFAULT_DATE,
                        value:
                          infoData[MAPPING_FIELDS.DATE_RANGE] &&
                          infoData[MAPPING_FIELDS.DATE_RANGE].from
                            ? infoData[MAPPING_FIELDS.DATE_RANGE].from
                            : tmp,
                      });
                    }
                  }}
                  minDate={
                    infoData[MAPPING_FIELDS.DATE_RANGE] &&
                    infoData[MAPPING_FIELDS.DATE_RANGE].from
                      ? format(
                          new Date(infoData[MAPPING_FIELDS.DATE_RANGE].from),
                          'yyyy-MM-dd',
                        )
                      : MIN_DATE
                  }
                  maxDate={MAX_DATE}
                  isShowLabel={false}
                  isViewMode={isViewMode}
                  placeholder="MM/DD/YYYY"
                />
              )}
            </DateWrapper>
          </DateOrTimeWrapper>
          {renderError(
            MAPPING_FIELDS.DATE_RANGE,
            {},
            true,
            typeHandler === 'BUTTON_ITEM' ? buttonId : null,
            typeHandler === 'DEFAULT_ACTION',
          )}
        </div>
      ),
      [FORMAT_DATE_TIME.TIME]: (
        <div style={{ width: '100%' }}>
          <DateOrTimeWrapper>
            <TimeWrapper style={{ maxWidth: 155 }}>
              <MuiPickersUtilsProvider utils={DateFnsUtils}>
                <LabelQuote>From</LabelQuote>
                {isViewMode &&
                !(
                  infoData[MAPPING_FIELDS.TIME_RANGE] &&
                  infoData[MAPPING_FIELDS.TIME_RANGE].from
                ) ? null : (
                  <UIInputTime
                    isViewMode={isViewMode}
                    keyboardIcon={<AccessTimeIcon />}
                    margin="normal"
                    id="time-picker"
                    value={
                      (infoData[MAPPING_FIELDS.TIME_RANGE] &&
                        infoData[MAPPING_FIELDS.TIME_RANGE].from) ||
                      null
                    }
                    onChange={valueOut =>
                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.TIME_RANGE,
                        value: {
                          ...(infoData[MAPPING_FIELDS.TIME_RANGE] || {}),
                          from: Date.parse(valueOut),
                        },
                      })
                    }
                    KeyboardButtonProps={{
                      'aria-label': 'change time',
                    }}
                  />
                )}
              </MuiPickersUtilsProvider>
            </TimeWrapper>
            <TimeWrapper style={{ maxWidth: 140, gap: '10px' }}>
              <MuiPickersUtilsProvider utils={DateFnsUtils}>
                <LabelQuote>To</LabelQuote>
                {isViewMode &&
                !(
                  infoData[MAPPING_FIELDS.TIME_RANGE] &&
                  infoData[MAPPING_FIELDS.TIME_RANGE].to
                ) ? null : (
                  <UIInputTime
                    isViewMode={isViewMode}
                    keyboardIcon={<AccessTimeIcon />}
                    margin="normal"
                    id="time-picker"
                    value={
                      (infoData[MAPPING_FIELDS.TIME_RANGE] &&
                        infoData[MAPPING_FIELDS.TIME_RANGE].to) ||
                      null
                    }
                    onChange={valueOut =>
                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.TIME_RANGE,
                        value: {
                          ...(infoData[MAPPING_FIELDS.TIME_RANGE] || {}),
                          to: Date.parse(valueOut),
                        },
                      })
                    }
                    KeyboardButtonProps={{
                      'aria-label': 'change time',
                    }}
                  />
                )}
              </MuiPickersUtilsProvider>
            </TimeWrapper>
          </DateOrTimeWrapper>
          {renderError(
            MAPPING_FIELDS.TIME_RANGE,
            {},
            true,
            typeHandler === 'BUTTON_ITEM' ? buttonId : null,
            typeHandler === 'DEFAULT_ACTION',
          )}
        </div>
      ),
      [FORMAT_DATE_TIME.DATE_TIME]: (() => {
        // dtRange === dateTimeRange
        const dtRange = getObjectPropSafely(
          () => infoData[MAPPING_FIELDS.DATE_TIME_RANGE],
        );

        return (
          <div style={{ width: '100%' }}>
            <DateTimeWrapper>
              <DateWrapper>
                <LabelQuote>From</LabelQuote>
                {isViewMode &&
                !(dtRange && dtRange.from && dtRange.from.date) ? null : (
                  <UIInputCalendar
                    value={dtRange && dtRange.from && dtRange.from.date}
                    onChange={valueOut => {
                      let tmp = valueOut;
                      if (
                        isValidDate(valueOut) &&
                        (valueOut < TIMES_MIN || valueOut > TIMES_MAX)
                      )
                        tmp = new Date().getTime();

                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.DATE_TIME_RANGE,
                        value: {
                          ...(dtRange || {}),
                          from: {
                            ...((dtRange && dtRange.from) || {}),
                            date: isValidDate(tmp) ? tmp : '',
                          },
                        },
                      });

                      if (
                        infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME] &&
                        infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME].date < tmp
                      ) {
                        handleChangeDataItem(UPDATE_KEY, {
                          name: MAPPING_FIELDS.DEFAULT_DATE_TIME,
                          value: {
                            ...(infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME] ||
                              {}),
                            date: isValidDate(tmp) ? tmp : '',
                          },
                        });
                      }
                    }}
                    minDate={MIN_DATE}
                    maxDate={MAX_DATE}
                    isShowLabel={false}
                    isViewMode={isViewMode}
                    placeholder="MM/DD/YYYY"
                  />
                )}
              </DateWrapper>
              <span style={{ whiteSpace: 'nowrap' }}>at</span>
              <TimeWrapper>
                <MuiPickersUtilsProvider utils={DateFnsUtils}>
                  {isViewMode &&
                  !(dtRange && dtRange.from && dtRange.from.time) ? null : (
                    <UIInputTime
                      isViewMode={isViewMode}
                      keyboardIcon={<AccessTimeIcon />}
                      margin="normal"
                      id="time-picker"
                      value={
                        (dtRange && dtRange.from && dtRange.from.time) || null
                      }
                      onChange={valueOut =>
                        handleChangeDataItem(UPDATE_KEY, {
                          name: MAPPING_FIELDS.DATE_TIME_RANGE,
                          value: {
                            ...(dtRange || {}),
                            from: {
                              ...((dtRange && dtRange.from) || {}),
                              time: Date.parse(valueOut),
                            },
                          },
                        })
                      }
                      KeyboardButtonProps={{
                        'aria-label': 'change time',
                      }}
                    />
                  )}
                </MuiPickersUtilsProvider>
              </TimeWrapper>
            </DateTimeWrapper>
            {renderError(
              MAPPING_FIELDS.DATE_TIME_RANGE,
              {},
              true,
              typeHandler === 'BUTTON_ITEM' ? buttonId : null,
              typeHandler === 'DEFAULT_ACTION',
            )}
            <DateTimeWrapper style={{ marginTop: 4 }}>
              <DateWrapper>
                <LabelQuote>To</LabelQuote>
                {isViewMode &&
                !(dtRange && dtRange.to && dtRange.to.date) ? null : (
                  <UIInputCalendar
                    value={dtRange && dtRange.to && dtRange.to.date}
                    onChange={valueOut => {
                      let tmp = valueOut;
                      if (
                        isValidDate(valueOut) &&
                        (valueOut < TIMES_MIN || valueOut > TIMES_MAX)
                      )
                        tmp = new Date().getTime();

                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.DATE_TIME_RANGE,
                        value: {
                          ...(dtRange || {}),
                          to: {
                            ...((dtRange && dtRange.to) || {}),
                            date: isValidDate(tmp) ? tmp : '',
                          },
                        },
                      });
                    }}
                    minDate={
                      dtRange && dtRange.from && dtRange.from.date
                        ? format(new Date(dtRange.from.date), 'yyyy-MM-dd')
                        : MIN_DATE
                    }
                    maxDate={MAX_DATE}
                    isShowLabel={false}
                    isViewMode={isViewMode}
                    placeholder="MM/DD/YYYY"
                  />
                )}
              </DateWrapper>
              <span style={{ whiteSpace: 'nowrap' }}>at</span>
              <TimeWrapper>
                <MuiPickersUtilsProvider utils={DateFnsUtils}>
                  {isViewMode &&
                  !(dtRange && dtRange.to && dtRange.to.time) ? null : (
                    <UIInputTime
                      isViewMode={isViewMode}
                      keyboardIcon={<AccessTimeIcon />}
                      margin="normal"
                      id="time-picker"
                      value={(dtRange && dtRange.to && dtRange.to.time) || null}
                      onChange={valueOut =>
                        handleChangeDataItem(UPDATE_KEY, {
                          name: MAPPING_FIELDS.DATE_TIME_RANGE,
                          value: {
                            ...(dtRange || {}),
                            to: {
                              ...((dtRange && dtRange.to) || {}),
                              time: Date.parse(valueOut),
                            },
                          },
                        })
                      }
                      KeyboardButtonProps={{
                        'aria-label': 'change time',
                      }}
                    />
                  )}
                </MuiPickersUtilsProvider>
              </TimeWrapper>
            </DateTimeWrapper>
          </div>
        );
      })(),
    }[formatActive]);

  switch (actionTypeActive) {
    case ACTION_TYPES.MESSAGE: {
      return (
        <GridContainer container>
          <Grid item sm={3} className={getAdjustColumn('left')}>
            <WrapperCenterFlexCenter>
              <Title>
                {getTranslateMessage(
                  TRANSLATE_KEY._LINE_TEMPLATE__MESS_TEXT,
                  'Message Text',
                )}
                <span style={{ color: '#ff0000' }}> *</span>
              </Title>
              <PaddingElement />
            </WrapperCenterFlexCenter>
          </Grid>
          <Grid item sm={9} className={getAdjustColumn('right')}>
            <InputPreview
              value={infoData[MAPPING_FIELDS.MESSAGE_TEXT]}
              type="input"
              isViewMode={isViewMode}
            >
              <WrapperDisable disabled={false}>
                <TinymceEditor
                  errors={getErrMsgByType({
                    mappingField: MAPPING_FIELDS.MESSAGE_TEXT,
                    buttonId,
                    isExtraData: typeHandler === 'DEFAULT_ACTION',
                  })}
                  groupCodes={GROUP_CODES}
                  typeComponent="input"
                  useAllocatedCode={useAllocatedCode}
                  moduleConfig={moduleConfig}
                  isNewCustomTag={isNewCustomTag}
                  onTagRemove={onTagRemove}
                  onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
                  onChangeOthers={() => {}}
                  onChange={valueOut =>
                    handleChangeDataItem(UPDATE_KEY, {
                      name: MAPPING_FIELDS.MESSAGE_TEXT,
                      value: valueOut,
                    })
                  }
                  initData={infoData[MAPPING_FIELDS.MESSAGE_TEXT]}
                  enableShortLink={false}
                  width="100%"
                  isBlastCampaign={false}
                />
                {renderError(
                  MAPPING_FIELDS.MESSAGE_TEXT,
                  infoData[MAPPING_FIELDS.MESSAGE_TEXT],
                  false,
                  buttonId,
                  typeHandler === 'DEFAULT_ACTION',
                )}
              </WrapperDisable>
            </InputPreview>
          </Grid>
        </GridContainer>
      );
    }
    case ACTION_TYPES.URI: {
      const URITypeSelected = getObjectPropSafely(
        () => infoData[MAPPING_FIELDS.URI_TYPE],
      );
      const URITypeValue = URITypeSelected && URITypeSelected.value;

      return (
        <Fragment>
          <GridContainer container style={{ paddingBottom: 0 }}>
            <Grid item sm={3} className={getAdjustColumn('left')}>
              <WrapperCenterFlexCenter>
                <Title>
                  {getTranslateMessage(TRANSLATE_KEY._, 'URI Type')}
                  <span style={{ color: '#ff0000' }}> *</span>
                </Title>
              </WrapperCenterFlexCenter>
            </Grid>
            <Grid item sm={9} className={getAdjustColumn('right')}>
              <InputPreview
                isViewMode={isViewMode}
                type="input"
                value={URITypeSelected && URITypeSelected.value}
              >
                <UISelect
                  onlyParent
                  use="tree"
                  isSearchable={false}
                  options={URI_TYPE_OPTIONS}
                  value={URITypeSelected}
                  onChange={valueOut =>
                    handleResetAndChangeData({
                      buttonId,
                      infoData,
                      callback,
                      type: typeHandler,
                      nameFieldChange: MAPPING_FIELDS.URI_TYPE,
                      dataIn: valueOut,
                    })
                  }
                  placeholder="Choose an action type"
                  fullWidthPopover
                  labelWidth="100%"
                />
              </InputPreview>
            </Grid>
          </GridContainer>
          {URITypeValue === URI_TYPES.PHONE && (
            <GridContainer container>
              <Grid item sm={3} className={getAdjustColumn('left')}>
                <WrapperCenterFlexCenter>
                  <Title>
                    {getTranslateMessage(
                      TRANSLATE_KEY._LINE_ACT_CALL_PHONE_NO,
                      'Phone number',
                    )}
                    <span style={{ color: '#ff0000' }}> *</span>
                  </Title>
                  <PaddingElement />
                </WrapperCenterFlexCenter>
              </Grid>
              <Grid item sm={9} className={getAdjustColumn('right')}>
                <InputPreview
                  value={infoData[MAPPING_FIELDS.PHONE]}
                  type="input"
                  isViewMode={isViewMode}
                >
                  <UITextField
                    value={infoData[MAPPING_FIELDS.PHONE]}
                    onChange={valueOut =>
                      handleChangeDataItem(UPDATE_KEY, {
                        name: MAPPING_FIELDS.PHONE,
                        value: valueOut,
                      })
                    }
                    // firstText={props.errors[0]}
                    // maxLength={1000}
                    textFieldProps={{
                      disabled: false,
                      size: 'small',
                      multiline: false,
                      rowsMax: 1,
                      className: classNames({
                        'input-text-field-error': !!getErrMsgByType({
                          type: 'string',
                          mappingField: MAPPING_FIELDS.PHONE,
                          buttonId,
                          isExtraData: typeHandler === 'DEFAULT_ACTION',
                        }),
                      }),
                      style: {
                        width: '100%',
                      },
                    }}
                  />
                  {renderError(
                    MAPPING_FIELDS.PHONE,
                    infoData[MAPPING_FIELDS.PHONE],
                    false,
                    buttonId,
                    typeHandler === 'DEFAULT_ACTION',
                  )}
                </InputPreview>
              </Grid>
            </GridContainer>
          )}
          {URITypeValue === URI_TYPES.REDIRECT_URL && (
            <GridContainer container>
              <Grid item sm={3} className={getAdjustColumn('left')}>
                <WrapperCenterFlexCenter>
                  <Title>
                    {getTranslateMessage(TRANSLATE_KEY._, 'URL')}
                    <span style={{ color: '#ff0000' }}> *</span>
                  </Title>
                  <PaddingElement />
                </WrapperCenterFlexCenter>
              </Grid>
              <Grid item sm={9} className={getAdjustColumn('right')}>
                <InputPreview
                  value={infoData[MAPPING_FIELDS.URL]}
                  type="input"
                  isViewMode={isViewMode}
                >
                  <WrapperDisable disabled={false}>
                    <TinymceEditor
                      errors={getErrMsgByType({
                        mappingField: MAPPING_FIELDS.URL,
                        buttonId,
                        isExtraData: typeHandler === 'DEFAULT_ACTION',
                      })}
                      groupCodes={GROUP_CODES}
                      typeComponent="input"
                      useAllocatedCode={useAllocatedCode}
                      moduleConfig={moduleConfig}
                      isNewCustomTag={isNewCustomTag}
                      onTagRemove={onTagRemove}
                      onChangeExtraDataTagProperty={
                        onChangeExtraDataTagProperty
                      }
                      onChange={valueOut =>
                        handleChangeDataItem(UPDATE_KEY, {
                          name: MAPPING_FIELDS.URL,
                          value: valueOut,
                        })
                      }
                      hiddenDynamicOption={[DYNAMIC_KEY.addPer]}
                      onChangeOthers={() => {}}
                      initData={infoData[MAPPING_FIELDS.URL]}
                      enableShortLink
                      width="100%"
                      isBlastCampaign={false}
                    />
                    {renderError(
                      MAPPING_FIELDS.URL,
                      infoData[MAPPING_FIELDS.URL],
                      false,
                      buttonId,
                      typeHandler === 'DEFAULT_ACTION',
                    )}
                  </WrapperDisable>
                </InputPreview>
              </Grid>
            </GridContainer>
          )}
        </Fragment>
      );
    }
    case ACTION_TYPES.POST_BACK: {
      return (
        <GridContainer container style={{ paddingBottom: 0 }}>
          <Grid item sm={3} className={getAdjustColumn('left')}>
            <WrapperCenterFlexCenter>
              <Title>
                {getTranslateMessage(TRANSLATE_KEY._, 'Data')}
                <span style={{ color: '#ff0000' }}> *</span>
              </Title>
              <PaddingElement />
            </WrapperCenterFlexCenter>
          </Grid>
          <Grid item sm={9} className={getAdjustColumn('right')}>
            <InputPreview
              isViewMode={isViewMode}
              type="input"
              value={infoData[MAPPING_FIELDS.DATA_POST_BACK]}
            >
              <WrapperDisable disabled={false}>
                <TinymceEditor
                  errors={getErrMsgByType({
                    mappingField: MAPPING_FIELDS.DATA_POST_BACK,
                    buttonId,
                    isExtraData: typeHandler === 'DEFAULT_ACTION',
                  })}
                  groupCodes={GROUP_CODES}
                  typeComponent="input"
                  useAllocatedCode={useAllocatedCode}
                  moduleConfig={moduleConfig}
                  isNewCustomTag={isNewCustomTag}
                  onTagRemove={onTagRemove}
                  onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
                  onChange={valueOut =>
                    handleChangeDataItem(UPDATE_KEY, {
                      name: MAPPING_FIELDS.DATA_POST_BACK,
                      value: valueOut,
                    })
                  }
                  onChangeOthers={() => {}}
                  initData={infoData[MAPPING_FIELDS.DATA_POST_BACK]}
                  enableShortLink={false}
                  width="100%"
                  isBlastCampaign={false}
                />
                {renderError(
                  MAPPING_FIELDS.DATA_POST_BACK,
                  infoData[MAPPING_FIELDS.DATA_POST_BACK],
                  false,
                  buttonId,
                  typeHandler === 'DEFAULT_ACTION',
                )}
              </WrapperDisable>
            </InputPreview>
          </Grid>
        </GridContainer>
      );
    }
    case ACTION_TYPES.DATE_TIME: {
      const formatSelected = getObjectPropSafely(
        () => infoData[MAPPING_FIELDS.FORMAT],
      );
      const formatValue = formatSelected && formatSelected.value;
      // dtRange === dateTimeRange
      const dtRange = getObjectPropSafely(
        () => infoData[MAPPING_FIELDS.DATE_TIME_RANGE],
      );

      return (
        <Fragment>
          <GridContainer container>
            <Grid item sm={3} className={getAdjustColumn('left')}>
              <WrapperCenterFlexCenter>
                <Title>
                  {getTranslateMessage(TRANSLATE_KEY._, 'Data')}
                  <span style={{ color: '#ff0000' }}> *</span>
                </Title>
                <PaddingElement />
              </WrapperCenterFlexCenter>
            </Grid>
            <Grid item sm={9} className={getAdjustColumn('right')}>
              <InputPreview
                value={infoData[MAPPING_FIELDS.DATA_DATE_TIME]}
                type="input"
                isViewMode={isViewMode}
              >
                <TinymceEditor
                  errors={getErrMsgByType({
                    mappingField: MAPPING_FIELDS.DATA_DATE_TIME,
                    buttonId,
                    isExtraData: typeHandler === 'DEFAULT_ACTION',
                  })}
                  groupCodes={GROUP_CODES}
                  typeComponent="input"
                  useAllocatedCode={useAllocatedCode}
                  moduleConfig={moduleConfig}
                  isNewCustomTag={isNewCustomTag}
                  onTagRemove={onTagRemove}
                  onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
                  onChangeOthers={() => {}}
                  onChange={valueOut =>
                    handleChangeDataItem(UPDATE_KEY, {
                      name: MAPPING_FIELDS.DATA_DATE_TIME,
                      value: valueOut,
                    })
                  }
                  initData={infoData[MAPPING_FIELDS.DATA_DATE_TIME]}
                  enableShortLink={false}
                  width="100%"
                  isBlastCampaign={false}
                />
                {renderError(
                  MAPPING_FIELDS.DATA_DATE_TIME,
                  infoData[MAPPING_FIELDS.DATA_DATE_TIME],
                  false,
                  buttonId,
                  typeHandler === 'DEFAULT_ACTION',
                )}
              </InputPreview>
            </Grid>
          </GridContainer>
          <GridContainer container style={{ paddingBottom: 0 }}>
            <Grid item sm={3} className={getAdjustColumn('left')}>
              <WrapperCenterFlexCenter>
                <Title>
                  {getTranslateMessage(TRANSLATE_KEY._, 'Format')}
                  <span style={{ color: '#ff0000' }}> *</span>
                </Title>
              </WrapperCenterFlexCenter>
            </Grid>
            <Grid item sm={9} className={getAdjustColumn('right')}>
              <InputPreview
                isViewMode={isViewMode}
                type="input"
                value={formatSelected && formatSelected.value}
              >
                <UISelect
                  onlyParent
                  use="tree"
                  isSearchable={false}
                  options={FORMAT_DATE_TIME_OPTIONS}
                  value={formatSelected}
                  onChange={valueOut =>
                    handleResetAndChangeData({
                      buttonId,
                      callback,
                      infoData,
                      dataIn: valueOut,
                      type: typeHandler,
                      nameFieldChange: MAPPING_FIELDS.FORMAT,
                    })
                  }
                  fullWidthPopover
                  labelWidth="100%"
                />
              </InputPreview>
            </Grid>
          </GridContainer>
          <GridContainer container style={{ paddingBottom: 0 }}>
            <Grid item sm={3} className={getAdjustColumn('left')}>
              <WrapperCenterFlexCenter
                style={{
                  justifyContent: 'flex-start',
                }}
              >
                <Title
                  style={{
                    paddingTop: 6,
                  }}
                >
                  {getLabelDateTime('RANGE', formatValue)}
                </Title>
              </WrapperCenterFlexCenter>
            </Grid>
            <Grid item sm={9} className={getAdjustColumn('right')}>
              <DateOrTimeWrapper
                isDateTime={formatValue === FORMAT_DATE_TIME.DATE_TIME}
              >
                {renderDateTimeFormatType(formatValue || '')}
              </DateOrTimeWrapper>
            </Grid>
          </GridContainer>
          <GridContainer container style={{ paddingBottom: 0 }}>
            <Grid item sm={3} className={getAdjustColumn('left')}>
              <WrapperCenterFlexCenter>
                <Title>{getLabelDateTime('DEFAULT', formatValue)}</Title>
              </WrapperCenterFlexCenter>
            </Grid>
            <Grid item sm={9} className={getAdjustColumn('right')}>
              <DateOrTimeWrapper>
                {formatValue === FORMAT_DATE_TIME.DATE && (
                  <DateWrapper>
                    <LabelQuote>On</LabelQuote>
                    {isViewMode &&
                    !infoData[MAPPING_FIELDS.DEFAULT_DATE] ? null : (
                      <UIInputCalendar
                        value={infoData[MAPPING_FIELDS.DEFAULT_DATE]}
                        onChange={valueOut => {
                          let tmp = valueOut;
                          if (
                            isValidDate(valueOut) &&
                            (valueOut < TIMES_MIN || valueOut > TIMES_MAX)
                          )
                            tmp = new Date().getTime();

                          handleChangeDataItem(UPDATE_KEY, {
                            name: MAPPING_FIELDS.DEFAULT_DATE,
                            value: isValidDate(tmp) ? tmp : '',
                          });
                        }}
                        minDate={
                          infoData[MAPPING_FIELDS.DATE_RANGE] &&
                          infoData[MAPPING_FIELDS.DATE_RANGE].from
                            ? format(
                                new Date(
                                  infoData[MAPPING_FIELDS.DATE_RANGE].from,
                                ),
                                'yyyy-MM-dd',
                              )
                            : MIN_DATE
                        }
                        maxDate={
                          infoData[MAPPING_FIELDS.DATE_RANGE] &&
                          infoData[MAPPING_FIELDS.DATE_RANGE].to
                            ? format(
                                new Date(
                                  infoData[MAPPING_FIELDS.DATE_RANGE].to,
                                ),
                                'yyyy-MM-dd',
                              )
                            : MAX_DATE
                        }
                        isShowLabel={false}
                        isViewMode={isViewMode}
                        placeholder="MM/DD/YYYY"
                      />
                    )}
                  </DateWrapper>
                )}
                {formatValue === FORMAT_DATE_TIME.DATE_TIME && (
                  <DateTimeWrapper>
                    <DateWrapper>
                      <LabelQuote>At</LabelQuote>
                      {isViewMode &&
                      !(
                        infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME] &&
                        infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME].date
                      ) ? null : (
                        <UIInputCalendar
                          value={
                            infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME] &&
                            infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME].date
                          }
                          onChange={valueOut => {
                            let tmp = valueOut;
                            if (
                              isValidDate(valueOut) &&
                              (valueOut < TIMES_MIN || valueOut > TIMES_MAX)
                            )
                              tmp = new Date().getTime();

                            handleChangeDataItem(UPDATE_KEY, {
                              name: MAPPING_FIELDS.DEFAULT_DATE_TIME,
                              value: {
                                ...(infoData[
                                  MAPPING_FIELDS.DEFAULT_DATE_TIME
                                ] || {}),
                                date: isValidDate(tmp) ? tmp : '',
                              },
                            });
                          }}
                          minDate={
                            dtRange && dtRange.from && dtRange.from.date
                              ? format(
                                  new Date(dtRange.from.date),
                                  'yyyy-MM-dd',
                                )
                              : MIN_DATE
                          }
                          maxDate={
                            dtRange && dtRange.to && dtRange.to.date
                              ? format(new Date(dtRange.to.date), 'yyyy-MM-dd')
                              : MAX_DATE
                          }
                          isShowLabel={false}
                          isViewMode={isViewMode}
                          placeholder="MM/DD/YYYY"
                        />
                      )}
                    </DateWrapper>
                    <span style={{ whiteSpace: 'nowrap' }}>at</span>
                    <TimeWrapper>
                      <MuiPickersUtilsProvider utils={DateFnsUtils}>
                        {isViewMode &&
                        !(
                          infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME] &&
                          infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME].time
                        ) ? null : (
                          <UIInputTime
                            isViewMode={isViewMode}
                            keyboardIcon={<AccessTimeIcon />}
                            margin="normal"
                            id="time-picker"
                            value={
                              (infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME] &&
                                infoData[MAPPING_FIELDS.DEFAULT_DATE_TIME]
                                  .time) ||
                              null
                            }
                            onChange={valueOut =>
                              handleChangeDataItem(UPDATE_KEY, {
                                name: MAPPING_FIELDS.DEFAULT_DATE_TIME,
                                value: {
                                  ...(infoData[
                                    MAPPING_FIELDS.DEFAULT_DATE_TIME
                                  ] || {}),
                                  time: Date.parse(valueOut),
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              'aria-label': 'change time',
                            }}
                          />
                        )}
                      </MuiPickersUtilsProvider>
                    </TimeWrapper>
                  </DateTimeWrapper>
                )}
                {formatValue === FORMAT_DATE_TIME.TIME && (
                  <TimeWrapper style={{ maxWidth: 155 }}>
                    <LabelQuote>At</LabelQuote>
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      {isViewMode &&
                      !infoData[MAPPING_FIELDS.DEFAULT_TIME] ? null : (
                        <UIInputTime
                          isViewMode={isViewMode}
                          keyboardIcon={<AccessTimeIcon />}
                          margin="normal"
                          dis
                          id="time-picker"
                          value={infoData[MAPPING_FIELDS.DEFAULT_TIME] || null}
                          onChange={valueOut =>
                            handleChangeDataItem(UPDATE_KEY, {
                              name: MAPPING_FIELDS.DEFAULT_TIME,
                              value: Date.parse(valueOut),
                            })
                          }
                          KeyboardButtonProps={{
                            'aria-label': 'change time',
                          }}
                        />
                      )}
                    </MuiPickersUtilsProvider>
                  </TimeWrapper>
                )}
              </DateOrTimeWrapper>
            </Grid>
          </GridContainer>
        </Fragment>
      );
    }
    default: {
      return null;
    }
  }
}

ActionTypeMapItem.propTypes = {
  actionTypeActive: PropTypes.string.isRequired,
  typeHandler: PropTypes.string,
  infoData: PropTypes.object.isRequired,
  buttonId: PropTypes.string,
  isViewMode: PropTypes.bool,
  getErrMsgByType: PropTypes.func,
  renderError: PropTypes.func,
  isNewCustomTag: PropTypes.bool,
  useAllocatedCode: PropTypes.bool,
  moduleConfig: PropTypes.object,
  getLabelDateTime: PropTypes.func,
  callback: PropTypes.func,
  handleChangeDataItem: PropTypes.func,
  onTagRemove: PropTypes.func,
  onChangeExtraDataTagProperty: PropTypes.func,
};
ActionTypeMapItem.defaultProps = {
  actionTypeActive: '',
  infoData: {},
  buttonId: '',
  isViewMode: false,
  moduleConfig: {},
  typeHandler: 'BUTTON_ITEM',
  useAllocatedCode: false,
  getErrMsgByType: () => [],
  renderError: () => {},
  getLabelDateTime: () => {},
  callback: () => {},
  handleChangeDataItem: () => {},
};
export default memo(ActionTypeMapItem);
