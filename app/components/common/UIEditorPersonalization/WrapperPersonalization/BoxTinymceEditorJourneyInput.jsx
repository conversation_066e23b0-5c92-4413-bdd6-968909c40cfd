// Libraries
import { pick } from 'lodash';

export default function BoxTinymceEditorJourneyInput(props) {
  const { children } = props;

  const defaultCustomTagProps = pick(props, [
    'otherData',
    'journeyNodeTagProperties',
    'useAllocatedCode',
    'isNewCustomTag',
    'groupCodes',
    'moduleConfig',
    'isForceHideBtnPersonalization',
    'onTagRemove',
    'onChangeExtraDataTagProperty',
  ]);

  return children({ defaultCustomTagProps });
}
