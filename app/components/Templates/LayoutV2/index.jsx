/* eslint-disable indent */
// Libraries
import { get, merge } from 'lodash';
import React, { useCallback, useEffect, useRef } from 'react';
import { Layout, useLayoutStore } from '@antscorp/antsomi-ui';
import ForecastResult from 'containers/ForecastResult';
import PropTypes from 'prop-types';
import { updateValue } from '../../../redux/actions';
import { ConversationAnalytic } from '@antscorp/antsomi-genai';
// import { makeSelectTmpOwnerId } from '../../../modules/Dashboard/selector';
import { useDispatch, useSelector } from 'react-redux';
import { makeSelectDashboardMenuCodeActive } from '../../../modules/Dashboard/selector';
import { getAppCodeByMenuCode } from '../../../utils/web/permission';
import { getPortalId } from '../../../utils/web/cookie';
import {
  getPortalFormatDateTimeLong,
  getPortalLocaleLanguage,
  getPortalTimeZone,
} from '../../../utils/web/portalSetting';

export const LayoutV2 = props => {
  const { children, headerProps, ...restProps } = props;
  const dispatch = useDispatch();

  const setHeaderState = useLayoutStore(store => store.setHeaderState);

  const forecastResultRef = useRef(null);

  const onSelectAccount = accountId => {
    dispatch(updateValue('@@DASHBOARD_SET_OWNER_ID_FROM_DATA@@', accountId));
  };

  const menuCode = useSelector(makeSelectDashboardMenuCodeActive());

  const appCode = getAppCodeByMenuCode(menuCode);

  const portalId = getPortalId();

  const defaultHeaderProps = {
    accountSelection: {
      onSelectAccount,
    },
    helpConfig: {
      configs: {
        appCode: appCode,
        config: {
          p_timezone: getPortalTimeZone(),
          api_pid: portalId,
          p_f_longdatetime: getPortalFormatDateTimeLong(),
          user_language: getPortalLocaleLanguage(),
          embeddedData: {},
          INSIGHT_U_OGS: PORTAL_CONFIG.INSIGHT_U_OGS,
        },
      },
    },
  };

  const handleCallbackProcessingNotify = useCallback((type, data) => {
    switch (type) {
      case 'forecast_name': {
        const onSetupForecast = get(
          forecastResultRef,
          'current.onSetupForecast',
          () => {},
        );
        const rawData = get(data, 'row', {});

        if (typeof onSetupForecast === 'function') {
          onSetupForecast(rawData);
        }
        break;
      }
      default: {
        break;
      }
    }
  }, []);

  useEffect(() => {
    setHeaderState({
      rightContent: (
        <ConversationAnalytic
          drawerProps={{
            classNames: {
              content: 'antsomi-genai-drawer',
            },
          }}
        />
      ),
    });
  }, [setHeaderState]);

  return (
    <Layout
      {...restProps}
      headerProps={merge(defaultHeaderProps, headerProps)}
      processingNotificationProps={{
        callback: handleCallbackProcessingNotify,
      }}
    >
      {children}
      <ForecastResult ref={forecastResultRef} />
    </Layout>
  );
};

LayoutV2.propTypes = {
  children: PropTypes.node,
  changeOwnerBreadcrumb: PropTypes.func,
  headerProps: PropTypes.any,
};
