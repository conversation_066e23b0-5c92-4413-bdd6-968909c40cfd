import TRANSLATE_KEY from '../../../messages/constant';

export const CODE = {
  GREATER_THAN: 'greater_than',
  GREATER_THAN_EQUAL: 'greater_than_equal',
  LESS_THAN: 'less_than',
  LESS_THAN_EQUAL: 'less_than_equal',
  NOT_EQUALS: 'not_equals',
  EQUALS: 'equals',
  EXISTS: 'exists',
  NOT_EXISTS: 'not_exists',
  CONTAINS: 'contains',
  DOESNT_CONTAIN: 'doesnt_contain',
  START_WITH: 'start_with',
  NOT_START_WITH: 'not_start_with',
  END_WITH: 'end_with',
  NOT_END_WITH: 'not_end_with',
  BEFORE_DATE: 'before_date',
  AFTER_DATE: 'after_date',
  MATCHES: 'matches',
  NOT_MATCHES: 'not_matches',
  MATCHES_ALL: 'contains',
  INCLUDES: 'includes',
  DOESNT_INCLUDE: 'doesnt_include',
  BETWEEN: 'between',
  EQUAL_TIME_AGO: 'equal_time_ago',
  NOT_EQUAL_TIME_AGO: 'not_equal_time_ago',
  BEFORE_TIME_AGO: 'before_time_ago',
  AFTER_TIME_AGO: 'after_time_ago',
  BETWEEN_TIME_AGO: 'between_time_ago',
};

export { CODE as OPERATORS_CODE };
export const OPERATORS_OPTION = {
  GREATER_THAN: {
    label: 'is greater than',
    translateCode: TRANSLATE_KEY._OPERATOR_GREATER_THAN,
    value: 'greater_than',
  },
  GREATER_THAN_EQUAL: {
    label: 'is greater than or equal',
    translateCode: TRANSLATE_KEY._OPERATOR_GREATER_OR_EQUAL,
    value: 'greater_than_equal',
  },
  LESS_THAN: {
    label: 'is less than',
    translateCode: TRANSLATE_KEY._OPERATOR_LESS_THAN,
    value: 'less_than',
  },
  LESS_THAN_EQUAL: {
    label: 'is less than or equal',
    translateCode: TRANSLATE_KEY._OPERATOR_LESS_OR_EQUAL,
    value: 'less_than_equal',
  },
  NOT_EQUALS: {
    label: 'is not equal',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_EQUAL,
    value: 'not_equals',
  },
  EQUALS: {
    label: 'is equal',
    translateCode: TRANSLATE_KEY._OPERATOR_EQUAL,
    value: 'equals',
  },
  BETWEEN: {
    label: 'is between',
    translateCode: TRANSLATE_KEY._OPERATOR_BETWEEN,
    value: 'between',
  },
  EXISTS: {
    label: 'exists',
    translateCode: TRANSLATE_KEY._OPERATOR_EXIST,
    value: 'exists',
  },
  NOT_EXISTS: {
    label: 'does not exist',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_EXIST,
    value: 'not_exists',
  },
  CONTAINS: {
    label: 'contains',
    translateCode: TRANSLATE_KEY._OPERATOR_CONTAIN,
    value: 'contains',
  },
  DOESNT_CONTAIN: {
    label: 'does not contain',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_CONTAIN,
    value: 'doesnt_contain',
  },
  START_WITH: {
    label: 'starts with',
    translateCode: TRANSLATE_KEY._OPERATOR_START_WITH,
    value: 'start_with',
  },
  NOT_START_WITH: {
    label: "doesn't start with",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_START_WITH,
    value: 'not_start_with',
  },
  END_WITH: {
    label: 'ends with',
    translateCode: TRANSLATE_KEY._OPERATOR_END_WITH,
    value: 'end_with',
  },
  NOT_END_WITH: {
    label: "doesn't end with",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_END_WITH,
    value: 'not_end_with',
  },
  BEFORE_DATE: {
    label: 'is before date',
    translateCode: TRANSLATE_KEY._OPERATOR_BEFORE_DATE,
    value: 'before_date',
  },
  AFTER_DATE: {
    label: 'is after date',
    translateCode: TRANSLATE_KEY._OPERATOR_AFTER_DATE,
    value: 'after_date',
  },
  EQUAL_TIME_AGO: {
    label: 'is equal time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_EQUAL_TIME_AGO,
    value: 'equal_time_ago',
  },
  NOT_EQUAL_TIME_AGO: {
    label: "isn't equal time ago",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_EQUAL_TIME_AGO,
    value: 'not_equal_time_ago',
  },
  BEFORE_TIME_AGO: {
    label: 'is before time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_BEFORE_TIME_AGO,
    value: 'before_time_ago',
  },
  AFTER_TIME_AGO: {
    label: 'is after time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_AFTER_TIME_AGO,
    value: 'after_time_ago',
  },
  BETWEEN_TIME_AGO: {
    label: 'is between time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_BETWEEN_TIME_AGO,
    value: 'between_time_ago',
  },
  MATCHES: {
    label: 'matches any of',
    translateCode: TRANSLATE_KEY._OPERATOR_MATCH_ANY,
    value: 'matches',
  },
  NOT_MATCHES: {
    label: "doesn't match any of",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_MATCH_ANY,
    value: 'not_matches',
  },
  MATCHES_ALL: {
    label: 'match all of',
    translateCode: TRANSLATE_KEY._OPERATOR_MATCH_ALL,
    value: 'contains',
  },
  INCLUDES: {
    label: 'includes',
    translateCode: TRANSLATE_KEY._OPERATOR_INCLUDE,
    value: 'includes',
  },
  DOESNT_INCLUDE: {
    label: 'does not include',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_INCLUDE,
    value: 'doesnt_include',
  },
};

export const OPERATORS = {
  number: {
    list: [
      OPERATORS_OPTION.GREATER_THAN,
      OPERATORS_OPTION.GREATER_THAN_EQUAL,
      OPERATORS_OPTION.LESS_THAN,
      OPERATORS_OPTION.LESS_THAN_EQUAL,
      OPERATORS_OPTION.BETWEEN,
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.CONTAINS,
      CODE.DOESNT_CONTAIN,
      CODE.LESS_THAN,
      CODE.LESS_THAN_EQUAL,
      CODE.BETWEEN,
      CODE.NOT_EQUALS,
      CODE.EQUALS,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  string: {
    list: [
      OPERATORS_OPTION.CONTAINS,
      OPERATORS_OPTION.DOESNT_CONTAIN,
      OPERATORS_OPTION.START_WITH,
      OPERATORS_OPTION.NOT_START_WITH,
      OPERATORS_OPTION.END_WITH,
      OPERATORS_OPTION.NOT_END_WITH,
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.CONTAINS,
      CODE.DOESNT_CONTAIN,
      CODE.START_WITH,
      CODE.NOT_START_WITH,
      CODE.END_WITH,
      CODE.NOT_END_WITH,
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  suggestion: {
    list: [
      OPERATORS_OPTION.CONTAINS,
      OPERATORS_OPTION.DOESNT_CONTAIN,
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.START_WITH,
      OPERATORS_OPTION.NOT_START_WITH,
      OPERATORS_OPTION.END_WITH,
      OPERATORS_OPTION.NOT_END_WITH,
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.CONTAINS,
      CODE.DOESNT_CONTAIN,
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.START_WITH,
      CODE.NOT_START_WITH,
      CODE.END_WITH,
      CODE.NOT_END_WITH,
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  text: {
    list: [
      OPERATORS_OPTION.CONTAINS,
      OPERATORS_OPTION.DOESNT_CONTAIN,
      OPERATORS_OPTION.START_WITH,
      OPERATORS_OPTION.NOT_START_WITH,
      OPERATORS_OPTION.END_WITH,
      OPERATORS_OPTION.NOT_END_WITH,
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.CONTAINS,
      CODE.DOESNT_CONTAIN,
      CODE.START_WITH,
      CODE.NOT_START_WITH,
      CODE.END_WITH,
      CODE.NOT_END_WITH,
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  datetime: {
    list: [
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.BEFORE_DATE,
      OPERATORS_OPTION.AFTER_DATE,
      OPERATORS_OPTION.BETWEEN,
      OPERATORS_OPTION.EQUAL_TIME_AGO,
      OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
      OPERATORS_OPTION.BEFORE_TIME_AGO,
      OPERATORS_OPTION.AFTER_TIME_AGO,
      OPERATORS_OPTION.BETWEEN_TIME_AGO,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.BEFORE_DATE,
      CODE.AFTER_DATE,
      CODE.BETWEEN,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
      CODE.EQUAL_TIME_AGO,
      CODE.NOT_EQUAL_TIME_AGO,
      CODE.BEFORE_TIME_AGO,
      CODE.AFTER_TIME_AGO,
      CODE.BETWEEN_TIME_AGO,
    ]),
  },
  date: {
    list: [
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.BEFORE_DATE,
      OPERATORS_OPTION.AFTER_DATE,
      OPERATORS_OPTION.EQUAL_TIME_AGO,
      OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
      OPERATORS_OPTION.BEFORE_TIME_AGO,
      OPERATORS_OPTION.AFTER_TIME_AGO,
      OPERATORS_OPTION.BETWEEN_TIME_AGO,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.BEFORE_DATE,
      CODE.AFTER_DATE,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
      CODE.EQUAL_TIME_AGO,
      CODE.NOT_EQUAL_TIME_AGO,
      CODE.BEFORE_TIME_AGO,
      CODE.AFTER_TIME_AGO,
      CODE.BETWEEN_TIME_AGO,
    ]),
  },
  object_id: {
    list: [
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  array: {
    list: [
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  array_number: {
    list: [
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  array_string: {
    list: [
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  array_datetime: {
    list: [
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  array_text: {
    list: [
      OPERATORS_OPTION.MATCHES,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([
      CODE.MATCHES,
      CODE.NOT_MATCHES,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
    ]),
  },
  unique: {
    list: [OPERATORS_OPTION.EQUALS, OPERATORS_OPTION.NOT_EQUALS],
    set: new Set([CODE.EQUALS, CODE.NOT_EQUALS]),
  },
  boolean: {
    list: [
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
    ],
    set: new Set([CODE.EQUALS, CODE.NOT_EQUALS, CODE.EXISTS, CODE.NOT_EXISTS]),
  },
  object: {
    list: [OPERATORS_OPTION.EXISTS, OPERATORS_OPTION.NOT_EXISTS],
    set: new Set([CODE.EXISTS, CODE.NOT_EXISTS]),
  },
  datetime_v2_p1: {
    list: [
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
      OPERATORS_OPTION.NOT_MATCHES,
      OPERATORS_OPTION.MATCHES,
    ],
    set: new Set([
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
      CODE.NOT_MATCHES,
      CODE.MATCHES,
    ]),
  },
  datetime_v2_p2: {
    list: [
      OPERATORS_OPTION.EQUALS,
      OPERATORS_OPTION.NOT_EQUALS,
      OPERATORS_OPTION.EXISTS,
      OPERATORS_OPTION.NOT_EXISTS,
      OPERATORS_OPTION.BEFORE_DATE,
      OPERATORS_OPTION.AFTER_DATE,
      OPERATORS_OPTION.BETWEEN,
    ],
    set: new Set([
      CODE.EQUALS,
      CODE.NOT_EQUALS,
      CODE.EXISTS,
      CODE.NOT_EXISTS,
      CODE.BEFORE_DATE,
      CODE.BETWEEN,
      CODE.AFTER_DATE,
    ]),
  },
};

export const MAP_OPERATORS = {
  'datetime_v2_p2-equals': OPERATORS_OPTION.EQUALS,
  'datetime_v2_p2-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'datetime_v2_p2-exists': OPERATORS_OPTION.EXISTS,
  'datetime_v2_p2-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'datetime_v2_p2-before_date': OPERATORS_OPTION.BEFORE_DATE,
  'datetime_v2_p2-after_date': OPERATORS_OPTION.AFTER_DATE,
  'datetime_v2_p2-between': OPERATORS_OPTION.BETWEEN,
  'datetime_v2_p1-equals': OPERATORS_OPTION.EQUALS,
  'datetime_v2_p1-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'datetime_v2_p1-exists': OPERATORS_OPTION.EXISTS,
  'datetime_v2_p1-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'datetime_v2_p1-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'datetime_v2_p1-matches': OPERATORS_OPTION.MATCHES,
  'datetime_v2_p1-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,
  'datetime_v2_p1-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'datetime_v2_p2-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,
  'datetime_v2_p2-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'number-greater_than': OPERATORS_OPTION.GREATER_THAN,
  'number-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,
  'number-less_than': OPERATORS_OPTION.LESS_THAN,
  'number-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'number-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'number-equals': OPERATORS_OPTION.EQUALS,
  'number-exists': OPERATORS_OPTION.EXISTS,
  'number-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'string-contains': OPERATORS_OPTION.CONTAINS,
  'number-between': OPERATORS_OPTION.BETWEEN,
  'string-between': OPERATORS_OPTION.BETWEEN,
  'string-less_than': OPERATORS_OPTION.LESS_THAN,
  'string-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'string-greater_than': OPERATORS_OPTION.GREATER_THAN,
  'string-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,
  'string-doesnt_contain': OPERATORS_OPTION.DOESNT_CONTAIN,
  'string-start_with': OPERATORS_OPTION.START_WITH,
  'string-end_with': OPERATORS_OPTION.END_WITH,
  'string-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'string-equals': OPERATORS_OPTION.EQUALS,
  'string-exists': OPERATORS_OPTION.EXISTS,
  'string-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'text-contains': OPERATORS_OPTION.CONTAINS,
  'text-doesnt_contain': OPERATORS_OPTION.DOESNT_CONTAIN,
  'text-start_with': OPERATORS_OPTION.START_WITH,
  'text-end_with': OPERATORS_OPTION.END_WITH,
  'text-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'text-equals': OPERATORS_OPTION.EQUALS,
  'text-exists': OPERATORS_OPTION.EXISTS,
  'text-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'text-between': OPERATORS_OPTION.BETWEEN,
  'text-less_than': OPERATORS_OPTION.LESS_THAN,
  'text-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'text-greater_than': OPERATORS_OPTION.GREATER_THAN,
  'text-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,

  // 'datetime-contains': OPERATORS_OPTION.EQUALS, // hot fix date time operator = containts
  'datetime-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'datetime-matches': OPERATORS_OPTION.MATCHES,
  'datetime-equals': OPERATORS_OPTION.EQUALS,
  'datetime-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'datetime-before_date': OPERATORS_OPTION.BEFORE_DATE,
  'datetime-after_date': OPERATORS_OPTION.AFTER_DATE,
  'datetime-exists': OPERATORS_OPTION.EXISTS,
  'datetime-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'datetime-equal_time_ago': OPERATORS_OPTION.EQUAL_TIME_AGO,
  'datetime-not_equal_time_ago': OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
  'datetime-before_time_ago': OPERATORS_OPTION.BEFORE_TIME_AGO,
  'datetime-after_time_ago': OPERATORS_OPTION.AFTER_TIME_AGO,
  'datetime-between_time_ago': OPERATORS_OPTION.BETWEEN_TIME_AGO,
  'datetime-between': OPERATORS_OPTION.BETWEEN,
  'datetime-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,
  'datetime-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'date-equals': OPERATORS_OPTION.EQUALS,
  'date-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'date-before_date': OPERATORS_OPTION.BEFORE_DATE,
  'date-after_date': OPERATORS_OPTION.AFTER_DATE,
  'date-exists': OPERATORS_OPTION.EXISTS,
  'date-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'date-equal_time_ago': OPERATORS_OPTION.EQUAL_TIME_AGO,
  'date-not_equal_time_ago': OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
  'date-before_time_ago': OPERATORS_OPTION.BEFORE_TIME_AGO,
  'date-after_time_ago': OPERATORS_OPTION.AFTER_TIME_AGO,
  'date-between_time_ago': OPERATORS_OPTION.BETWEEN_TIME_AGO,
  'object_id-matches': OPERATORS_OPTION.MATCHES,
  'object_id-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'object_id-exists': OPERATORS_OPTION.EXISTS,
  'object_id-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'array-includes': OPERATORS_OPTION.MATCHES,
  'array-doesnt_include': OPERATORS_OPTION.NOT_MATCHES,
  'object_id-start_with': OPERATORS_OPTION.START_WITH,
  'object_id-end_with': OPERATORS_OPTION.END_WITH,
  'object_id-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'object_id-equals': OPERATORS_OPTION.EQUALS,
  'object_id-contains': OPERATORS_OPTION.CONTAINS,
  'object_id-doesnt_contain': OPERATORS_OPTION.DOESNT_CONTAIN,
  'object_id-greater_than': OPERATORS_OPTION.GREATER_THAN,
  'object_id-greater_than_equal': OPERATORS_OPTION.GREATER_THAN_EQUAL,
  'object_id-less_than': OPERATORS_OPTION.LESS_THAN,
  'object_id-less_than_equal': OPERATORS_OPTION.LESS_THAN_EQUAL,
  'object_id-between': OPERATORS_OPTION.BETWEEN,
  'array-exists': OPERATORS_OPTION.EXISTS,
  'array-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'array_number-includes': OPERATORS_OPTION.MATCHES,
  'array_number-doesnt_include': OPERATORS_OPTION.NOT_MATCHES,
  'array_number-exists': OPERATORS_OPTION.EXISTS,
  'array_number-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'array_string-includes': OPERATORS_OPTION.MATCHES,
  'array_string-doesnt_include': OPERATORS_OPTION.NOT_MATCHES,
  'array_string-exists': OPERATORS_OPTION.EXISTS,
  'array_string-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'array_datetime-includes': OPERATORS_OPTION.MATCHES,
  'array_datetime-doesnt_include': OPERATORS_OPTION.NOT_MATCHES,
  'array_datetime-exists': OPERATORS_OPTION.EXISTS,
  'array_datetime-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'array_text-includes': OPERATORS_OPTION.MATCHES,
  'array_text-doesnt_include': OPERATORS_OPTION.NOT_MATCHES,
  'array_text-exists': OPERATORS_OPTION.EXISTS,
  'array_text-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'unique-equals': OPERATORS_OPTION.EQUALS,
  'unique-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'boolean-matches': OPERATORS_OPTION.MATCHES,
  'boolean-exists': OPERATORS_OPTION.EXISTS,
  'boolean-not_exists': OPERATORS_OPTION.NOT_EXISTS,
  'boolean-equals': OPERATORS_OPTION.EQUALS,
  'boolean-not_equals': OPERATORS_OPTION.NOT_EQUALS,
  'object-exists': OPERATORS_OPTION.EXISTS,
  'object-not_exists': OPERATORS_OPTION.NOT_EXISTS,

  'number-matches': OPERATORS_OPTION.MATCHES,
  'number-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'string-matches': OPERATORS_OPTION.MATCHES,
  'string-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'text-matches': OPERATORS_OPTION.MATCHES,
  'text-not_matches': OPERATORS_OPTION.NOT_MATCHES,

  'array-matches': OPERATORS_OPTION.MATCHES,
  'array-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'array_number-matches': OPERATORS_OPTION.MATCHES,
  'array_number-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'array_number-contains': OPERATORS_OPTION.MATCHES_ALL,
  'array_string-matches': OPERATORS_OPTION.MATCHES,
  'array_string-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'array_datetime-matches': OPERATORS_OPTION.MATCHES,
  'array_datetime-not_matches': OPERATORS_OPTION.NOT_MATCHES,
  'array_text-matches': OPERATORS_OPTION.MATCHES,
  'array_text-not_matches': OPERATORS_OPTION.NOT_MATCHES,

  'string-not_start_with': OPERATORS_OPTION.NOT_START_WITH,
  'string-not_end_with': OPERATORS_OPTION.NOT_END_WITH,
  'text-not_start_with': OPERATORS_OPTION.NOT_START_WITH,
  'text-not_end_with': OPERATORS_OPTION.NOT_END_WITH,
  'object_id-not_start_with': OPERATORS_OPTION.NOT_START_WITH,
  'object_id-not_end_with': OPERATORS_OPTION.NOT_END_WITH,
};

// let map = {};
// Object.keys(OPERATORS).forEach(key => {
//   OPERATORS[key].forEach(item => {
//     map[`${key}-${item.value}`] = item;
//   });
// });
// JSON.stringify(map)

// function toMap() {
//   const map = {};
//   Object.keys(OPERATORS).forEach(key => {
//     OPERATORS[key].forEach(item => {
//       map[`${key}-${item.value}`] = item;
//     });
//   });
//
// }

// function toCode() {
//   const map = {};
//   Object.keys(OPERATORS).forEach(key => {
//     OPERATORS[key].forEach(item => {
//       map[`${item.value.toUpperCase()}`] = item.value;
//     });
//   });
//
// }
