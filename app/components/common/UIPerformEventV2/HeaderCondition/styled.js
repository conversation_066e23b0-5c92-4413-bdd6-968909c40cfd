import { THEME } from '@antscorp/antsomi-ui/es/constants';
import styled from 'styled-components';
export const WrapperHeaderGroupCondition = styled.div`
  display: flex;
  background: rgb(247 247 247);
  padding: 10px 20px;
  justify-content: space-between;
  align-items: center;
`;

export const Title = styled.div`
  color: #000000;
`;

export const WrapperIcon = styled.div`
  &.refine-close {
    position: relative;
    top: 16px;
    transform: translateY(-50%);
  }

  &.icon-remove {
    color: ${THEME.token?.colorPrimary};
    font-size: 16px;
    cursor: pointer;
  }

  span {
    cursor: pointer;
    display: block;
    color: ${props => (props.isRFM ? '#005eb8' : '#7f7f7f')};
    font-size: ${props => (props.isRFM ? '18px' : '20px')};
  }
`;
