// Libraries
import React, { useEffect, useState } from 'react';
import { UIButton } from '@xlab-team/ui-components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  DRAWER_DETAIL_DIMENSION,
  DrawerDetail,
  EditableName,
} from '@antscorp/antsomi-ui';
import propsTypes from 'prop-types';
import { isEmpty } from 'lodash';
import { useHistory } from 'react-router-dom';

// Components
import CreateCollectionModule from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Create/Loadable';
import UIHelmet from '../../../components/Templates/LayoutContent/Helmet';

// Styled
import { BoxHeaderRight, WrapperBody, WrapperHeader } from './styled';

// Actions
import { updateValue } from '../../../redux/actions';

// Selectors
import { makeSelectIsOpenDrawerCreateCollection } from '../../../modules/Dashboard/ApiHub/BusinessObject/Main/selectors';
import { makeSelectConfigCreateSegment } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Create/selectors';

// Constants
import { MODULE_CONFIG } from '../../../modules/Dashboard/ApiHub/BusinessObject/Main/config';
import { BUTTON_COLLECTION_SAVE_ID } from './constants';
import { MODULE_CONFIG as MODULE_CONFIG_COLLECTION } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Create/config';

const DrawerCreateCollection = props => {
  const { mainConfigCreateSegment, dispatchChangeName } = props;
  const [browserTitle, setBrowserTitle] = useState('');

  const history = useHistory();

  const searchParams = new URLSearchParams(window.location.search);

  const onCloseDrawer = () => {
    if (searchParams.get('copyId')) {
      // If the drawer is opened from a copy action, we need to remove the copyId
      searchParams.delete('copyId');
      searchParams.delete('name');

      history.push({
        search: searchParams.toString(),
      });
    }
    props.toggleIsOpenDrawerCreateCollection(false);
  };

  const { main } = mainConfigCreateSegment;

  useEffect(() => {
    setBrowserTitle(main?.name);
  }, [searchParams.get('name')]);

  const onBlurName = () => {
    if (isEmpty(main?.name)) {
      setBrowserTitle('Create new collection');
    } else {
      setBrowserTitle(main?.name);
    }
  };

  return (
    <DrawerDetail
      open={props.isOpenDrawerCreateCollection}
      menuProps={{
        show: false,
        showExpandButton: false,
      }}
      minWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.minWidth}
      maxWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
      destroyOnClose
      headerProps={{
        children: (
          <WrapperHeader>
            <EditableName
              value={main?.name}
              required
              onChange={name => {
                dispatchChangeName(name);
              }}
              error={main.errorName}
              onBlur={onBlurName}
            />
            <BoxHeaderRight>
              <UIButton theme="outline" onClick={onCloseDrawer}>
                Cancel
              </UIButton>

              <div id={BUTTON_COLLECTION_SAVE_ID} />
            </BoxHeaderRight>
          </WrapperHeader>
        ),
        showBorderBottom: true,
        height: '50px ',
        style: {
          padding: '0 15px',
        },
      }}
    >
      <WrapperBody>
        <UIHelmet title={browserTitle} />
        <CreateCollectionModule design="create" isUIV2 />
      </WrapperBody>
    </DrawerDetail>
  );
};

const mapStateToProps = createStructuredSelector({
  isOpenDrawerCreateCollection: makeSelectIsOpenDrawerCreateCollection(),
  mainConfigCreateSegment: makeSelectConfigCreateSegment(),
});

const mapDispatchToProps = dispatch => {
  return {
    toggleIsOpenDrawerCreateCollection: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_IS_OPEN_DRAWER_CREATE_COLLECTION@@`,
          params,
        ),
      );
    },
    dispatchChangeName: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG_COLLECTION.key}@@SEGMENT_NAME@@`, value),
      ),
  };
};

DrawerCreateCollection.propTypes = {
  isOpenDrawerCreateCollection: propsTypes.bool,
  toggleIsOpenDrawerCreateCollection: propsTypes.func,
  mainConfigCreateSegment: propsTypes.object,
  dispatchChangeName: propsTypes.func,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(DrawerCreateCollection);
