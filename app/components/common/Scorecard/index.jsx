/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import _isEmpty from 'lodash/isEmpty';
import {
  UIIconButton as IconButton,
  UITextSearch as TextSearch,
  UINodata as Nodata,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import Popover from '@material-ui/core/Popover';
import UISelect from 'components/form/UISelectCondition';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import {
  WrapperContent,
  WrapperItem,
  Label,
  Score,
  WrapperButton,
  ListContent,
  Item,
  StyledPopover,
} from './styles';
import JourneyServices from 'services/Journey';

import { WrapperSearch } from '../UIDiagramsEvent/styled';
import { useCallback } from 'react';
import { addMessageToQueue } from '../../../utils/web/queue';
import { InputSearch } from '@antscorp/antsomi-ui';
const styleDropdown = {
  width: '10rem',
};

function Scorecard(props) {
  const {
    optionScoreCard,
    defaultOptionScoreCard = [],
    data,
    objectType,
    isLoading,
  } = props;
  const [anchorEl, setAnchorEl] = useState(null);
  const [state, setState] = useImmer({
    listScoreCard: defaultOptionScoreCard,
    valueSearch: '',
    listSearch: [],
  });
  useEffect(() => {
    setState(draft => {
      draft.listScoreCard = defaultOptionScoreCard;
    });
  }, [isLoading]);
  // useEffect(() => {
  //   setState(draft => {
  //     draft.listScoreCard = defaultOptionScoreCard;
  //   });
  // }, []);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover-action-label' : undefined;
  const handleClose = () => {
    setAnchorEl(null);
    setState(draft => {
      draft.valueSearch = '';
      draft.listSearch = [];
    });
  };
  const handleOpenOption = event => {
    setAnchorEl(event.currentTarget);
  };
  const onSearch = value => {
    let tempState = [...optionScoreCard];
    if (value.length > 0) {
      tempState = tempState.filter(item => {
        if (item.label.toLowerCase().includes(value.trim().toLowerCase())) {
          return item;
        }
      });
    }
    setState(draft => {
      draft.valueSearch = value;
      draft.listSearch = tempState;
    });
  };
  const onClickItem = item => {
    const tempState = [...state.listScoreCard];
    tempState.push(item.value);
    setState(draft => {
      draft.listScoreCard = tempState;
    });
    fetchListScoreCardAPI(tempState);

    handleClose();
  };
  const onDeleteItem = value => {
    const tempState = [...state.listScoreCard];
    const index = state.listScoreCard.findIndex(each => each === value);
    if (index !== -1) {
      tempState.splice(index, 1);
      setState(draft => {
        draft.listScoreCard = tempState;
      });
    }
    fetchListScoreCardAPI(tempState);
  };
  const fetchListScoreCardAPI = useCallback(scroreData => {
    const params = {
      data: {
        type: objectType,
        properties: scroreData,
      },
    };
    setState(draft => {
      draft.isLoading = true;
    });
    JourneyServices.actionsHistory
      .updateScoreCard(params)
      .then(res => {
        if (res.code === 200 && !_isEmpty(res.data)) {
        } else {
        }
      })
      .catch(err => {
        if (!err.isCanceled) {
          addMessageToQueue({
            path: 'app/components/common/Scorecard/index.jsx',
            func: 'fetchListScoreCardAPI',
            data: err.stack,
          });
          console.warn('err', err);
        }
      });
  });
  const renderContent = () => {
    if (data.length <= 0) {
      return (
        <Nodata height="150px">
          {getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
        </Nodata>
      );
    }
    return (
      <WrapperContent>
        {!isLoading &&
          state.listScoreCard.length > 0 &&
          state.listScoreCard.map((each, index) => {
            const scoreCount = data.filter(tmp => tmp.status === each);
            const label = optionScoreCard.filter(tmp => tmp.value === each);
            return (
              <WrapperItem>
                <Label>{label[0].label}</Label>
                <Score>{parseInt(scoreCount[0].count)}</Score>
                <div className="icon-delete">
                  <IconButton
                    onClick={() => onDeleteItem(each)}
                    iconName="delete"
                    size="20px"
                    color="#005eb8"
                  />
                </div>
              </WrapperItem>
            );
          })}
        {state.listScoreCard.length < optionScoreCard.length && (
          <WrapperDisable>
            <WrapperButton onClick={handleOpenOption}>
              + Add scorecard
            </WrapperButton>
          </WrapperDisable>
        )}
        <StyledPopover
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          // anchorPosition={{ left: -100, top: -400 }}
        >
          <WrapperSearch
            style={{ padding: '10px 10px 0 10px' }}
            className="wrapper-search"
          >
            <InputSearch
              classDiv="search-filter"
              placeholder="Search..."
              // translateCode={TRANSLATE_KEY._ACT_SEARCH}
              // isTimeout
              onChange={e => onSearch(e.target.value)}
              value={state.valueSearch}
              // focus
              // type="text"
              // use="full-border"
            />
          </WrapperSearch>

          <ListContent>
            {(state.valueSearch ? state.listSearch : optionScoreCard).map(
              (each, index) => (
                <WrapperDisable
                  disabled={
                    state.listScoreCard.findIndex(tmp => tmp === each.value) !==
                    -1
                  }
                  key={each.value}
                >
                  <Item onClick={() => onClickItem(each)}>{each.label}</Item>
                </WrapperDisable>
              ),
            )}
          </ListContent>
        </StyledPopover>
      </WrapperContent>
    );
  };

  return renderContent();
}

Scorecard.propTypes = {
  callback: PropTypes.func,
};

Scorecard.defaultProps = {
  callback: () => {},
};

export default memo(Scorecard);
