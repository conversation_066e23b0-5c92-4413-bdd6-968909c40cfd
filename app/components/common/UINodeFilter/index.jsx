/* eslint-disable indent */
/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import { UILoading } from '@xlab-team/ui-components';

// Components
import SelectTypeFilter from './_UI/SelectTypeFilter';
import WrapperAudienceAttributes from './_UI/WrapperAudienceAttribute';
import TargetAudienceMultiFilter from '../../Templates/TargetAudienceFilter';
import UIPerformEventV2 from '../UIPerformEventV2';

// Styled
import { BlockContentWrapper, ContainerNodeContent, Title } from './styled';

// Utils
import {
  FILTER_TYPE,
  initDataByTypeFilter,
  initDataUIFilter,
  MAP_DATA_OPTIONS_FILTER,
  mapDataResponseListEvents,
  toConditionPerfEventUI,
} from './utils';
import { generateKey, safeParse } from '../../../utils/common';

// Hooks

import useUpdateEffect from '../../../hooks/useUpdateEffect';
import { isEmpty } from 'lodash';

const UINodeFilter = props => {
  const { initData, isViewMode, tagsAndEvents } = props;
  const [state, setState] = useImmer(initDataUIFilter());
  const [stateLocal, setStateLocal] = useImmer({
    isInitDone: false,
    initKey: null,
  });

  const responseGetListEvents = useMemo(() => {
    return mapDataResponseListEvents(tagsAndEvents?.events?.list);
  }, [tagsAndEvents?.events?.list]);

  useEffect(() => {
    // console.log('props.componentId', props.componentId, initData);

    setStateLocal(draft => {
      draft.isInitDone = false;
      draft.initKey = generateKey();
    });

    return () => {
      setState(() => initDataUIFilter());
      setStateLocal(draft => {
        draft.isInitDone = false;
        draft.initKey = null;
      });
    };
  }, [props.componentId]);

  useUpdateEffect(() => {
    if (stateLocal.initKey !== null) {
      if (Object.keys(safeParse(initData, {})).length > 0) {
        const filterType = safeParse(initData.filterType, {});

        setState(draft => {
          draft.isFetchInfoData = true;
          draft.filterType = MAP_DATA_OPTIONS_FILTER[filterType.value];
          if (initData.event_attribute) {
            draft.event_attribute = initData.event_attribute;
          }
          if (initData.item_segment) {
            draft.item_segment = initData.item_segment;
          }
          if (initData.customer_attributes) {
            draft.user_attributes = initData.customer_attribute;
          }
        });
      }
      setStateLocal(draft => {
        draft.isInitDone = true;
      });
    }
  }, [stateLocal.initKey]);

  useUpdateEffect(() => {
    props.onChange(state);
  }, [state]);

  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };

  const onChangeTypeFilter = (key, data) => {
    if (key) {
      setStateCommon({
        [`${key}`]: MAP_DATA_OPTIONS_FILTER[data] || {},
        [`${data}`]: initDataByTypeFilter[data],
      });
    } else {
      // Reset state when removing filter
      setStateCommon({
        ...initDataUIFilter(),
        isRealtime: true,
      });
    }
  };

  const onChange = (type, data) => {
    if (type === 'user_attribute' || type === 'customer_attribute') {
      setStateCommon({ user_attributes: data });
    } else {
      setStateCommon({ [type]: data });
    }
  };

  const onChangeSegment = data => {
    if (
      !isEmpty(data.currentData.includeCluster) ||
      !isEmpty(data.currentData.excludeCluster)
    ) {
      setState(draft => {
        draft.item_segment = data;
      });
    } else {
      setStateCommon({
        ...initDataUIFilter(),
        isRealtime: true,
      });
    }
  };

  const onChangePerformEvent = data => {
    setState(draft => {
      draft.event_attribute = {
        rules: data,
      };
    });
  };

  const renderContentByTypeDelay = () => {
    switch (state.filterType.value) {
      case FILTER_TYPE.TARGET_AUDIENCE: {
        return (
          <div style={{ maxWidth: '50rem' }}>
            <TargetAudienceMultiFilter
              itemTypeId={props.itemTypeId}
              initData={
                Object.keys(safeParse(props.initData, {})).length === 0
                  ? undefined
                  : props.initData.item_segment
              }
              onChange={onChangeSegment}
              validateKey={props.validateKey}
              componentId={stateLocal.initKey}
              isViewMode={isViewMode}
              isScheduledTrigger={props.triggerType === 'SCHEDULED'}
              channelActive={props.channelActive}
              triggerTargetAudienceId={props.triggerTargetAudienceId}
            />
          </div>
        );
      }

      case FILTER_TYPE.VISITOR_ATTRIBUTE:
      case FILTER_TYPE.CUSTOMER_ATTRIBUTE: {
        return (
          <WrapperAudienceAttributes
            onChange={onChange}
            initData={
              Object.keys(safeParse(props.initData, {})).length === 0
                ? undefined
                : props.initData.user_attributes
            }
            isInit={
              Object.keys(safeParse(props.initData, {})).length === 0
                ? false
                : props.initData.isInitUserAttrs
            }
            validateKey={props.validateKey}
            componentId={stateLocal.initKey}
            isShowFullOption={props.triggerType !== 'SCHEDULED'}
            disabled={props.disabled}
            isViewMode={isViewMode}
            itemTypeId={
              state.filterType.value === FILTER_TYPE.CUSTOMER_ATTRIBUTE
                ? '-1003'
                : '-1007'
            }
            triggerTargetAudienceId={props.triggerTargetAudienceId}
          />
        );
      }

      case FILTER_TYPE.PERFORMED_EVENT: {
        let dataRules;

        if (
          props.initData.event_attribute &&
          props.initData.event_attribute.rules
        ) {
          dataRules = props.initData.event_attribute.rules;
        }

        const initDataPerformEvent = dataRules?.get('data-init')
          ? toConditionPerfEventUI(
              dataRules.get('data-init')?.toJS(),
              props.eventValue,
              props.insightPropertyIds,
              props.triggerNodeId,
            )
          : dataRules;

        return (
          <>
            <UIPerformEventV2
              translateLabelTitle="_TITL_WAITING_EVENT"
              moduleConfig={props.moduleConfig}
              validateKey={props.validateKey}
              onChange={onChangePerformEvent}
              initData={initDataPerformEvent}
              componentId={props.componentId}
              showCustomTitle={false}
              showTitle={false}
              hiddenDatasources
              hiddenTitle
              disabledEvent={false}
              disabledEventConditions={false}
              isMultiple
              isViewMode={isViewMode}
              labelPerfEvent="Perform event this Journey"
              showHeader
              isShowAnd
              limitGroups={20}
              getFullEventTrackingService={() => responseGetListEvents}
              sourceTags={props.sourceTags}
              isUsingCustomerJourney
              triggerTargetAudienceId={props.triggerTargetAudienceId}
              isHideSource
              channelActive={props.channelActive}
            />
          </>
        );
      }

      default:
        return null;
    }
  };

  if (!stateLocal.isInitDone) {
    return <UILoading isLoading />;
  }

  return (
    <ContainerNodeContent>
      <SelectTypeFilter
        onChange={onChangeTypeFilter}
        initData={state.filterType}
        componentId={stateLocal.initKey}
        isActionBase={props.triggerType !== 'SCHEDULED'}
        disabled={props.disabled}
        design={props.design}
        isViewMode={isViewMode}
        itemTypeId={props.itemTypeId}
        channelActive={props.channelActive}
        triggerTargetAudienceId={props.triggerTargetAudienceId}
      />
      <BlockContentWrapper alignItems="start">
        <Title data-test="condition-label" />
        <div data-test="condition" className="width-100">
          {renderContentByTypeDelay()}
        </div>
      </BlockContentWrapper>
    </ContainerNodeContent>
  );
};

UINodeFilter.defaultProps = {
  initData: {},
  onChange: () => {},
  validateKey: 1,
  disabled: false,
};

export default UINodeFilter;
