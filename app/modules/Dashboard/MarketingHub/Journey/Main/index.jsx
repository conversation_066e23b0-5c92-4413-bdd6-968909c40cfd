/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { memo, useRef, useMemo, useLayoutEffect } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';

import ErrorBoundary from 'components/common/ErrorBoundary';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { useHistory, useParams } from 'react-router-dom';

// import Tabs from '@material-ui/core/Tabs';
// import Tab from '@material-ui/core/Tab';
import useElementSizeObserver from '../../../../../hooks/useElementSizeObserver';

import reducer from './reducer';
import saga from './saga';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import { init, reset, updateValue } from '../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../config';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { WrapperDetail, AntsomiTabsUI } from './styles';
import {
  makeSelectActiveRow,
  makeSelectStoryDetailDomainMain,
} from './selectors';

import ListPage from '../List';
import CampaignsPage from '../Campaigns';
import VariantsPage from '../Variants';
// import ZonePage from '../Zone';
// import SettingsPage from './Settings';
import LayoutLoading from '../../../../../components/Templates/LayoutContent/LayoutLoading';
import APP from '../../../../../appConfig';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../../utils/web/cookie';
import { StyleWrapper } from '../List/styles';
import {
  makeSelectIsOpenFullScreen,
  makeSelectIsSaveDoneJourney,
  makeSelectJourneyChannel,
  makeSelectJourneyChannelActive,
  makeSelectJourneyCommonMain,
} from '../selectors';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import { checkActiveTabExist } from './utils';
import { getReportInfo } from '../Detail/Dashdoard/config.report';
import { makeUrlPermisison } from '../../../../../utils/web/permission';

import DrawerJourneyDetail from './DrawerJourneyDetail';
import { isDetailDrawer } from '../utils';
import { CHANNEL } from '../constant';
import { useLessPaddingTopWorkspace } from '../../../../../hooks/useLessPaddingTopWorkspace';
import DrawerCreatingJourney from '../../../../../containers/Drawer/DrawerCreatingJourney';
import { get } from 'lodash';
import { HeaderProvider } from '../Header';

const MAP_TITLE = {
  title: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_AUTOMATION, 'Stories'),
  tab: {
    overview: getTranslateMessage(
      TRANSLATE_KEY._TAB_STORY_OVERVIEW,
      'Overview',
    ),
    list: getTranslateMessage(TRANSLATE_KEY._TAB_JOURNEY, 'Journey'),
    zones: getTranslateMessage(TRANSLATE_KEY._TAB_STORY_ZONE, 'Zones'),
    campaigns: getTranslateMessage(TRANSLATE_KEY._TAB_CAMPAIGN, 'Campaigns'),
    variants: getTranslateMessage(TRANSLATE_KEY._TAB_VARIANT, 'Variants'),
  },
  titleMap: {
    overview: getTranslateMessage(
      TRANSLATE_KEY._TAB_STORY_OVERVIEW,
      'Overview',
    ),
    list: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_LISTING_JOURNEY,
      'Journey',
    ),
    zones: getTranslateMessage(TRANSLATE_KEY._TAB_STORY_ZONE, 'Zones'),
    campaigns: getTranslateMessage(TRANSLATE_KEY._TAB_CAMPAIGN, 'All Campaign'),
    variants: getTranslateMessage(TRANSLATE_KEY._TAB_VARIANT, 'All Variants'),
    journeys: getTranslateMessage(TRANSLATE_KEY._TAB_JOURNEY, 'All Journeys'),
  },
};

export function DetailPage(props) {
  // const [selectedRows, setSelectedRows] = useState([]);
  const { activeId, tab, channelId = 0 } = useParams();
  const tabWrapperRef = useRef();
  const headerRef = useRef();
  const [{ width: tabWidth }] = useElementSizeObserver();
  const rightTabWidth = tabWrapperRef.current
    ? tabWrapperRef.current.getBoundingClientRect().width - tabWidth
    : 0;

  const REPORT_CONFIG_KEY = `journey_overview-${props.channelActive.code}`;
  const reportOverviewConfig = getReportInfo(REPORT_CONFIG_KEY);
  const isShowOverviewTab = reportOverviewConfig !== null;

  const { main, isSaveDone = false, mainCommon } = props;

  const history = useHistory();
  const searchParams = new URLSearchParams(history.location.search);

  const oid = searchParams.get('oid');

  useLessPaddingTopWorkspace();

  useLayoutEffect(() => {
    const activeTabDefault = checkActiveTabExist({
      activeTab: tab,
      isShowOverviewTab,
      channelId,
    }); // nhập tạp trên url không đúng sẽ trả về tab default list

    if (tab) {
      props.updateChannelActive(channelId);
      props.init({ activeId, tab: activeTabDefault, ownerId: oid });
    } else {
      const newUrl = `${
        APP.PREFIX
      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelId}/${activeTabDefault}`;

      history.push(makeUrlPermisison(newUrl));
    }

    return () => {
      props.reset();
    };
  }, [channelId, oid]);

  const onChangeTab = value => {
    props.onChangeTab(value);

    const newUrl = `${
      APP.PREFIX
    }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
      props.channelActive.value
    }/${value}`;

    history.push(makeUrlPermisison(newUrl));
  };

  const channelInfo = Object.values(CHANNEL).find(
    channel => channel.id === props.channelActive.value,
  );

  const openDrawerDetail = useMemo(() => {
    return isDetailDrawer(isSaveDone);
  }, [isSaveDone, searchParams]);

  const getChannelActive = () => {
    let result = props.channelActive;

    if (+channelId !== props.channelActive.value) {
      result = props.channels.list.find(
        channel => channel.value === +channelId,
      );
    }

    return result;
  };

  if (!tab || tab !== main.activeTab) return null;

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Main/index.jsx">
      <UIHelmet title={channelInfo?.label} />
      <HeaderProvider ref={headerRef}>
        <StyleWrapper>
          <LayoutContent padding="0" justifyContent="flex-start">
            <WrapperDetail>
              <LayoutLoading isLoading={false}>
                <AntsomiTabsUI
                  activeKey={main.activeTab}
                  onChange={onChangeTab}
                  items={[
                    {
                      children: (
                        <ListPage
                          activeTab={main.activeTab}
                          channelActive={
                            getChannelActive() || props.channelActive
                          }
                          channelId={+channelId}
                          isShowOverviewTab={isShowOverviewTab}
                          rightTabWidth={rightTabWidth}
                        />
                      ),
                      key: 'list',
                      label:
                        props.channelActive.value === 0
                          ? MAP_TITLE.titleMap.journeys
                          : MAP_TITLE.tab.list,
                    },
                    {
                      children: (
                        <CampaignsPage
                          storyId={main.activeId}
                          channelId={+channelId}
                          isShowOverviewTab={isShowOverviewTab}
                          rightTabWidth={rightTabWidth}
                        />
                      ),
                      key: 'campaigns',
                      label:
                        props.channelActive.value === 0
                          ? MAP_TITLE.titleMap.campaigns
                          : MAP_TITLE.tab.campaigns,
                    },
                    {
                      children: (
                        <VariantsPage
                          storyId={main.activeId}
                          channelId={+channelId}
                          isShowOverviewTab={isShowOverviewTab}
                          rightTabWidth={rightTabWidth}
                        />
                      ),
                      key: 'variants',
                      label:
                        props.channelActive.value === 0
                          ? MAP_TITLE.titleMap.variants
                          : MAP_TITLE.tab.variants,
                    },
                  ]}
                />

                {openDrawerDetail && (
                  <DrawerJourneyDetail
                    open={openDrawerDetail}
                    fullScreen={props.isFullScreen}
                    channelId={props.channelActive.value}
                    ref={headerRef}
                  />
                )}
                <DrawerCreatingJourney
                  isOpen={mainCommon?.drawerJourneyInfo.isOpenCreateJourney}
                  toggle={isOpen => props.onToggleCreateJourney(isOpen)}
                  channelId={
                    get(
                      props.mainCommon,
                      'drawerJourneyInfo.channelActive.value',
                      '',
                    ) || get(props.mainCommon, 'channelActive.value', '')
                  }
                  drawerJourneyInfo={mainCommon.drawerJourneyInfo}
                  ref={headerRef}
                />
              </LayoutLoading>
            </WrapperDetail>
          </LayoutContent>
        </StyleWrapper>
      </HeaderProvider>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectStoryDetailDomainMain(),
  mainCommon: makeSelectJourneyCommonMain(),

  activeRow: makeSelectActiveRow(),
  channelActive: makeSelectJourneyChannelActive(),
  isFullScreen: makeSelectIsOpenFullScreen(),
  isSaveDone: makeSelectIsSaveDoneJourney(),
  channels: makeSelectJourneyChannel(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB@@`, params));
    },
    updateChannelActive: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG_COMMON.key}@@CHANNEL_ACTIVE_FROM_URL@@`,
          params,
        ),
      );
    },
    onToggleCreateJourney: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG_COMMON.key}@@TOGGLE_CREATE_JOURNEY`,
          params,
        ),
      );
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withReducer,
  withSaga,
  withConnect,
  memo,
)(DetailPage);
