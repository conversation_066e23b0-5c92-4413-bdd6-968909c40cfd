/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect, useMemo } from 'react';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import { Flex, Icon } from '@antscorp/antsomi-ui';
import { Grid } from '@material-ui/core';
// Components
import ItemCondition from './Item';
import { Divider } from '../../../../Atoms/Divider';

// Styled
import { ButtonWithIcon, GroupConditionWrapper } from '../../styled';

// Constants
import { MAP_SEGMENT_VALUES } from '../../../UIPerformEvent/constants';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { ARCHIVE_STATUS, TYPE_ATTRIBUTE } from '../../../../../utils/constants';

// Utils
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import { filterDeep } from '../../../../../utils/web/utils';

const GroupConditionHaveAttribute = props => {
  const { item, groupIndex, options, isViewMode } = props;

  const optionsProperty = useMemo(
    () =>
      filterDeep(
        props.options,
        (_value, _key, option) => {
          const { status, attrType } = option;

          return (
            Number(status) !== ARCHIVE_STATUS ||
            attrType !== TYPE_ATTRIBUTE.COMPUTED
          );
        },
        {
          childrenPath: 'options',
        },
      ),
    [options],
  );

  const onClick = () => {
    props.callback('ADD_GROUP');
  };

  const deleteGroup = () => {
    props.callback('DELETE_GROUP', { groupIndex });
  };

  useEffect(() => {
    if (item.size === 0) {
      deleteGroup();
    }
  }, [item.size]);

  const renderItemCondition = rule => {
    const result = [];
    if (rule.size === 0) {
      return null;
    }

    let indexItem = -1;
    const firstKey = rule.keySeq().first();

    rule.forEach((element, index) => {
      indexItem += 1;
      if (element.get('conditionType')) {
        if (
          element.get('conditionType').value === MAP_SEGMENT_VALUES.comp_attr
        ) {
          result.push(
            <ItemCondition
              moduleConfig={props.moduleConfig}
              keyItem={indexItem.toString()}
              groupIndex={groupIndex}
              itemIndex={index}
              item={element}
              options={optionsProperty}
              callback={props.callback}
              showAdd={item.size === indexItem + 1 && item.size <= 19}
              showDelIcon={!(props.totalGroupSize === 1 && item.size === 1)}
              isFirstItem={firstKey === index}
              key={indexItem.toString()}
              disabled={props.disabled}
              isViewMode={isViewMode}
              itemSize={item.size}
            />,
          );
        }
        return null;
      }
      return null;
    });

    return result;
  };

  const renderSeparator = () => {
    if (props.showButtonOr) {
      if (isViewMode) return null;

      return (
        <ButtonWithIcon
          onClick={onClick}
          icon={<Icon type="icon-ants-add" />}
          data-test="item-condition-btn-or"
          className="btn-or-condition"
        >
          {`${getTranslateMessage(TRANSLATE_KEY._ACT_OR, 'OR')}`}
        </ButtonWithIcon>
      );
    }

    if (props.indexGroup === props.maxGroup) return null;

    return (
      <Divider
        dashed
        label="OR"
        type="horizontal"
        style={{ margin: '15px 50px 15px 0' }}
      />
    );
  };

  return (
    <React.Fragment key={props.keyGroup}>
      <Grid container>
        <Grid item xs={12} lg={10}>
          <GroupConditionWrapper
            className="condition-audience-attr width-690"
            data-test="group-condition"
          >
            <Flex
              vertical
              style={{ padding: props.design === 'preview' ? '7px' : '0' }}
            >
              {renderItemCondition(item)}
            </Flex>
          </GroupConditionWrapper>
        </Grid>
        {renderSeparator() && (
          <Grid item xs={12} lg={10}>
            <div className="pt-12 pb-12 ">
              <WrapperDisable disabled={props.disabled}>
                {renderSeparator()}
              </WrapperDisable>
            </div>
          </Grid>
        )}
      </Grid>
    </React.Fragment>
  );
};

export default React.memo(GroupConditionHaveAttribute);
