/* eslint-disable no-undef */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/prop-types */
/* eslint-disable react/button-has-type */
/* eslint-disable import/no-cycle */
/* eslint-disable no-nested-ternary */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */

// Libraries
import React, { useCallback, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import { get, isEmpty } from 'lodash';
import { useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Tabs,
  TemplateListing,
  useTemplateListing,
  Icon as AntsomiIcon,
  queryClientAntsomiUI,
  getCategoriesFromObjectTemplate,
} from '@antscorp/antsomi-ui';

// Components
import {
  UIButton,
  UILoading,
  UICheckbox,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
} from '@xlab-team/ui-components';
import {
  Drawer,
  FormControlLabel,
  Icon,
  Radio,
  RadioGroup,
  Popover,
} from '@material-ui/core';

// Services
import JourneyServices from 'services/Journey';
import UserAttrServices from 'services/UserAttributes';

// Constants
import {
  LAYOUT_TEMPLATE,
  GALLERY,
  MY_TEMPLATE,
  TYPE_ALL,
  DEVICE_TEMPLATE,
  OBJECTIVE_TYPE,
  MAP_TITLE,
  POST_MESSAGE_TYPES,
  TEMPLATE_TAB_MENU_KEYS,
  TEMPLATE_PREVIEW_ID,
  TEMPLATE_TAB_MENU_ITEMS,
  ITEM_DIMENSION,
  BANNER_HEIGHT,
} from './constants';
import { MODULE_CONFIG } from '../../../../../modules/Dashboard/MarketingHub/Journey/config';

// Utils
import { getPortalId, getCurrentUserId } from 'utils/web/cookie';
import { MEDIA_TEMPLATE } from '../../utils';
import { isProduction, safeParse } from '../../../../../utils/common';
import { getObjectPropSafely } from '../../utils.3rd';
import { getChannelIdByCode } from '../../../../../modules/Dashboard/MarketingHub/Journey/utils';
import { getUrlCreateJourneyByTemplate } from '../../../../../modules/Dashboard/MarketingHub/Journey/CreateTemplate/utils';
import { getTranslateMessage } from 'containers/Translate/util';

// Styled
import {
  SavedBlock,
  WrapperSavedBlock,
  WrapperUICheckbox,
  Iframe,
  TemplatesWrapper,
  TemplateItem,
  UIModalStyled,
  WrapperOption,
  Wrapper,
  PreviewTemplateWrapper,
} from './styles';

// Selectors
import { createStructuredSelector } from 'reselect';
import { updateValue } from '../../../../../redux/actions';

// Hooks
import useDebounce from '../../../../../hooks/useDebounce';
import { makeSelectJourneyChannelActive } from '../../../../../modules/Dashboard/MarketingHub/Journey/selectors';
import {
  useDeepCompareMemo,
  useExternalServiceAuth,
} from '../../../../../hooks';
import {
  getMediaTemplateViewPageThumbnails,
  initPreviewMediaTemplate,
} from './utils';
import {
  CATEGORY_SPLIT_KEY,
  LIMIT_LIST_PER_PAGE,
  OBJECT_TYPES,
  QUERY_KEYS,
} from '@antscorp/antsomi-ui/es/constants';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { UI_DETAIL_DRAWER } from '../../../../../modules/Dashboard/MarketingHub/Journey/constant';

const MediaTemplateEditor = props => {
  const {
    hiddenObjectiveDropdown = false,
    objectiveTypesParent,
    journeySettings = {},
    errors = [],
  } = props;

  const [state, setState] = useImmer({
    isOpenDrawer: false,
    previewId: '',
    isLoadingTypesList: false,
    isLoadingTemplateType: true,
    isLoadingSavedBlock: false,
    selectedTypes: [1, 2, 3, 4, 5],
    selectedDevices: [1, 2],
    objectiveTypes: [1, 2, 3, 4],
    typesList: [],
    mediaTemplateList: [],
    // template after select
    selectedTemplate: {
      properties: {},
      template_name: '',
      template_setting: {
        rulesets: [],
      },
      template_type: null,
      viewPages: [],
    },
    isSaved: false,
    tabActive: GALLERY,
    postMessageId: null,
    version: null,
    /* Check set Template Detail Success  */
    isSetTemplateDetailSuccess: false,

    // modals
    isOpenSelectTemplate: false,
    selectedType: DEVICE_TEMPLATE.desktop.id,
    selectedDevice: LAYOUT_TEMPLATE.pop_up.id,
    isUseTemplate: false,
    isBlank: false,
    templateName: null,
    anchorEl: null,
    isOpenCheckboxType: false,
    activeTab: TEMPLATE_TAB_MENU_KEYS.templateGallery,

    /* Preview Modal State */
    isOpenPreviewModal: false,
  });
  const { activeTab } = state;

  const serviceAuth = useExternalServiceAuth();
  const history = useHistory();

  const searchParams = new URLSearchParams(window.location.search);

  const debounceState = useDebounce(state, 500);

  // Theo breadcrumb user_id
  // const hasPermisisonCreate = validateAction(
  //   MENU_CODE.MEDIA_TEMPLATE,
  //   APP_ACTION.CREATE,
  //   props.activeRow.c_user_id || getCurrentAccessUserId(),
  // );

  // Theo current user
  // const hasPermisisonCreate = checkingRoleScope(
  //   MENU_CODE.MEDIA_TEMPLATE,
  //   APP_ACTION.CREATE,
  //   APP_ROLE_SCOPE.CREATED_BY_USER,
  // );

  const publicLevel = useMemo(
    () => TEMPLATE_TAB_MENU_ITEMS[activeTab]?.publicLevel,
    [activeTab],
  );

  const getListType = useMemo(
    () => TEMPLATE_TAB_MENU_ITEMS[activeTab]?.getListType,
    [activeTab],
  );

  // const { data: templateCategoryList } = useGetTemplateCategoryList({
  //   args: {
  //     auth: serviceAuth,
  //     params: {
  //       publicLevel,
  //       getListType,
  //       objectTypes: [OBJECT_TYPES.MEDIA_TEMPLATE],
  //     },
  //   },
  // });

  const {
    // Values
    categoryItems,
    templateItems,
    templateDetail,
    similarTemplates,
    checkedCategories,
    openCategoryKeys,
    previewTemplateCategories,
    selectTemplateAction,

    // Loading
    isLoadingCategoryList,
    isLoadingTemplateDetail,

    // refetchTemplateList,
    // refetchCategoryList,

    // Functions
    onLoadMore: onLoadMoreTemplates,
    onChangeOpenCategoryKeys,
    onChangeCheckedCategories,
    onRemoveObjectTemplate,
    onSelectTemplate: onNewSelectTemplate,
    setState: setTemplateState,
  } = useTemplateListing({
    serviceAuth,
    config: {
      getListType,
      objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
      publicLevel, // 1: Public, 0: Restricted
      limitListPerPage: LIMIT_LIST_PER_PAGE,
    },
    checkedCategoriesDefault: {
      ...(hiddenObjectiveDropdown &&
        objectiveTypesParent && { goal: objectiveTypesParent }),
    },
  });

  const onOpenDrawer = () => {
    setState(draft => {
      draft.isOpenDrawer = true;
    });
    props.updateIsOpenSubDrawer(true);
    if (searchParams.get('ui') === UI_DETAIL_DRAWER) {
      props.updateFullScreen(true);
    }
  };

  const onSelectTemplate = value => {
    setState(draft => {
      // draft.isOpenDrawer = true;
      draft.selectedTemplate = value;
      if (value.template_id) {
        draft.isUseTemplate = true;
        draft.templateName = value.template_name;
      } else {
        draft.isBlank = true;
      }
    });
    // handle pre-select template blast campaign
    if (props.templateType) {
      const channelCode = props.channelActive.code;
      const channelId = getChannelIdByCode(channelCode);
      const redirectUrl = getUrlCreateJourneyByTemplate(
        channelId,
        props.templateType,
      );

      props.updateCreatingJourneyInfo({
        channelCode,
        templateType: props.templateType,
        data: {},
        selectedTemplateTemp: value,
      });

      if (redirectUrl) history.push(redirectUrl);
    } else {
      onOpenDrawer();
    }
  };

  const previewTemplate = ({ type, id }) => {
    if (type === POST_MESSAGE_TYPES.PREVIEW_CDP_CAMPAIGN_WAITING) {
      if (!state.previewId) {
        setState(draft => {
          draft.previewId = id;
        });
      }

      setTimeout(() => {
        window.postMessage(
          {
            ...templateDetail?.templateSettings,
            views: templateDetail?.settings.viewPages,
            messageType: POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN,
            targetScript: id,
          },
          '*',
        );
      }, 100);
    }
  };

  const togglePreviewModal = isOpen => {
    setState(draft => {
      draft.isOpenPreviewModal =
        typeof isOpen === 'boolean' ? isOpen : !draft.isOpenPreviewModal;
    });
  };

  useEffect(() => {
    if (hiddenObjectiveDropdown && props.objectiveTypesParent) {
      setState(draft => {
        draft.objectiveTypes = props.objectiveTypesParent;
      });

      // onChangeCheckedCategories({
      //   ...checkedCategories,
      //   goal: props.objectiveTypesParent.map(goal)
      // })
    }
  }, [props.objectiveTypesParent, checkedCategories]);

  useEffect(() => {
    if (!props.value) {
      const selectedTemplate = {
        properties: {},
        template_name: '',
        template_setting: {
          rulesets: [],
        },
        template_type: null,
        viewPages: [],
      };
      setState(draft => {
        draft.isSaved = false;
        // draft.selectedTypes = '';
        draft.selectedTemplate = selectedTemplate;
      });
    }
  }, [props.componentKey, props.value]);

  useEffect(() => {
    const initValue = props.variantExtraData;
    const isSavedTemplate =
      initValue.template_settings && initValue.template_settings.id;
    // || (initValue.properties && initValue.properties.id);
    const isSavedVariant = initValue.fe_config_id;
    if (isSavedTemplate && !isSavedVariant) {
      // Nếu là variant sau khi select chứ chưa save changes(API) thì mới chạy vào đây
      const viewPages = Object.entries(initValue.views).map(([k, v]) => ({
        ...v,
        id: k,
      }));

      const selectedTemplate = {
        properties: initValue.properties || {},
        template_setting: initValue.template_settings,
        template_name: initValue.template_settings.name,
        template_type: initValue.template_settings.type,
        viewPages,
      };
      setState(draft => {
        draft.isSaved = true;
        draft.selectedTemplate = selectedTemplate;
      });
    } else {
      // new Variant
      const selectedTemplate = {
        properties: {},
        template_name: '',
        template_setting: {
          rulesets: [],
        },
        template_type: null,
        viewPages: [],
      };
      setState(draft => {
        draft.isSaved = false;
        // draft.selectedTypes = '';
        draft.selectedTemplate = selectedTemplate;
      });
    }
  }, [props.componentKey]);

  useEffect(() => {
    const createFromTemplate = !isEmpty(
      getObjectPropSafely(() => props.creatingJourneyInfo.data, {}),
    );

    const selectedTemplateJourneyInfo = !isEmpty(
      getObjectPropSafely(
        () => props.creatingJourneyInfo.selectedTemplateTemp,
        {},
      ),
    );

    if (createFromTemplate) {
      const selectedTemplate = { ...props.creatingJourneyInfo.data };

      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        draft.isSaved = true;
        draft.isUseTemplate = false;
        draft.isBlank = false;
      });

      props.onChangeOthers(MEDIA_TEMPLATE, selectedTemplate, {
        isUseTemplate: state.isUseTemplate,
        templateName: state.templateName,
        isBlank: state.isBlank,
        resetCreatingJourneyInfo: true,
      });
    }

    if (selectedTemplateJourneyInfo) {
      const selectedTemplate = {
        ...props.creatingJourneyInfo.selectedTemplateTemp,
      };
      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        if (selectedTemplate.template_id) {
          draft.isUseTemplate = true;
          draft.templateName = selectedTemplate.template_name;
        } else {
          draft.isBlank = true;
        }
      });

      if (state.isSetTemplateDetailSuccess) {
        props.updateCreatingJourneyInfo({
          selectedTemplateTemp: {},
        });
      }
      props.updateLoadingOnStepTwo(false);
      onOpenDrawer();
    }
  }, [
    props.componentKey,
    props.creatingJourneyInfo,
    state.isSetTemplateDetailSuccess,
  ]);

  useEffect(() => {
    let isNullRes = false;

    const {
      fe_config_id,
      views,
      template_settings,
      copy_id,
    } = props.variantExtraData;

    const fetchData = async (id, isCopy) => {
      props.setIsFetchData(true);
      setState(draft => {
        draft.isSaved = true;
        draft.isLoadingSavedBlock = true;
      });

      let res;

      if (props.variantExtraData?.properties) {
        // use case khi use template ở khác portal thì cần lấy properties trong variantExtraData
        isNullRes = true;

        res = {
          data: [
            {
              properties: props.variantExtraData.properties,
            },
          ],
        };
      } else {
        res = await UserAttrServices.settings.get(id);
      }

      props.setIsFetchData(false);
      const { properties = {} } = res.data[0];
      const viewPages = Object.entries(safeParse(views, [])).map(([k, v]) => ({
        ...v,
        id: k,
      }));
      const selectedTemplate = {
        properties,
        template_setting: template_settings,
        template_name: safeParse(template_settings, {}).name,
        template_type: safeParse(template_settings, {}).type,
        viewPages,
        // Nếu là case clone => set fe_config_id = null
        fe_config_id: isCopy ? null : id || null,
      };

      setState(draft => {
        draft.selectedTemplate = selectedTemplate;
        draft.isLoadingSavedBlock = false;
      });
      if (!isNullRes) {
        props.onChangeOthers(MEDIA_TEMPLATE, selectedTemplate, {
          isSavedVariant: true,
        });
      }
    };
    // case Clone
    if (copy_id) {
      fetchData(copy_id, true);
    }
    // case update variant đã save API || case clone
    else if (fe_config_id) {
      fetchData(fe_config_id, false);
    }
  }, [
    props.variantExtraData && props.variantExtraData.fe_config_id,
    props.variantExtraData && props.variantExtraData.copy_id,
  ]);

  useEffect(() => {
    setState(draft => {
      draft.isLoadingTypesList = true;
    });
    const fetchData = async () => {
      const res = await JourneyServices.mediaTemplate.getListType({
        data: {},
      });
      setState(draft => {
        // draft.selectedTypes = res.data[0].template_type_id;
        draft.typesList = res.data || [];
        draft.isLoadingTypesList = false;
      });
    };
    const fetchVersion = async () => {
      const res = await JourneyServices.mediaTemplate.getVersion();
      setState(draft => {
        draft.version = get(res, 'data.version', '');
      });
    };

    fetchData();
    fetchVersion();
  }, []);

  useEffect(() => {
    window.addEventListener('message', e => previewTemplate(e.data));

    return () => {
      window.removeEventListener('message', e => previewTemplate(e.data));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      state.selectedTypes.length &&
      state.selectedDevices &&
      state.typesList.length &&
      !state.isSaved
    ) {
      setState(draft => {
        draft.isLoadingTemplateType = true;
      });
      const fetchMediaTemplates = async (types, devices, objectives) => {
        const filter = [
          {
            TEMPLATE_TYPE: {
              matches_any: types.includes(TYPE_ALL)
                ? // type all => matches_any all types
                  state.typesList.map(template => template.template_type_id)
                : types,
            },
          },
          {
            DEVICE_TYPE: { matches_any: devices },
          },
          {
            OBJECTIVE_TYPE: {
              arr_matches_any: objectives,
            },
          },
        ];
        const params = `?page=${1}&columns=["properties","template_setting","thumbnail"]&filter=${JSON.stringify(
          filter,
        )}`;
        // const res = await JourneyServices.mediaTemplate.getListByType(params);
        const res =
          state.tabActive === MY_TEMPLATE
            ? await JourneyServices.mediaTemplate.getListByType(params)
            : await JourneyServices.mediaTemplate.getListGallery(params);

        const data =
          state.tabActive === MY_TEMPLATE
            ? res.data.body || []
            : (res.data.body || []).map(item => ({
                ...item,
                template_id: item.media_gallery_id,
                template_name: item.media_gallery_name,
              }));

        setState(draft => {
          draft.mediaTemplateList = data;
          draft.isLoadingTemplateType = false;
        });
      };
      fetchMediaTemplates(
        state.selectedTypes,
        state.selectedDevices,
        state.objectiveTypes,
      );
    }
  }, [
    debounceState.selectedTypes,
    debounceState.selectedDevices,
    debounceState.objectiveTypes,
    debounceState.tabActive,
    debounceState.typesList,
    debounceState.isSaved,
  ]);
  useEffect(() => {
    const listener = event => {
      const { actionType, data } = event.data;
      switch (actionType) {
        case 'onCancelMediaTemplate': {
          onCloseDrawer();
          break;
        }
        case 'onSaveMediaTemplate': {
          const selectedTemplate = { ...data };
          setState(draft => {
            draft.selectedTemplate = selectedTemplate;
            draft.isSaved = true;
            draft.isUseTemplate = false;
            draft.isBlank = false;
          });
          props.onChangeOthers(MEDIA_TEMPLATE, selectedTemplate, {
            isUseTemplate: state.isUseTemplate,
            templateName: state.templateName,
            isBlank: state.isBlank,
          });
          onCloseDrawer();
          break;
        }
        case 'setMediaTemplateDetailSuccess': {
          setState(draft => {
            draft.isSetTemplateDetailSuccess = true;
          });
          stopPostMessage();
          break;
        }
        default: {
          break;
        }
      }
    };

    window.addEventListener('message', listener);
    return () => window.removeEventListener('message', listener);
  }, [props.componentKey, state.postMessageId]);

  useEffect(() => {
    if (templateDetail) {
      switch (selectTemplateAction) {
        case 'edit':
        case 'use': {
          const { model } = templateDetail;
          onSelectTemplate(model);
          setTemplateState(prev => ({
            ...prev,
            selectedTemplateId: undefined,
            selectTemplateAction: undefined,
          }));

          togglePreviewModal(false);
          break;
        }
        case 'preview':
        default: {
          if (state.previewId) {
            previewTemplate({
              type: POST_MESSAGE_TYPES.PREVIEW_CDP_CAMPAIGN_WAITING,
              id: state.previewId,
            });
          } else {
            initPreviewMediaTemplate({
              templateId: `${templateDetail.id}`,
              zoneSelector: `#${TEMPLATE_PREVIEW_ID}`,
              isPreview: true,
            });
          }

          break;
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    templateDetail,
    selectTemplateAction,
    activeTab,
    history,
    setTemplateState,
  ]);

  const pagePreviewThumbnails = useDeepCompareMemo(
    () => getMediaTemplateViewPageThumbnails(templateDetail),
    [templateDetail],
  );

  const onSelectTypes = value => {
    const index = state.selectedTypes.findIndex(type => type === value);
    if (index === -1) {
      const indexTypeAll = state.selectedTypes.findIndex(
        type => type === TYPE_ALL,
      );

      // select All => uncheck others
      if (value === TYPE_ALL && indexTypeAll === -1) {
        setState(draft => {
          draft.selectedTypes = [0];
        });
      } else if (value !== TYPE_ALL && indexTypeAll > -1) {
        // select others type & type all selected => select new type & uncheck type all
        setState(draft => {
          draft.selectedTypes.splice(indexTypeAll, 1, value);
        });
      } else {
        // select others type
        setState(draft => {
          draft.selectedTypes.push(value);
        });
      }
    } else if (state.selectedTypes.length > 1) {
      // Prevent delete last item
      setState(draft => {
        draft.selectedTypes.splice(index, 1);
      });
    }
  };

  const onChangeCheckbox = (value, key) => {
    const index = state[key].findIndex(type => type === value);
    if (index === -1) {
      setState(draft => {
        draft[key].push(value);
      });
    } else if (state[key].length > 1) {
      // Prevent delete last item
      setState(draft => {
        draft[key].splice(index, 1);
      });
    }
  };

  const onCloseDrawer = (_event, reason) => {
    props.updateIsOpenSubDrawer(false);

    // Refetch Template listing
    queryClientAntsomiUI.invalidateQueries(
      [QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST],
      { exact: false },
    );

    queryClientAntsomiUI.invalidateQueries(
      [QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST],
      { exact: false },
    );

    setState(draft => {
      draft.isOpenDrawer = false;
    });
    props.updateFullScreen(false);
    // if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
    // }
  };

  const onPostMessage = () => {
    const mediaTemplateIframe = document.getElementById(
      'media-template-iframe',
    );
    // remove Previews
    window.postMessage(
      { messageType: 'preview-antsomi-cdp-campaign-close-all' },
      '*',
    );

    const { unsubscribeSegmentType, triggerType } = journeySettings;

    if (mediaTemplateIframe) {
      const { description } = state.selectedTemplate;

      const data = {
        ...state.selectedTemplate.properties,
        description,
        categories: getCategoriesFromObjectTemplate(state.selectedTemplate),
        journeySettings,
        unsubscribeSegmentType,
        triggerType,
        objectiveTypes:
          state.selectedTemplate.objective_type ||
          state.selectedTemplate.properties.objectiveTypes,
      };
      console.log({ data });

      const postMessage = () =>
        mediaTemplateIframe.contentWindow.postMessage(
          {
            actionType: 'setMediaTemplateDetail',
            data,
          },
          '*',
        );
      startPostMessage(postMessage);
    }
  };

  const startPostMessage = postMessage => {
    let timer = 0;
    const interval = 200;

    const postMessageId = setInterval(() => {
      if (typeof postMessage === 'function' && timer <= 10000) {
        postMessage();
        timer += interval;
      } else {
        clearInterval(postMessageId);
      }
    }, interval);

    setState(draft => {
      draft.postMessageId = postMessageId;
    });
  };

  const stopPostMessage = () => {
    clearInterval(state.postMessageId);
  };

  const onEditTemplate = () => {
    onOpenDrawer();
    onPostMessage();

    if (searchParams.get('ui') === UI_DETAIL_DRAWER) {
      props.updateFullScreen(true);
    }
  };

  const onResetSelectedTemplate = () => {
    const selectedTemplate = {
      properties: {},
      template_name: '',
      template_setting: {
        rulesets: [],
      },
      template_type: null,
      viewPages: [],
    };
    setState(draft => {
      draft.isSaved = false;
      // draft.selectedTypes = '';
      draft.selectedTemplate = selectedTemplate;
    });

    props.onChangeOthers(MEDIA_TEMPLATE, selectedTemplate);
  };

  const toggleSelectTemplate = () => {
    setState(draft => {
      draft.isOpenSelectTemplate = !state.isOpenSelectTemplate;
    });
  };

  const renderSavedBlock = () => (
    <WrapperSavedBlock>
      <UILoading isLoading={state.isLoadingSavedBlock} />
      <SavedBlock errors={Boolean(errors?.length)}>
        <img
          src={state.selectedTemplate.properties.thumbnail}
          alt={state.selectedTemplate.properties.name}
          width="100%"
          className="img-thumbnail"
          // height="100%"
        />
        {!props.isViewMode && (
          <div className="btn-group">
            <UIButton theme="primary" onClick={onEditTemplate}>
              {MAP_TITLE.actEdit}
            </UIButton>
            <UIButton
              theme="outline"
              onClick={onResetSelectedTemplate}
              className="m-left-3 bg-white"
            >
              {MAP_TITLE.actChangeTemplate}
            </UIButton>
          </div>
        )}
      </SavedBlock>
    </WrapperSavedBlock>
  );

  /* Modal Create Template */
  const renderModal = () => {
    const templateInfo = Object.values(LAYOUT_TEMPLATE).find(
      type => type.id === state.selectedType,
    );
    const blankTemplate = {
      template_name: '',
      template_type: templateInfo.id,
      device_type: state.selectedDevice,
      template_setting: {},
      properties: {
        deviceType: state.selectedDevice,
        template: {
          id: templateInfo.id,
          type: templateInfo.name,
          name: templateInfo.label,
        },
      },
      thumbnail: '',
    };

    const onSelectType = id => {
      setState(draft => {
        draft.selectedType = +id;
      });
    };

    const onSelectDevice = id => {
      setState(draft => {
        draft.selectedDevice = +id;
      });
    };

    const onCreateNewTemplate = () => {
      onSelectTemplate(blankTemplate);
      toggleSelectTemplate();
    };

    return (
      <UIModalStyled
        isOpen={state.isOpenSelectTemplate}
        toggle={toggleSelectTemplate}
        width={635}
      >
        <div className="wrapper-icon-close" onClick={toggleSelectTemplate}>
          <Icon>close</Icon>
        </div>
        <UIModalHeader>{MAP_TITLE.createNewTemplate}</UIModalHeader>
        <UIModalBody>
          <p className="label">{MAP_TITLE.deviceType}</p>
          <RadioGroup
            row
            value={state.selectedDevice}
            onChange={e => onSelectDevice(e.target.value)}
            className="group-radio"
          >
            {Object.values(DEVICE_TEMPLATE).map(each => (
              <React.Fragment key={each.id}>
                <FormControlLabel
                  value={each.id}
                  control={<Radio color="primary" size="small" />}
                  label={
                    <div className="radio-label">
                      {each.icon}
                      <p className="label">{each.label}</p>
                    </div>
                  }
                />
              </React.Fragment>
            ))}
          </RadioGroup>
          <p className="label">{MAP_TITLE.templateType}</p>
          <TemplatesWrapper>
            {state.typesList.map(type => {
              const item = LAYOUT_TEMPLATE[type.template_code];
              const { id, label, img } = item;

              return (
                <TemplateItem
                  key={id}
                  className={state.selectedType === id ? 'active' : ''}
                  onClick={() => onSelectType(id)}
                >
                  <img src={img} alt={label} />
                  <p className="label">{label}</p>
                </TemplateItem>
              );
            })}
          </TemplatesWrapper>
        </UIModalBody>
        <UIModalFooter>
          <UIButton theme="outline" onClick={toggleSelectTemplate}>
            {MAP_TITLE.cancel}
          </UIButton>
          <UIButton theme="primary" onClick={onCreateNewTemplate}>
            {MAP_TITLE.create.toUpperCase()}
          </UIButton>
        </UIModalFooter>
      </UIModalStyled>
    );
  };

  const onClickPreviewThumbnail = useCallback(
    viewId => {
      window.postMessage({
        messageType:
          POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN_VIEW_NAVIGATE,
        targetScript: state.previewId,
        viewId,
      });
    },
    [state.previewId],
  );

  const renderDisplayBlock = () => {
    const handleClosePopupCheckbox = () => {
      setState(draft => {
        draft.anchorEl = null;
        draft.isOpenCheckboxType = false;
      });
    };

    const renderOption = checkboxKey => {
      let content = null;
      switch (checkboxKey) {
        case 'deviceTypes':
          content = (
            <WrapperOption>
              {Object.values(DEVICE_TEMPLATE).map((device, index) => (
                <WrapperUICheckbox
                  key={device.id}
                  isLast={index === Object.keys(DEVICE_TEMPLATE).length - 1}
                >
                  <UICheckbox
                    label={
                      <p className="d-flex align-items-center m-y-0">
                        <span style={{ fontSize: 12 }} className="m-left-1">
                          {device.label}
                        </span>
                      </p>
                    }
                    checked={state.selectedDevices.includes(device.id)}
                    onChange={() =>
                      onChangeCheckbox(device.id, 'selectedDevices')
                    }
                    disabled={state.isLoadingTemplateType}
                  />
                </WrapperUICheckbox>
              ))}
            </WrapperOption>
          );
          break;

        case 'displayModes':
          content = (
            <WrapperOption>
              {state.typesList.map(item => (
                <WrapperUICheckbox key={item.template_type_id}>
                  <UICheckbox
                    label={
                      <p className="d-flex align-items-center m-y-0">
                        <span style={{ fontSize: 12 }} className="m-left-1">
                          {LAYOUT_TEMPLATE[item.template_code].label}
                        </span>
                      </p>
                    }
                    checked={state.selectedTypes.includes(
                      item.template_type_id,
                    )}
                    onChange={() => onSelectTypes(item.template_type_id)}
                    disabled={state.isLoadingTemplateType}
                  />
                </WrapperUICheckbox>
              ))}
            </WrapperOption>
          );
          break;

        case 'objectiveTypes':
          content = (
            <WrapperOption>
              {Object.values(OBJECTIVE_TYPE).map((objective, index) => (
                <WrapperUICheckbox
                  key={objective.id}
                  isLast={index === Object.keys(OBJECTIVE_TYPE).length - 1}
                >
                  <UICheckbox
                    label={
                      <p className="d-flex align-items-center m-y-0">
                        <span style={{ fontSize: 12 }} className="m-left-1">
                          {objective.label}
                        </span>
                      </p>
                    }
                    checked={state.objectiveTypes.includes(objective.id)}
                    onChange={() =>
                      onChangeCheckbox(objective.id, 'objectiveTypes')
                    }
                    disabled={state.isLoadingTemplateType}
                  />
                </WrapperUICheckbox>
              ))}
            </WrapperOption>
          );
          break;

        default:
          break;
      }

      return content;
    };
    return (
      <>
        <Wrapper className="template-listing-wrapper">
          <div className="__container">
            <Tabs
              activeKey={activeTab}
              items={Object.values(TEMPLATE_TAB_MENU_ITEMS)}
              // shadow
              onChange={tab => {
                setState(draft => {
                  draft.activeTab = tab;
                });

                // Reset category default
                setTimeout(() => {
                  onChangeCheckedCategories(
                    hiddenObjectiveDropdown && objectiveTypesParent
                      ? {
                          goal: objectiveTypesParent.map(
                            goal => `goal${CATEGORY_SPLIT_KEY}${goal}`,
                          ),
                        }
                      : {},
                  );
                }, 1000);
              }}
            />

            <TemplateListing
              templatesProps={{
                items: templateItems,
                onLoadMoreTemplates,
              }}
              categoryListingProps={{
                items: categoryItems,
                loading: isLoadingCategoryList,
                checkedCategories,
                openKeys: openCategoryKeys,
                onOpenChange: onChangeOpenCategoryKeys,
                onMenuChange: value => {
                  onChangeCheckedCategories(value);
                },
              }}
              templateItemProps={{
                'data-test': 'template',
                showSkeleton: true,
                /* Adjust size of item when in small screen */
                ...(!props.templateType && {
                  width: ITEM_DIMENSION.width,
                  height: ITEM_DIMENSION.height,
                }),
                editBtnProps: {
                  text: getTranslateMessage(TRANSLATE_KEY._, 'Use template'),
                  onClick: id => onNewSelectTemplate(id, 'use'),
                },
                previewBtnProps: {
                  onClick: id => {
                    togglePreviewModal(true);
                    onNewSelectTemplate(id, 'preview');
                  },
                },
                removable: false,
                removeModalProps: {
                  onOk: onRemoveObjectTemplate,
                },
              }}
              blankTemplateProps={{
                show: false,
                description: getTranslateMessage(
                  TRANSLATE_KEY._,
                  'Blank Template',
                ),
                onClick: () => toggleSelectTemplate(),
              }}
              previewModalProps={{
                bannerProps: {
                  height: BANNER_HEIGHT,
                  deviceType:
                    templateDetail?.deviceType === 1 ? 'desktop' : 'mobile',
                  showSkeleton: true,
                  children: (
                    <PreviewTemplateWrapper
                      id={TEMPLATE_PREVIEW_ID}
                      // NOTES: Value 5 is slide in type
                      $isSlideIn={+(templateDetail?.type || 1) === 5}
                      className="ants-relative ants-h-full ants-w-full"
                    />
                  ),
                },
                thumbnailProps: {
                  thumbnails: pagePreviewThumbnails,
                  onClickThumbnail: ({ id }) => onClickPreviewThumbnail(id),
                },
                informationProps: {
                  itemName: templateDetail?.name,
                  categoryListing: previewTemplateCategories,
                  description: templateDetail?.description,
                  showDeviceRadios: false,
                  buttonText: getTranslateMessage(
                    TRANSLATE_KEY._,
                    'Use template',
                  ),

                  /* Click use/create template button */
                  onButtonClick: () =>
                    onNewSelectTemplate(templateDetail?.id || '', 'use'),
                },
                similarTemplateProps: {
                  similarTemplates,
                  similarCardProps: {
                    showSkeleton: true,
                    onClickWrapper: id => {
                      onNewSelectTemplate(id, 'preview');
                    },
                  },
                },
                loading: isLoadingTemplateDetail || false,
                open: state.isOpenPreviewModal,
                onCancel: () => togglePreviewModal(false),
                onOk: () => togglePreviewModal(false),
              }}
              emptyProps={{
                icon: <AntsomiIcon type="icon-ants-media-template" />,
                description: getTranslateMessage(
                  TRANSLATE_KEY._,
                  'No template available',
                ),
              }}
            />
          </div>
        </Wrapper>
        <Popover
          // id={id}
          open={state.isOpenCheckboxType}
          anchorEl={state.anchorEl}
          onClose={handleClosePopupCheckbox}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          // anchorPosition={{ left: -100, top: -400 }}
        >
          {renderOption(state.checkboxKey)}
        </Popover>
      </>
    );
  };

  const renderDrawer = () => (
    <Drawer
      anchor="right"
      transitionDuration={0}
      open={state.isOpenDrawer}
      onClose={(_, reason) => onCloseDrawer(_, reason)}
      PaperProps={{
        style: {
          // width: '90vw',
          width: 'calc(100vw - 48px)',
        },
      }}
    >
      <Iframe
        id="media-template-iframe"
        title="media-tempalte-iframe"
        data-test="media-template-iframe"
        src={
          isProduction()
            ? `${
                PORTAL_CONFIG.URL_MEDIA_TEMPLATE
              }/${getPortalId()}#/${getCurrentUserId()}/media-template/embed`
            : `https://sandbox-media-template.antsomi.com/cdp/${getPortalId()}#/${getCurrentUserId()}/media-template/embed`
        }
        allowFullScreen
        width="100%"
        height="100%"
        onLoad={onPostMessage}
      />
    </Drawer>
  );

  return (
    <>
      {state.isSaved ? renderSavedBlock() : renderDisplayBlock()}
      {renderDrawer()}
      {renderModal()}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  channelActive: makeSelectJourneyChannelActive(),
});

const mapDispatchToProps = dispatch => ({
  updateIsOpenSubDrawer: value => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@IS_OPEN_SUB_DRAWER`, value));
  },
  updateCreatingJourneyInfo: value => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@CREATING_JOURNEY_INFO`, value));
  },
  updateLoadingOnStepTwo: params => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@LOADING_ON_STEP_TWO`, params));
  },
  updateFullScreen: params => {
    dispatch(updateValue(`${MODULE_CONFIG.key}@@TOGGLE_FULL_SCREEN@@`, params));
  },
});

MediaTemplateEditor.propTypes = {
  variantExtraData: PropTypes.object,
  setIsFetchData: PropTypes.func,
  onChangeOthers: PropTypes.func,
  // eslint-disable-next-line react/no-unused-prop-types
  isViewMode: PropTypes.bool,
  componentKey: PropTypes.string,
  hiddenObjectiveDropdown: PropTypes.bool,
  objectiveTypesParent: PropTypes.array,
  creatingJourneyInfo: PropTypes.object,
};

MediaTemplateEditor.defaultProps = {
  variantExtraData: {
    design: 'media-template',
    type: 'media-template',
    template_settings: {
      rulesets: [],
    },
    properties: {},
    fe_config_id: null,
    views: {},
  },
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(MediaTemplateEditor);
