/* eslint-disable no-nested-ternary */
/* eslint-disable no-shadow */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable indent */
import React, { useState, useEffect, useMemo, memo } from 'react';
import { difference, isEmpty, isNaN } from 'lodash';
// import COLORS from 'utils/colors';
import ObjectServices from 'services/Object';
import OperateServices from 'services/Operate';
import { useImmer } from 'use-immer';
import { OrderedSet } from 'immutable';
import { UIButton as Button } from '@xlab-team/ui-components';
import styled from 'styled-components';
import {
  safeParse,
  generateKey,
  getObjectPropSafely,
} from '../../../../utils/common';

import {
  isPropertyLookupIds,
  mapItemExtendValue,
  mapItemValueApply,
  mappingValueToOptions,
  mapItemDeleteStatus,
  mapValueCountryToOption,
  mapOptionWithExtendValue,
  getLeafNodes,
  sortCountryByValues,
} from '../utils';
import { isDecryptFields } from '../../../../utils/web/attribute';
import { getParamsAPILookup } from '../../../../services/map';
import AbtractsService from '../../../../services/Abstract';
// import useToggle from '../../../../hooks/useToggle';
import { getCurrentUserId, getPortalId } from '../../../../utils/web/cookie';
import { DATA_ACCESS_OBJECT } from '../../../../utils/constants';
import { scrollYearNow } from './Tree/utils';
import { toSegmentInsight } from 'containers/Drawer/DrawerSegment/utils';

import {
  MATCHES_ANY_SAMPLE_DATA,
  MatchesAnySelect,
  Tooltip,
  Typography,
} from '@antscorp/antsomi-ui';
import {
  LongerIcon,
  WarningIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import { getCellDisplayNumber } from '../../../../containers/Table/utils';
import { flatTree } from '@antscorp/antsomi-ui/es/utils';
// const KEY_CODE = {
//   PG_UP: 38,
//   PG_DOWN: 40,
//   ENTER: 13,
//   SPACE: 32,
//   RIGHT: 39,
//   LEFT: 37,
// };
const LIMIT = 20;
export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;
const PopoverDropdown = memo(props => {
  const { property, item, config } = props;
  const { propertyCode, objectLabel } = config;
  const [firstLoad, setFristLoad] = useState(false);
  const [value, setValue] = useState(OrderedSet([]));
  const [label, setLabel] = useState(OrderedSet([]));

  const [optionCurrentChecked, setOptionCurrentChecked] = useState(
    OrderedSet([]),
  );
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingSelect, setLoadingSelect] = useState(false);
  const [firstLoadLookup, setFirstLoadLookup] = useState(true);
  const [mapOptions, setMapOptions] = useState({});
  const [options, setOptions] = useState([]);
  const [missingIds, setMissingIds] = useState([]);
  const [stateSearch, setStateSearch] = useImmer({
    searchData: [],
  });
  const [componentKey] = useState(`autosuggestion-thanhnt-id-${generateKey()}`);
  const [indexCurrentValue, setIndexCurrentValue] = useState(0);
  const [mapPreviewColumn, setPreviewColumn] = useState([]);
  const extend = item.get('extendValue');
  const operator = item.get('operator');
  const dataType = item.get('dataType');

  const isId =
    propertyCode === 'customer_id' ||
    propertyCode === 'id' ||
    // propertyCode === 'story_id' ||
    propertyCode === 'segment_ids' ||
    propertyCode === 'campaign_id';

  const objLookup = isPropertyLookupIds({
    property,
    item,
    config,
    value: props.value,
  });

  const isDatetimeV2 =
    (property.dataType === 'datetime' || dataType === 'datetime') &&
    props.semantic;

  const isEncyptable =
    props.isEncrypt === 1 &&
    !isDecryptFields({
      itemTypeId: property.itemTypeId,
      attributeCode: propertyCode,
    });

  const isShowSelectGroup = useMemo(() => {
    return (
      property &&
      (property.itemTypeName === 'city' ||
        property.itemTypeName === 'province') &&
      property.propertyName === 'name'
    );
  }, [property]);

  const isFilterCountryName = useMemo(() => {
    return (
      property &&
      property.itemTypeName === 'country' &&
      property.propertyName === 'name'
    );
  }, [property]);

  const isFilterCityName = useMemo(() => {
    return (
      property &&
      property.itemTypeName === 'city' &&
      property.propertyName === 'name'
    );
  }, [property]);

  let labelNoti = objectLabel || '';
  if (property.itemTypeId !== undefined) {
    labelNoti = +property.itemTypeId === 1 ? 'collection' : 'segment';
  }
  const [state, setState] = useImmer({
    page: 1,
    limit: LIMIT,
    search: '',
    hasLoadMore: true,
    showCheckedOnly: false,
    isSelectAll: false,
    widthSpan: 0,
    initOperatorValue: props.operatorValue,
    extendValue: [],
    groupSelect: {
      groupItems: [],
      selectGroup: [],
    },
    selectedTreeData: [],
  });

  const { isMulti } = props;

  const setGroupSelect = data => {
    const groupItems = data.map(item => {
      return {
        key: `country-${item.countryId}`,
        label: item.countryName,
      };
    });

    setState(draft => {
      draft.groupSelect.groupItems = groupItems;
      draft.groupSelect.selectGroup = groupItems.map(item => item.key);
    });
  };

  const fetchData = async (
    isLoadMore,
    page,
    limit,
    search,
    listGroupSelected = [],
  ) => {
    if (!props.isBackUp) {
      const { itemTypeId, itemTypeName } = property;
      if (propertyCode === 'segment_ids') {
        props.config.filters.OR[0].AND.push({
          type: 1,
          column: 'status',
          data_type: 'number',
          operator: 'matches',
          value: [1, 2],
        });
      }

      const feServices =
        isShowSelectGroup || isFilterCountryName
          ? 'suggestionLocationItems'
          : safeParse(props.config.feServices, 'suggestion'); // suggestionMultilang

      const params = {
        data: {
          limit,
          page,
          sd: 'asc',
          search: search.trim(),
          decryptFields: isEncyptable ? [] : [`${propertyCode}`],
          itemTypeId,
          propertyCode,
          itemTypeName,
        },
      };

      const paramsCountry = {};

      params.data = {
        ...params.data,
        ...props.config,
        page,
        limit,
        search: search.trim(),
      };

      const type =
        itemTypeName === 'country'
          ? 'country'
          : itemTypeName === 'province'
          ? 'state-province'
          : 'city-district';

      paramsCountry.data = {
        type,
        search: search.trim(),
        page,
        limit: isFilterCityName ? 5 : 15,
        sort: 'asc',
        countryIds: listGroupSelected, // support filter country
        isPaginate: true, // support pagination for country
      };

      if (
        props.semantic &&
        (property.dataType === 'datetime' || dataType === 'datetime')
      ) {
        params.data.dataType = 'datetime';
        params.data.semantic = props.semantic.value;
      }
      if (propertyCode === 'segment_ids') {
        params.data.isGetOnlySuggestParams = false;

        if (config.objectType === 'BO_SEGMENTS') {
          const currentUserId = getCurrentUserId();
          const ownerId = getObjectPropSafely(
            () => props.activeRow && props.activeRow.owner_id,
            '',
          );

          params.data.ownerId = +ownerId || +currentUserId;
          params.menuObjectType = 'SEGMENT';
        }
      }
      if (props.moduleConfig && props.moduleConfig.viewId) {
        params.data.viewId = props.moduleConfig.objectId;
      }
      setLoading(true);

      let res = null;

      if (isShowSelectGroup || isFilterCountryName) {
        res = await ObjectServices[feServices].getList(paramsCountry);
      } else {
        res = await ObjectServices[feServices].getList(params);
      }

      if (res !== null) {
        const isHasLoadMore =
          isFilterCountryName || isShowSelectGroup
            ? res.total > options.length && res.list.length > 0
            : res.list.length >= LIMIT;

        setFirstLoadLookup(false);
        setState(draft => {
          draft.hasLoadMore = isHasLoadMore;
        });
        setState(draft => {
          draft.page = res.list.length > 0 ? page : state.page;
        });

        if (!firstLoad) {
          setFristLoad(true);
        }

        if (!isShowSelectGroup) {
          // if (optional !== undefined) {
          //   // Order is strict , cause in Promise context, SetState become sync
          //   setMapOptions({ ...optional.mapOptions, ...res.map });
          //   setOptions(
          //     mappingValueToOptions(
          //       isLoadMore,
          //       optional.value,
          //       optional.mapOptions,
          //       optional.options,
          //       res,
          //       props.semantic,
          //     ),
          //   );
          // } else {
          // Order is strict , cause in Promise context, SetState become sync
          const oldMapOptions = { ...mapOptions };
          // call API lookup sau khi mở Modal (dùng cho trường hợp !== id)
          // if (!firstLoad && ids.length > 0 && props.config.isPk !== 1) {
          //   const resLookup = await fetchDataFirst(ids);
          //   oldMapOptions = { ...resLookup.map, ...oldMapOptions };
          // }
          setMapOptions({ ...res.map });
          setOptions(
            mappingValueToOptions(
              isLoadMore,
              value,
              oldMapOptions,
              options,
              res,
              props.semantic,
            ),
          );
          // }
        } else if (isLoadMore) {
          setOptions([...options, ...mapValueCountryToOption(res.list)]);
        } else {
          setOptions(mapValueCountryToOption(res.list));
        }
        setLoading(false);
      }
      // });
    }
  };

  const fetchDataFirst = (ids, extend) => {
    const dataTmp = mapItemValueApply(ids, extend);
    setPreviewColumn(dataTmp);

    // const data = { ...mapOptions, ...resMap };
  };

  const fetchDataFristCustomerId = async (ids, extend) => {
    const { itemTypeId, itemTypeName } = property;
    // console.log('fetchDataFirst', { property, config: props.config });
    const params = {
      data: {},
    };
    // const feServices = safeParse(props.config.feServices, 'suggestion'); // suggestionMultilang
    const feServices = 'suggestion';
    if (feServices === 'suggestionMultilang') {
      // params.data = getParamsAPILookup({ ...props.config, ...property, ids });
      params.data = getParamsAPILookup({
        objectName: props.config.objectName,
        objectType: props.config.objectType,
        ...property,
        ids,
        config: props.config,
      });
      // console.log(
      //   'aaaa',
      //   getParamsAPILookup({
      //     objectName: props.config.objectName,
      //     objectType: props.config.objectType,
      //     ...property,
      //     ids,
      //     config: props.config,
      //   }),
      // );
    } else if (itemTypeId === -100) {
      params.data = {
        ids,
        decryptFields: isEncyptable ? [] : [`${propertyCode}`],
        itemTypeId,
        propertyCode,
        itemTypeName,
        config: props.config,
        itemPropertyName: propertyCode,
        scope: props.config.scope,
        dataType: property.dataType,
      };
    } else {
      params.data = {
        ids,
        decryptFields: isEncyptable ? [] : [`${propertyCode}`],
        itemTypeId: itemTypeId || props.config.itemTypeId,
        propertyCode,
        itemTypeName: itemTypeName || props.config.itemTypeName,
        config: props.config,
        itemPropertyName: propertyCode,
        scope: props.config.scope || 2,
      };
    }
    // Truong hop is member off
    if (propertyCode === 'segment_ids') {
      const filters = [...props.config.filters.OR[0].AND];
      filters.push({
        type: 1,
        column: 'segment_id',
        data_type: 'number',
        operator: 'matches',
        extendValue: [],
        value: ids,
      });
      params.data = {
        decryptFields: ['segment_ids'],
        isGetOnlySuggestParams: false,
        feKey: '-1007-users-segment_ids',
        feServices: 'suggestionMultilang',
        isLookupLabelId: true,
        isPk: 1,
        itemTypeId: -1007,
        itemTypeName: 'users',
        limit: 20,
        objectName: 'segment_ids',
        objectType: 'BO_SEGMENTS',
        page: 1,
        propertyCode: 'segment_ids',
        sd: 'asc',
        search: '',
        // overwrite props.config
        filters: {
          OR: [
            {
              AND: filters,
            },
          ],
        },
        isUnLimit: true,
      };
    }
    // console.log('build param fetchDataFirst DONE', params);
    setLoading(true);
    let res;
    if (propertyCode === 'segment_ids') {
      res = await ObjectServices.suggestionMultilang.getList(params);
      // Case is member off missing permission access
      if (Array.isArray(res.list) && Array.isArray(ids)) {
        const keyList = res.list.map(each => each.id);
        const missingKeyList = difference(
          ids.map(each => Number(each)),
          keyList,
        );

        if (missingKeyList.length > 0) {
          const response = await OperateServices.getObjectInfo({
            body: {
              objectIds: missingKeyList,
              objectType: DATA_ACCESS_OBJECT.SEGMENT,
            },
          });

          if (
            response &&
            response.code === 200 &&
            Array.isArray(response.data)
          ) {
            const missingMap = response.data.map(each => ({
              id: each.id,
              value: `${each.id}`,
              name: each.name,
              label: each.name,
              status: each.status,
            }));

            res.list.push(...missingMap);
            setMissingIds(missingKeyList);
          }
        }
      }
    } else {
      res = await ObjectServices[feServices].lookupByIds(params);
    }
    setLoading(false);
    setFirstLoadLookup(false);
    if (res.list.length > 0) {
      const dataTmp = mapItemExtendValue(res.list, extend, ids, labelNoti);
      setPreviewColumn(dataTmp);
      const data = { ...mapOptions, ...res.map };
      setMapOptions(Object.keys(data).filter(key => data[key].status !== 4));

      // initLabel sau khi fetchData từ value (vì thiếu props initLabel)
      const initLabel = ids.map(item => safeParse(data[item], {}).label);
      setLabel(OrderedSet(initLabel));
    } else if (ids) {
      const dataTmp = mapItemDeleteStatus(ids, labelNoti);
      setPreviewColumn(dataTmp);
    }
    setOptionCurrentChecked(OrderedSet(ids));
    return res;
  };

  const fetchDataDateTimeV2 = async (ids, extend) => {
    // console.log('fetchDataFirst', { property, config: props.config });
    const params = {
      data: {
        limit: state.limit,
        page: state.page,
        sd: 'asc',
        search: state.search.trim(),
        lookupValues: ids,
      },
    };
    params.data.dataType = property.dataType;
    params.data.semantic = props.semantic.value;
    // const feServices = safeParse(props.config.feServices, 'suggestion'); // suggestionMultilang
    const feServices = 'suggestionDateTime';

    // console.log('build param fetchDataFirst DONE', params);
    setLoading(true);
    const res = await ObjectServices[feServices].getList(params);
    setLoading(false);
    setFirstLoadLookup(false);
    if (res.list.length > 0) {
      const dataTmp = mapItemExtendValue(res.list, extend, ids, labelNoti);
      setPreviewColumn(dataTmp);
      const data = { ...mapOptions, ...res.map };
      if (
        props.semantic.value === 'MINUTE' ||
        props.semantic.value === 'HOUR'
      ) {
        setMapOptions(res.list.filter(item => item.status !== 4));
      } else {
        setMapOptions(Object.keys(data).filter(key => data[key].status !== 4));
      }

      // initLabel sau khi fetchData từ value (vì thiếu props initLabel)
      const initLabel = ids.map(item => safeParse(data[item], {}).label);
      setLabel(OrderedSet(initLabel));
    } else if (ids) {
      const dataTmp = mapItemDeleteStatus(ids, labelNoti);
      setPreviewColumn(dataTmp);
    }
    setOptionCurrentChecked(OrderedSet(ids));
    return res;
    // });
  };

  const fetchDataLocationGroup = async (ids, extend) => {
    // Retrieve the item type name from the property object.
    const { itemTypeName } = property;

    const idsTmp = difference(ids, extend);

    // Create the request parameters.
    const params = {
      data: {
        type: itemTypeName === 'province' ? 'province' : 'city', // Determine the type of location based on the item type name.
        objectNames: idsTmp, // Set the object names to the provided IDs.
      },
    };

    // Make the API request to fetch the location data.
    const res = await ObjectServices.suggestionLocationItems.lookupByIds(
      params,
    );

    // Sort the fetched location data based on the provided IDs.
    const sortData = sortCountryByValues(ids, res.list);

    // Map the sorted location data to options.
    const optionsLocations = mapValueCountryToOption(sortData);

    // Get the leaf nodes from the mapped options.
    const flattenTree = getLeafNodes(optionsLocations);

    // Update the state with the mapped options and extend data.
    setState(draft => {
      draft.selectedTreeData = mapOptionWithExtendValue(
        optionsLocations,
        extend,
      );
    });

    // Set the preview column with the mapped leaf nodes and extend data.
    setPreviewColumn(mapOptionWithExtendValue(flattenTree, extend));
  };
  const updateValue = (newValue, newLabel, newExtend) => {
    props.onChange(newValue, newLabel, newExtend);
  };
  const onSelect = (selectData, treeData) => {
    setPreviewColumn(selectData);
    const arrValues = [];
    const arrLabels = [];
    const arrExtendValue = [];
    selectData.forEach(item => {
      if (item.isExtendValue) {
        arrExtendValue.push(item.key);
        arrValues.push(item.key);
        arrLabels.push(item.key);
      } else {
        arrValues.push(item.value);
        arrLabels.push(item.label);
      }
    });

    updateValue(arrValues, arrLabels, arrExtendValue);

    setState(draft => {
      draft.selectedTreeData = treeData;
    });
  };

  const onSearch = valueSearch => {
    if (!isMulti) {
      const arrValues = [];
      const arrLabels = [];
      arrValues.push(valueSearch);
      arrLabels.push(valueSearch);

      updateValue(arrValues, arrLabels);
    }

    setState(draft => {
      draft.search = valueSearch;
      draft.page = 1;
      draft.limit = LIMIT;
    });

    fetchData(
      false,
      1,
      LIMIT,
      valueSearch,
      state.groupSelect.selectGroup?.map(item => +item.split('-')[1]),
    );

    // const searchData = [];
    // mapPreviewColumn.forEach(each => {
    //   if (
    //     each.value.toLowerCase().indexOf(valueSearch.toLowerCase()) !== -1 ||
    //     each.value.toString().indexOf(valueSearch) !== -1 ||
    //     each.name.toLowerCase().indexOf(valueSearch.toLowerCase()) !== -1 ||
    //     each.name.toString().indexOf(valueSearch) !== -1
    //   ) {
    //     searchData.push(each);
    //   }
    //   setStateSearch(draft => {
    //     draft.searchData = searchData;
    //   });
    // });
  };

  const fetchDataCountry = async () => {
    const resCountry = await ObjectServices.suggestionLocationItems.getList({
      data: {
        type: 'country',
        search: '',
        isPaginate: false,
      },
    });

    setGroupSelect(resCountry.list);
  };

  const onLoadMore = () => {
    const { isFocus, page } = scrollYearNow(props.semantic);
    if (!state.showCheckedOnly) {
      const nextPage = isFocus
        ? state.page > page
          ? state.page + 1
          : page + 1
        : state.page + 1;

      if (state.hasLoadMore) {
        fetchData(true, nextPage, LIMIT, state.search);
      }
    }
  };
  const initScreen = temptValue => {
    const imutableValue = OrderedSet(temptValue);

    setValue(imutableValue);
    // lần render đầu chỉ có value & không có options => không thể map value=>label
    // const imutableLabel = OrderedSet(temptLabel);
    // setLabel(imutableLabel);

    if (props.config.isPk !== 1) {
      // setState(draft => {
      //   draft.showCheckedOnly = imutableValue.size > 0;
      // });
      setOptionCurrentChecked(imutableValue);
    }
    if (!isId) {
      const fetchDataFunction = isShowSelectGroup
        ? fetchDataLocationGroup
        : fetchDataFirst;

      fetchDataFunction(temptValue, extend);
    }
    // call API lookup trước khi mở Modal (dùng cho trường hợp === id)
    if (!firstLoad && imutableValue.size > 0 && props.config.isPk === 1) {
      setFirstLoadLookup(false);
      if (isId) {
        fetchDataFristCustomerId(temptValue, extend);
      } else if (isDatetimeV2) {
        fetchDataDateTimeV2(temptValue, extend);
      } else {
        fetchDataFirst(temptValue, extend);
      }
      // case isPK not found (AM)
    } else if ((props.config.isPk === 0 || !props.config.isPk) && isId) {
      fetchDataFirst(temptValue, extend);
      setFirstLoadLookup(false);
    } else {
      setFirstLoadLookup(false);

    }

    if (isShowSelectGroup) {
      fetchDataCountry();
    }
  };
  const initState = () => {
    setMapOptions({});
    setOptions([]);
    setLoading(false);
    setLoadingSelect(false);
    setFirstLoadLookup(true);
    setFristLoad(false);
    setState(draft => {
      draft.page = 1;
      draft.limit = scrollYearNow(props.semantic).limit;
      draft.search = '';
      draft.hasLoadMore = true;
      draft.showCheckedOnly = false;
      draft.isSelectAll = false;
    });
  };
  useEffect(() => {
    setPreviewColumn([]);
    setLabel(OrderedSet([]));
    setValue(OrderedSet([]));
  }, [operator, propertyCode]);

  useEffect(() => {
    initState();
  }, [props.config.feKey, props.operatorValue, isEncyptable]);
  useEffect(() => {
    initScreen(
      // initValue || value cho lan render dau
      safeParse(
        props.initValue.length > 0
          ? props.semantic
            ? props.value
            : props.initValue
          : props.value,
        [],
      ),
    );
  }, [
    props.initValue,
    props.config.feKey,
    props.operatorValue,
    isEncyptable,
    // cho Journey conditionYes
    props.componentKey,
  ]);

  useEffect(() => {
    if (!isMulti && open && value.size > 0) {
      let indexItem = -1;

      options.forEach((tmptItem, index) => {
        if (value.has(tmptItem.value)) {
          indexItem = index;
          return indexItem;
        }
      });

      if (indexItem === -1) {
        setIndexCurrentValue(0);
      } else {
        setIndexCurrentValue(indexItem);
      }
    }
    // reset indexCurrentValue every time willUnMount
    return () => {
      if (!isMulti && !open) {
        setIndexCurrentValue(0);
      }
    };
  }, [open, options]);

  useEffect(() => {
    if (open && props.fullWidthPopover) {
      const button = document.getElementById(componentKey);
      const { width } = button.getBoundingClientRect();

      if (width) {
        setState(draft => {
          draft.widthSpan = (width * 80) / 100;
        });
      }
    }
  }, [open]);

  // didmount if isSegmentIds will call lookup
  // use for lookup dropdown label, not include into value on popup
  useEffect(() => {
    if (objLookup.status) {
      const params = {
        config: props.config,
        data: props.value,
        objectName: objLookup.code,
      };
      AbtractsService.lookupInfo.get(params).then(res => {
        if (res.code === 200) {
          const arrLabels = [];
          res.data.forEach(each => {
            arrLabels.push(each[objLookup.pathLabel]);
          });
          setLabel(OrderedSet(arrLabels));
        }
      });
    }

    // Handle remove tabindex=-1 for  MuiPopover-paper to fix bug input not focus
    const MuiPopoverEls = document.querySelectorAll(
      '.MuiPopover-root > .MuiPaper-root.MuiPopover-paper',
    );

    if (MuiPopoverEls && MuiPopoverEls.length) {
      MuiPopoverEls.forEach(el => {
        el.removeAttribute('tabindex');
      });
    }
  }, []);

  const onClick = () => {
    if (!firstLoad) {
      fetchData(false, state.page, state.limit, state.search);
    }
  };

  const groupSelectProps = {
    name: 'Country',
    options: state.groupSelect.groupItems,
    onApply: list => {
      const ids = list.map(item => +item.split('-')[1]);

      setState(draft => {
        draft.groupSelect.selectGroup = list;
      });

      fetchData(false, 1, state.limit, state.search, ids);
    },
    selected: state.groupSelect.selectGroup,
  };

  const objectName = useMemo(() => {
    const { itemTypeName, propertyName } = property;
    let name = `${property.propertyName || property.translateLabel}`;

    if (itemTypeName === 'city' && propertyName === 'name') {
      name = 'City/ District';
    }

    if (itemTypeName === 'province' && propertyName === 'name') {
      name = 'State/ Province';
    }

    if (itemTypeName === 'country' && propertyName === 'name') {
      name = 'Country';
    }

    return name;
  }, [property]);

  return (
    <MatchesAnySelect
      {...(props.isFilter
        ? {
            maxSelectedItem: 100,
            maxLengthEachItem: 200,
          }
        : {})}
      objectName={objectName}
      dataType={property.dataType}
      searchValue={state.search}
      loading={isLoading}
      items={
        property.autoSuggestion === 0 &&
        property.dataType === 'string' &&
        property.itemTypeName
          ? []
          : options
      }
      selectedItems={mapPreviewColumn}
      selectedTreeData={state.selectedTreeData}
      error={props.error ? props.helperText : undefined}
      onChange={onSelect}
      onLoadMore={onLoadMore}
      onChangeSearch={onSearch}
      {...(props.isFilter
        ? {
            popoverProps: {
              // align: {
              //   overflow: {
              //     shiftY: false,
              //     shiftX: true,
              //   },
              //   offset: [0, -100],
              // },
              placement: 'bottomLeft',
              // getPopupContainer: trigger => trigger.parentNode,
            },
          }
        : {
            popoverProps: {
              placement: 'topLeft',
            },
          })}
      onClick={onClick}
      customItemRenders={{
        title(item) {
          if (propertyCode === 'segment_ids') {
            const url = toSegmentInsight({
              portalId: getPortalId(),
              segmentId: item.id,
            });
            return (
              <Typography.Link href={url} target="_blank">
                {item.title?.toString()}
              </Typography.Link>
            );
          }
        },
      }}
      // case is member of
      {...(propertyCode === 'segment_ids'
        ? {
            renderExtraValues: [
              {
                key: 'segmentSize',
                label: 'Members',
                render(item) {
                  const column = {
                    displayFormat: {
                      type: 'NUMBER',
                      config: { decimal: '.', decimalPlace: 0, group: ',' },
                    },
                  };
                  return getCellDisplayNumber(column, item.segmentSize || 0);
                },
              },
            ],
          }
        : {})}
      {...(property.autoSuggestion === 0 &&
      property.dataType === 'string' &&
      property.itemTypeName
        ? {
            listEmptyProps: {
              icon: <LongerIcon />,
              description: (
                <>
                  You don't have any value <br />
                  Enable Auto suggestion or click Extend Value to add value
                </>
              ),
            },
          }
        : {})}
      groupSelectProps={isShowSelectGroup && groupSelectProps}
    />
  );
});

export default PopoverDropdown;
