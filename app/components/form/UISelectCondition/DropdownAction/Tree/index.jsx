/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable no-lonely-if */
/* eslint-disable react/no-array-index-key */
import React, { useState, useEffect, memo, useCallback } from 'react';
// import PrivateIcon from 'components/Icon/Private';
import { OrderedMap } from 'immutable';
import { UITextSearch as TextSearch } from '@xlab-team/ui-components';
import { Scrollbars, useDeepCompareEffect } from '@antscorp/antsomi-ui';
import NavItem, { UINavListItem } from './Item';
import Level from './Level';
import { Nodata, StyleWrapperFooter, WrapperSearchBox } from './styled';
import { serializeValue } from '../../utils';
import { searchTree, checkActive } from './utils';
import { STATUS_ITEM_CODE } from '../../../../../utils/constants';
import { generateKey } from '../../../../../utils/common';
import { DataIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { getTranslateMessage } from '../../../../../containers/Translate/util';

function Tree(props) {
  const {
    isMulti,
    mode,
    onlyHasData,
    isFooter = true,
    searchNodataLabel,
    searchNodataIcon,
  } = props;
  const [cacheKey, setCacheKey] = useState(generateKey());
  const [tree, setTree] = useState(OrderedMap({}));
  const [treeSearch, setTreeSearch] = useState(null);
  const [temptTree, setTemptTree] = useState(OrderedMap({}));
  const [search, setSearch] = useState('');

  const [active, setActive] = useState([]);

  // const wrapperRef = useRef();

  const initTree = useCallback(
    (options, value) => {
      if (Array.isArray(options) && options.length > 0) {
        const newOptions = options.map(opt => ({
          ...opt,
          label: opt.eventPropertyDisplay
            ? opt.eventPropertyDisplay
            : opt.label,
        }));

        const tempt = serializeValue(newOptions, value, isMulti, onlyHasData);

        setTree(tempt);
      } else {
        setTree(OrderedMap({}));
      }
    },
    [isMulti, onlyHasData],
  );

  const initTreeSearch = useCallback(
    (options, value) => {
      const newOptions = options.map(opt => ({
        ...opt,
        label: opt.eventPropertyDisplay ? opt.eventPropertyDisplay : opt.label,
      }));

      const tempt = serializeValue(newOptions, value, isMulti, onlyHasData);

      setTreeSearch(tempt);
    },
    [isMulti, onlyHasData],
  );

  useEffect(() => {
    if (props.isOpen) {
      setSearch('');
    }
  }, [props.isOpen]);

  useDeepCompareEffect(() => {
    if (props.optionSearch && props.optionSearch.length) {
      initTreeSearch(props.optionSearch, props.value);
    }
    initTree(props.options, props.value);
  }, [
    props.options,
    props.optionSearch,
    props.value,
    initTree,
    initTreeSearch,
  ]);

  useDeepCompareEffect(() => {
    if (!isMulti) {
      initTree(props.options, props.value);
    }
  }, [initTree, isMulti, props.options, props.value]);

  useEffect(() => {
    // if (props.isOpen){
    setTimeout(() => {
      onScroll();
    }, 200);
    // }
    return () => {
      clearTimeout(onScroll());
    };
  }, [props.isOpen, props.indexCurrentValue, props.indexChild]);

  const onScroll = () => {
    if (
      props.options !== undefined &&
      props.options.length > 0 &&
      props.indexCurrentValue >= 0
    ) {
      const searchLength = search.trim().length;
      let currentItem;
      if (props.isOpenParent && props.indexChild >= 0) {
        if (searchLength > 0) {
          if (
            props.temptOptions.length > 0 &&
            props.temptOptions[props.indexCurrentValue]
          ) {
            if (
              props.temptOptions[props.indexCurrentValue].options &&
              props.temptOptions[props.indexCurrentValue].options.length > 0
            ) {
              if (
                props.temptOptions[props.indexCurrentValue].options[
                  props.indexChild
                ]
              ) {
                currentItem =
                  props.temptOptions[props.indexCurrentValue].options[
                    props.indexChild
                  ].value;
              }
            } else {
              currentItem = null;
            }
          }
        } else {
          if (
            props.options.length > 0 &&
            props.options[props.indexCurrentValue]
          ) {
            if (
              props.options[props.indexCurrentValue].options &&
              props.options[props.indexCurrentValue].options.length > 0
            ) {
              if (
                props.options[props.indexCurrentValue].options[props.indexChild]
              ) {
                currentItem =
                  props.options[props.indexCurrentValue].options[
                    props.indexChild
                  ].value;
              } else {
                currentItem = null;
              }
            }
          }
        }
      } else {
        if (searchLength > 0) {
          if (
            props.temptOptions.length > 0 &&
            props.temptOptions[props.indexCurrentValue]
          ) {
            if (props.temptOptions[props.indexCurrentValue]) {
              currentItem = props.temptOptions[props.indexCurrentValue].value;
            } else {
              currentItem = null;
            }
          }
        } else {
          if (
            props.options.length > 0 &&
            props.options[props.indexCurrentValue]
          ) {
            currentItem = props.options[props.indexCurrentValue].value;
          } else {
            currentItem = null;
          }
        }
      }

      if (currentItem !== null || currentItem !== undefined) {
        const currentElement = document.getElementById(
          `${cacheKey}-${currentItem}`,
        );

        if (currentElement !== null) {
          // scrollToElement(wrapperRef, currentElement);
        }
      }
    }
  };
  const clickTreeItem = (isParent, open, parentPath, itemKey, name) => {
    if (isMulti && mode === 'tags') {
      if (props.optionSearch && props.optionSearch.length) {
        const index = props.optionSearch.findIndex(item => item.value === name);
        if (index > -1) {
          props.onChange(props.optionSearch[index]);
        }
      } else {
        const index = props.options.findIndex(item => item.value === name);
        if (index > -1) {
          props.onChange(props.options[index]);
        }
      }

      return;
    }

    // setTree(tree.setIn([...parentPath, itemKey, 'active'], true));
    const temptPath = [...parentPath, itemKey];
    let tempt = search.trim().length > 0 ? temptTree : tree;
    // console.log({ isParent, active });
    if (isParent) {
      // no set open for item
      tempt = tree.setIn([...temptPath, 'open'], !open);

      const index =
        search.trim().length > 0
          ? temptTree.getIn([...temptPath, 'index'])
          : tree.getIn([...temptPath, 'index']);
      props.handleOnClick(!open, index);
    } else {
      // no set active for parent
      if (active.length > 0) {
        tempt = tempt.setIn([...active, 'active'], false);
      }
      tempt = tempt.setIn([...temptPath, 'active'], true);
      setActive(temptPath);
      const index =
        search.trim().length > 0
          ? temptTree.getIn([...temptPath, 'index'])
          : tree.getIn([...temptPath, 'index']);

      props.handleOnClick(!open, index);

      props.onChange(tempt.getIn([...temptPath, 'item']));
    }
    setTree(tempt);
  };

  const onSearch = value => {
    setSearch(value);
    const arrayTree = [];
    let hasChilds = false;

    if (value.trim().length > 0) {
      // action search
      let result;
      if (treeSearch) {
        result = searchTree(value.trim(), treeSearch);
      } else {
        result = searchTree(value.trim(), tree);
      }

      result.forEach(item => {
        if (item.get('childs')) {
          // kiểm tra xem option có child hay không
          hasChilds = true;
          const objectGroup = {
            groupId: item.get('item').groupId,
            groupName: item.get('item').groupName,
            label: item.get('item').label,
          };

          const arrayChilds = [];

          item.get('childs').forEach(obj => {
            if (item.get('hidden') === false) {
              const objectChild = obj.get('item');
              objectChild.index = obj.get('index');
              arrayChilds.push(objectChild);
            }
          });

          objectGroup.options = arrayChilds;
          arrayTree.push(objectGroup);
        } else if (item.get('hidden') === false) {
          // Trường hợp là single element
          // const itemTreeToObject = {
          //   label: item.get('label'),
          //   value: item.get('name'),

          // };
          arrayTree.push(item.getIn(['item']));
        }
      });
      setTemptTree(result);
    }

    if (arrayTree.length > 0) {
      setCacheKey(generateKey());
    }

    props.handleTextSearch(arrayTree, value.trim().length, hasChilds);
  };

  const showContent = (path, level, childs) => {
    const result = [];
    childs.forEach((item, key) => {
      if (
        item.get('hidden') === false &&
        (item.get('statusItemCode') === STATUS_ITEM_CODE.ACTIVE ||
          item.get('statusItemCode') === STATUS_ITEM_CODE.DISABLED)
      ) {
        const isActive = checkActive(isMulti, props.value, item);
        // const isActive = !isMulti
        //   ? item.get('active')
        //   : props.value.has(item.get('name'));
        if (item.get('childs') && !props.onlyParent) {
          const newPath = [...path, key, 'childs'];
          const isOpen = item.get('open') || props.isParentOpen;
          result.push(
            <Level key={key} level={level}>
              <li
                data-test={`lv-${level} ${item.get('dataTest') || ''}`}
                id={item.get('name')}
              >
                <NavItem
                  isMulti={isMulti}
                  mode={mode}
                  key={key}
                  itemKey={key}
                  isParent
                  onlyParent={props.onlyParent}
                  onlyChild={props.onlyChild}
                  name={item.get('name')}
                  label={item.get('label')}
                  tooltip={item.get('tooltip')}
                  translateCode={item.get('translateCode')}
                  open={isOpen}
                  active={isActive}
                  disabled={item.get('disabled')}
                  path={path}
                  onClick={clickTreeItem}
                  hover={item.get('index') === props.indexCurrentValue}
                  isOpenParent={props.isOpenParent}
                  pressEnter={props.pressEnter}
                  index={item.get('index')}
                  isPress={props.isPress}
                  ItemComponent={props.ItemComponent}
                  FooterComponent={props.FooterComponent}
                />
                {isOpen && (
                  <UINavListItem>
                    {showContent(newPath, level + 1, item.get('childs'))}
                  </UINavListItem>
                )}
              </li>
            </Level>,
          );
        } else {
          result.push(
            <Level key={key} level={level}>
              <li
                data-test={`lv-${level} ${item.get('dataTest') || ''}`}
                id={`${cacheKey}-${item.get('name')}`}
              >
                <NavItem
                  key={key}
                  isMulti={isMulti}
                  mode={mode}
                  itemKey={key}
                  isParent={false}
                  isFirstLevel={level === 0}
                  onlyParent={props.onlyParent}
                  onlyChild={props.onlyChild}
                  name={item.get('name')}
                  label={item.get('label')}
                  tooltip={item.get('tooltip')}
                  translateCode={item.get('translateCode')}
                  open={false}
                  active={isActive}
                  disabled={item.get('disabled')}
                  path={path}
                  onClick={clickTreeItem}
                  hover={
                    props.isOpenParent
                      ? item.get('index') ===
                        `${props.indexCurrentValue}-${props.indexChild}`
                      : item.get('index') === props.indexCurrentValue
                  }
                  ItemComponent={props.ItemComponent}
                  FooterComponent={props.FooterComponent}
                />
                <UINavListItem />
              </li>
            </Level>,
          );
        }
      }
    });

    if (result.length > 0) {
      if (level === 0) {
        return (
          <Scrollbars autoHeight autoHeightMax={200}>
            {result}
          </Scrollbars>
        );
      }

      return <div>{result}</div>;
    }

    if (searchNodataLabel) {
      return (
        <Nodata>
          <div className="wrapper-data-icon">
            {searchNodataIcon || (
              <DataIcon width={30} height={30} color={globalToken.bw5} />
            )}
          </div>
          <p className="text-no-data">{searchNodataLabel}</p>
        </Nodata>
      );
    }

    if (search !== '') {
      return (
        <Nodata>
          <div className="wrapper-data-icon">
            <DataIcon width={30} height={30} color={globalToken.bw5} />
          </div>
          <p className="text-no-data">
            {getTranslateMessage(
              props.searchNoDataTranslateCode,
              'No options found',
            )}
          </p>
        </Nodata>
      );
    }

    return (
      <Nodata>
        <div className="wrapper-data-icon">
          <DataIcon width={30} height={30} color={globalToken.bw5} />
        </div>
        <p className="text-no-data">
          {getTranslateMessage(props.searchNoDataTranslateCode, 'No options')}
        </p>
      </Nodata>
    );
  };

  // searchTranslateCode={this.props.searchTranslateCode}
  // searchNoDataTranslateCode={this.props.searchNoDataTranslateCode}

  // const showNodata = searchLength => {
  //   if (tree.size === 0) {
  //     return <Nodata>No options</Nodata>;
  //   }
  //   if (searchLength > 0 && temptTree.size === 0) {
  //     return <Nodata>No options</Nodata>;
  //   }
  //   return null;
  // };
  const searchLength = search.trim().length;
  return (
    <div className="browse-folders__folder-tree">
      {/* isOpen: only focus when component mounted */}
      {props.isSearchable && props.isOpen && (
        <div className="width-100 m-bottom-2">
          <WrapperSearchBox>
            <TextSearch
              classDiv="search-filter"
              placeholder={props.searchPlaceholder}
              translateCode={props.searchPlaceholderTranslateCode}
              isTimeout
              onChange={onSearch}
              value={search}
              focus
              type="text"
              // use="full-border"
            />
          </WrapperSearchBox>
        </div>
      )}
      {props.isOpen && showContent([], 0, searchLength > 0 ? temptTree : tree)}
      {props.FooterComponent && isFooter && (
        // <props.FooterComponent popupStateTree={props.popupStateTree} />
        <StyleWrapperFooter className="border-top">
          <props.FooterComponent
            callback={props.callback}
            popupStateTree={props.popupStateTree}
            disabled={props.value.size === 0}
          />
        </StyleWrapperFooter>
      )}
    </div>
  );
}

export default memo(Tree);
