/* eslint-disable no-else-return */
/* eslint-disable react/no-did-update-set-state */
/* eslint-disable react/no-did-mount-set-state */
/* eslint-disable react/prop-types */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable consistent-return */
import React, { memo } from 'react';
import PropTypes from 'prop-types';
import cls from 'classnames';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import TranslateMessage from 'containers/Translate';
import DropdownAction from './DropdownAction';
import { Typography } from '@antscorp/antsomi-ui';

import { safeParse } from '../../../utils/common';
import TRANSLATE_KEY from '../../../messages/constant';
import { getTranslateMessage } from '../../../containers/Translate/util';

// Styled
import { ListViewModeWrapper, ItemViewMode, Wrapper } from './styled';
import { TitleTime } from '../../common/UISchedulerTrigger/styled';
import DropdownTagsLabel from './DropdownAction/DropdownTagsLabel';

// Constant
import { DATA_TEST } from './constants';

class UISelectCondition extends React.PureComponent {
  onChange = value => {
    if (this.props.use === 'tree' || this.props.use === 'inline') {
      if (this.props.returnProperty) {
        this.props.onChange({ dataCallback: this.props.property, value });
      } else if (this.props.returnName) {
        this.props.onChange(this.props.name, value);
      } else if (this.props.index) {
        this.props.onChange({ value, index: this.props.index });
      } else {
        this.props.onChange(value);
      }
    } else {
      const propsValue = safeParse(this.props.value, null);
      // eslint-disable-next-line no-lonely-if
      if (propsValue === null || value.value !== propsValue.value) {
        this.props.onChange(this.props.property, value);
      }
    }
  };

  renderViewMode(option, label) {
    if (this.props.renderViewMode) {
      return this.props.renderViewMode(option, label);
    }

    if (this.props.isMulti && this.props.mode === 'tags') {
      return (
        <DropdownTagsLabel
          options={option}
          placeholder=""
          maxTags={2}
          isViewMode={this.props.isViewMode}
        />
      );
    }

    if (label) {
      return (
        <ListViewModeWrapper
          className="list"
          style={
            !this.props.isViewMode
              ? {
                  paddingLeft: 4,
                  margin: '4px 0 2px',
                }
              : {
                  paddingLeft: 4,
                }
          }
        >
          <ItemViewMode className="item">{option.label}</ItemViewMode>
        </ListViewModeWrapper>
      );
    } else {
      return (
        <Typography.Text
          style={{ lineHeight: '30px' }}
          ellipsis={{ tooltip: option.label }}
        >
          {option.label}
        </Typography.Text>
      );
    }
  }

  render() {
    return (
      <Wrapper
        style={{ ...this.props.inputWrapperStyle }}
        data-test={DATA_TEST.WRAPPER}
      >
        {this.showUI(this.props.use)}
      </Wrapper>
    );
  }

  showUI = use => {
    const { value, isViewMode, label, hideLabelSelect } = this.props;

    if (use === 'tree') {
      return (
        // <div className={this.props.className}>
        <WrapperDisable
          disabled={this.props.disabled}
          className={cls(this.props.className, {
            disabled: this.props.disabled,
          })}
        >
          {!hideLabelSelect && label && (
            <TitleTime className="title" data-test={DATA_TEST.LABEL}>
              {label}
            </TitleTime>
          )}

          {isViewMode && value ? (
            this.renderViewMode(value, label)
          ) : (
            <DropdownAction
              searchPlaceholderTranslateCode={
                this.props.searchPlaceholderTranslateCode
              }
              selectWidth={this.props.selectWidth}
              searchNoDataTranslateCode={this.props.searchNoDataTranslateCode}
              searchPlaceholder={this.props.searchPlaceholder}
              noDataTranslateCode={this.props.noDataTranslateCode}
              isMulti={this.props.isMulti}
              mode={this.props.mode}
              feKey={this.props.feKey}
              display={this.props.display}
              className={this.props.className}
              isSearchable={this.props.isSearchable}
              onlyParent={this.props.onlyParent}
              onlyHasData={this.props.onlyHasData}
              onlyChild={this.props.onlyChild}
              isParentOpen={this.props.isParentOpen}
              placeholder={this.props.placeholder}
              placeholderTranslateCode={this.props.placeholderTranslateCode}
              options={this.props.options || []}
              optionSearch={this.props.optionSearch || []}
              mapOptions={this.props.mapOptions}
              use={this.props.use}
              value={value}
              onChange={this.onChange}
              errors={this.props.errors}
              isShowLabelFlagSelected={this.props.isShowLabelFlagSelected}
              labelSelected={this.props.labelSelected}
              translateCodeLabelSelected={this.props.translateCodeLabelSelected}
              fullWidthPopover={this.props.fullWidthPopover}
              labelWidth={this.props.labelWidth}
              ItemComponent={this.props.ItemComponent}
              FooterComponent={this.props.FooterComponent}
              DropdownButnComponent={this.props.DropdownButnComponent}
              minWidth={this.props.minWidth}
              tooltip={this.props.tooltip}
              isErrBtnBottom={this.props.isErrBtnBottom}
              isDisabledBorder={this.props.isDisabledBorder}
              type={this.props.type}
              labelButton={this.props.labelButton}
              widthButton={this.props.widthButton}
              callback={this.props.callback}
              renderButtonDropdown={this.props.renderButtonDropdown}
              popoverProps={this.props.popoverProps}
              isFooter={this.props.isFooter}
              searchNodataLabel={this.props.searchNodataLabel}
              searchNodataIcon={this.props.searchNodataIcon}
              disabled={this.props.disabled}
              styleLabelDropdown={this.props.styleLabelDropdown}
            />
          )}
        </WrapperDisable>
        // </div>
      );
    } else if (use === 'inline') {
      return (
        <WrapperDisable
          disabled={this.props.disabled}
          className={cls({
            disabled: this.props.disabled,
          })}
        >
          <DropdownAction
            searchPlaceholderTranslateCode={
              this.props.searchPlaceholderTranslateCode
            }
            selectWidth={this.props.selectWidth}
            searchNoDataTranslateCode={this.props.searchNoDataTranslateCode}
            searchPlaceholder={this.props.searchPlaceholder}
            noDataTranslateCode={this.props.noDataTranslateCode}
            isMulti={this.props.isMulti}
            mode={this.props.mode}
            feKey={this.props.feKey}
            display={this.props.display}
            className={this.props.className}
            isSearchable={this.props.isSearchable}
            onlyParent={this.props.onlyParent}
            onlyHasData={this.props.onlyHasData}
            onlyChild={this.props.onlyChild}
            isParentOpen={this.props.isParentOpen}
            placeholder={this.props.placeholder}
            placeholderTranslateCode={this.props.placeholderTranslateCode}
            options={this.props.options}
            mapOptions={this.props.mapOptions}
            value={value}
            use={this.props.use}
            onChange={this.onChange}
            errors={this.props.errors}
            labelSelected={this.props.labelSelected}
            translateCodeLabelSelected={this.props.translateCodeLabelSelected}
            fullWidthPopover={this.props.fullWidthPopover}
            labelWidth={this.props.labelWidth}
            ItemComponent={this.props.ItemComponent}
            FooterComponent={this.props.FooterComponent}
            DropdownButnComponent={this.props.DropdownButnComponent}
            minWidth={this.props.minWidth}
            callback={this.props.callback}
            isFooter={this.props.isFooter}
            disabled={this.props.disabled}
          />
        </WrapperDisable>
      );
    }

    return (
      <div className="private-form__set private-form__set--no-label text-search-filter-1">
        <div className="private-form__control-wrapper" tabIndex="-1">
          {this.props.label !== '' && (
            <label className="private-form__label">
              <TranslateMessage
                code={this.props.translateCode}
                defaultMessage={this.props.label}
              />
              {this.props.required ? <span> *</span> : null}
            </label>
          )}
          <WrapperDisable
            disabled={this.props.disabled}
            className={cls({
              disabled: this.props.disabled,
            })}
          >
            <DropdownAction
              searchPlaceholderTranslateCode={
                this.props.searchPlaceholderTranslateCode
              }
              selectWidth={this.props.selectWidth}
              searchNoDataTranslateCode={this.props.searchNoDataTranslateCode}
              searchPlaceholder={this.props.searchPlaceholder}
              noDataTranslateCode={this.props.noDataTranslateCode}
              isMulti={this.props.isMulti}
              mode={this.props.mode}
              feKey={this.props.feKey}
              display={this.props.display}
              className={this.props.className}
              isSearchable={this.props.isSearchable}
              onlyParent={this.props.onlyParent}
              onlyHasData={this.props.onlyHasData}
              isParentOpen={this.props.isParentOpen}
              placeholder={this.props.placeholder}
              placeholderTranslateCode={this.props.placeholderTranslateCode}
              // options={property.propertyValues}
              // mapOptions={property.mapPropertyValues}
              value={value}
              onChange={this.onChange}
              errors={this.props.errors}
              use={this.props.use}
              fullWidthPopover={this.props.fullWidthPopover}
              labelWidth={this.props.labelWidth}
              ItemComponent={this.props.ItemComponent}
              FooterComponent={this.props.FooterComponent}
              DropdownButnComponent={this.props.DropdownButnComponent}
              minWidth={this.props.minWidth}
              callback={this.props.callback}
              isFooter={this.rops.isFooter}
              disabled={this.props.disabled}
            />
          </WrapperDisable>
        </div>
      </div>
    );
  };
}

UISelectCondition.defaultProps = {
  searchPlaceholderTranslateCode: TRANSLATE_KEY._ACT_SEARCH,
  searchNoDataTranslateCode: TRANSLATE_KEY._INFO_NO_DATA,
  noDataTranslateCode: TRANSLATE_KEY._INFO_NO_DATA,
  searchPlaceholder: getTranslateMessage(
    TRANSLATE_KEY._ACT_SEARCH,
    'Search...',
  ),
  className: '',
  use: 'form',
  isMulti: false,
  mode: 'default',
  isSearchable: true,
  onlyParent: false,
  onlyHasData: true,
  returnProperty: false,
  returnName: false,
  placeholder: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
    'Select a column',
  ),
  placeholderTranslateCode: TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
  display: 'button',
  disabled: false,
  errors: [],
  labelSelected: 'selected',
  fullWidthPopover: true,
  isParentOpen: false,
  labelWidth: '100%',
  ItemComponent: null,
  DropdownButnComponent: null,
  FooterComponent: null,
  minWidth: '120px',
  isDisabledBorder: false,
  inputWrapperStyle: {},
};
UISelectCondition.propTypes = {
  searchPlaceholderTranslateCode: PropTypes.any,
  searchPlaceholder: PropTypes.string,
  searchNoDataTranslateCode: PropTypes.any,
  noDataTranslateCode: PropTypes.any,
  use: PropTypes.oneOf(['form', 'tree', 'inline']),
  isMulti: PropTypes.bool,
  mode: PropTypes.oneOf(['default', 'tags']),
  className: PropTypes.string,
  isSearchable: PropTypes.bool,
  onlyParent: PropTypes.bool,
  onlyHasData: PropTypes.bool,
  returnProperty: PropTypes.bool,
  returnName: PropTypes.bool,
  placeholder: PropTypes.string,
  placeholderTranslateCode: PropTypes.any,
  display: PropTypes.string,
  disabled: PropTypes.bool,
  errors: PropTypes.array,
  labelSelected: PropTypes.string,
  fullWidthPopover: PropTypes.bool,
  isParentOpen: PropTypes.bool,
  labelWidth: PropTypes.any,
  ItemComponent: PropTypes.func,
  DropdownButnComponent: PropTypes.func,
  FooterComponent: PropTypes.func,
  isViewMode: PropTypes.bool,
  isDisabledBorder: PropTypes.bool,
  minWidth: PropTypes.any,
  inputWrapperStyle: PropTypes.object,
};

export default memo(UISelectCondition);
