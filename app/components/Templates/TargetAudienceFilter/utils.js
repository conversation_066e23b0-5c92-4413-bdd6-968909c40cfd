import { isEmpty } from 'lodash';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { safeParse } from '../../../utils/common';
import {
  MAP_OPTIONS_SELECT_SEGMENTS,
  MAP_CONDITION_TYPE_SEGMENT,
  OPTIONS_SELECT_SEGMENTS,
} from '../../common/UITargetSelection/utils';
import { SEGMENT } from '../../common/contants';
// import { useFetchAudiences, useFetchSegment } from './useFetchData';

export const labelTargetVisitor = getTranslateMessage(
  TRANSLATE_KEY._STORY_AUDIENCE_VISITOR,
  'Target to Visitors',
);
export const labelTargetCustomer = getTranslateMessage(
  TRANSLATE_KEY._STORY_AUDIENCE_CUSTOMER,
  'Target to Customers',
);
export const MAP_TRANSLATE = {
  addAudiences: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_MORE, `Add more`),
  andAudiences: getTranslateMessage(TRANSLATE_KEY._TITL_AND, 'And'),
  addMore: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_MORE, `Add more`),
  excludeAudiencesBtn: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXCLUDE_AUDIENCE,
    `Exclude audiences`,
  ),
  andAlso: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_AND_ALSO,
    `and also...`,
  ),
  includeAudiences: getTranslateMessage(
    TRANSLATE_KEY._TITL_INCLUDE_AUDIENCE,
    'Include Audiences:',
  ),
  excludeAudiences: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXCLUDE_AUDIENCE,
    'Exclude Audiences:',
  ),
  visitorSegment: getTranslateMessage(
    TRANSLATE_KEY._TITL_VISITOR_SEG,
    'Visitor segments',
  ),
  customerSegment: getTranslateMessage(
    TRANSLATE_KEY._TITL_CUSTOMER_SEG,
    'Customer segments',
  ),
  visitorSpecific: getTranslateMessage(
    TRANSLATE_KEY._TITL_SPECIFIC_VISITOR,
    'Specific visitors',
  ),
  customerSpecific: getTranslateMessage(
    TRANSLATE_KEY._TITL_SPECIFIC_CUSTOMER,
    'Specific customers',
  ),
  atleast1Segment: getTranslateMessage(
    TRANSLATE_KEY._NOTI_SELECT_LEAST_ONE_OPTION,
    'Must select at least one option.',
  ),
  visitorId: getTranslateMessage(TRANSLATE_KEY._TITL_VISITOR_ID, 'Visitor ID'),
  customerName: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_CUSTOMER,
    'Customer',
  ),
};

export const MAP_AUDIENCE_TARGET = {
  '-1007@@segmentAudiences': {
    value: '-1007@@segmentAudiences',
    itemTypeId: -1007,
    audienceTypes: 'segmentAudiences',
    label: MAP_TRANSLATE.visitorSegment,
    disabled: false,
  },

  '-1003@@segmentAudiences': {
    value: '-1003@@segmentAudiences',
    itemTypeId: -1003,
    audienceTypes: 'segmentAudiences',
    label: MAP_TRANSLATE.customerSegment,
    disabled: false,
  },
};
export const LIST_AUDIENCE_TARGET = Object.values(MAP_AUDIENCE_TARGET);

export const initState = () => ({
  isLoading: true, // thanhnt add vao for control data folow
  itemTypeId: '-1007',
  includeCluster: [],
  excludeCluster: [],
  mapCluster: {},
  tempItemTypeId: 0,
  tempAudienceTypes: '',
  isOpenModal: false,
  includeOptions: LIST_AUDIENCE_TARGET,
  excludeOptions: LIST_AUDIENCE_TARGET,
  triggerOut: 0,
  sortFilter: [],
  dataSelectedInclude: {},
  dataSelectedExclude: {},
  isInit: true,
});

const MAP_LABEL_BY_ITEM_TYPE_ID = {
  '-1007@@segmentAudiences': MAP_TRANSLATE.visitorSegment,
  '-1007@@specificAudiences': MAP_TRANSLATE.visitorId,
  '-1003@@segmentAudiences': MAP_TRANSLATE.customerSegment,
  '-1003@@specificAudiences': MAP_TRANSLATE.customerName,
};

export const MAP_ITEM_TYPE_ID_BY_LABEL = {
  [SEGMENT.VISITOR_SEGMENT]: '-1007@@segmentAudiences',
  [SEGMENT.CUSTOMER_SEGMENT]: '-1003@@segmentAudiences',
};

export const ITEM_TYPE_ID = {
  VISITOR: '-1007',
  CUSTOMER: '-1003',
};

export const AUDIENCES_TYPE = {
  INCLUDE: 'include',
  EXCLUDE: 'exclude',
};

export const initCluster = ({
  itemTypeId,
  audienceTypes,
  use = 'include',
}) => ({
  itemTypeId,
  audienceTypes,
  key: `${use}${itemTypeId}@@${audienceTypes}`,
  id: `${use}${itemTypeId}@@${audienceTypes}`,
  use,
  initData: {
    isInit: true,
    audienceIds: [],
    listAudience: [],
    dataTypeBelongToSegment: MAP_OPTIONS_SELECT_SEGMENTS.includes,
  },
  titleSelect:
    MAP_LABEL_BY_ITEM_TYPE_ID[`${itemTypeId}@@${audienceTypes}`] || '',
  titleHeader:
    use === 'include'
      ? MAP_TRANSLATE.includeAudiences
      : MAP_TRANSLATE.excludeAudiences,
});

export const validateDataAudienceSegment = data => {
  let isValidate = false;
  const { currentData = {} } = data;
  const { includeCluster = [] } = currentData;
  isValidate = includeCluster.some(item => {
    const { initData = {} } = item;
    const { audienceIds = [] } = initData;
    return audienceIds.length > 0;
  });

  return {
    status: isValidate,
    errors: isValidate ? [] : [MAP_TRANSLATE.atleast1Segment],
  };
};

export const toEntryFE = data => {
  const validateData = safeParse(data, {});
  const {
    itemTypeId = -1003,
    excludedAudiences = {},
    includedAudiences = {},
  } = validateData;
  const dataToUI = {
    itemTypeId,
    isInit: true,
    backup: {},
    currentData: {},
  };

  if (Object.keys(validateData).length === 0) {
    return dataToUI;
  }
  dataToUI.isInit = true;
  dataToUI.backup = {
    includedAudiences,
    excludedAudiences,
  };
  return dataToUI;
};

export const toEntryAPI = data => {
  const { itemTypeId, isInit, backup, currentData = {} } = data;
  let targetData = {
    includedAudiences: {
      filters: { OR: [] },
      audienceTypes: [],
      specificAudienceIds: [],
    },
    excludedAudiences: {
      audienceTypes: [],
      filters: { OR: [] },
      specificAudienceIds: [],
    },
  };

  const { excludeCluster = [], includeCluster = [] } = currentData;
  if (!isInit) {
    const mapClusterToAPI = (item, fieldName) => {
      targetData[fieldName].audienceTypes.push(item.audienceTypes);
      if (item.audienceTypes === 'segmentAudiences') {
        const { initData = {} } = item;
        const { listAudience = [], dataTypeBelongToSegment } = initData;
        const dataFilter = toAPIAudienceSegment(
          listAudience,
          dataTypeBelongToSegment.value,
          itemTypeId,
        );
        targetData[fieldName].filters = dataFilter;
      } else if (item.audienceTypes === 'specificAudiences') {
        const { initData = {} } = item;
        const { audienceIds } = initData;
        // console.log(
        //   '🚀 ~ TargetAudienceFilter ~ mapClusterToAPI ~ initData:',
        //   initData,
        // );
        targetData[fieldName].specificAudienceIds = audienceIds;
      }
    };
    includeCluster.forEach(each => mapClusterToAPI(each, 'includedAudiences'));
    excludeCluster.forEach(each => mapClusterToAPI(each, 'excludedAudiences'));
  } else {
    targetData = backup;
  }
  const dataToAPI = {
    audienceSegmentType:
      parseInt(itemTypeId) === -1007 ? 'user_segment' : 'customer_segment',
    itemTypeId: parseInt(itemTypeId),
    ...targetData,
  };

  return dataToAPI;
};
export const toAPIAudienceSegment = (
  data = [],
  type = OPTIONS_SELECT_SEGMENTS[0].value,
  itemTypeId = '-1003',
) => {
  if (data.length === 0) {
    return { OR: [{ AND: [] }] };
  }

  const dataFilter = { OR: [] };

  // if (type === OPTIONS_SELECT_SEGMENTS[0].value) {
  if (type === OPTIONS_SELECT_SEGMENTS[1].value) {
    const dataAND = [];
    data.forEach(element => {
      const temp = {
        column: 'segment_ids',
        data_type: 'array_number',
        operator: 'matches',
        feOperator: 'doesnt_include',
        value: [element.value],
        code: element.code,
        conditionType: MAP_CONDITION_TYPE_SEGMENT[itemTypeId],
      };
      dataAND.push(temp);
    });
    dataFilter.OR = [{ AND: dataAND }];
  } else {
    data.forEach(element => {
      const temp = {
        AND: [
          {
            column: 'segment_ids',
            data_type: 'array_number',
            operator: 'matches',
            feOperator: 'includes',
            value: [element.value],
            code: element.code,
            conditionType: MAP_CONDITION_TYPE_SEGMENT[itemTypeId],
          },
        ],
      };
      dataFilter.OR.push(temp);
    });
  }

  return dataFilter;
};
const mapAPIToCluster = (audiences, use, clusterName, itemTypeId, data) => {
  if (!isEmpty(audiences)) {
    const cluster = initCluster({
      itemTypeId,
      audienceTypes: 'segmentAudiences',
      use,
    });
    const initData = getAudienceFromFilter(audiences);
    if (initData.audienceIds.length > 0) {
      cluster.initData = { ...cluster.initData, ...initData };
      data[clusterName].push(cluster);
      data.mapCluster[cluster.id] = cluster;
    }
  }
};
export const mapBackupToState = (backup, itemTypeId) => {
  const data = {
    includeCluster: [],
    excludeCluster: [],
    mapCluster: {},
  };

  mapAPIToCluster(
    backup.filters,
    'include',
    'includeCluster',
    itemTypeId,
    data,
  );
  mapAPIToCluster(
    backup.excludedFilters,
    'exclude',
    'excludeCluster',
    itemTypeId,
    data,
  );
  return data;
};

export function getAudienceFromFilter(filters) {
  let audienceIds = [];
  let feOperator = 'includes';
  let dataTypeBelongToSegment = MAP_OPTIONS_SELECT_SEGMENTS.includes;
  const rulesOR = safeParse(filters.OR, []);
  rulesOR.forEach(ruleOR => {
    ruleOR.AND.forEach(item => {
      if (!Array.isArray(item.value)) {
        audienceIds = audienceIds.concat(item.value);
        ({ feOperator = 'includes' } = item);
      }
    });
  });
  dataTypeBelongToSegment = MAP_OPTIONS_SELECT_SEGMENTS[feOperator];
  return { audienceIds, dataTypeBelongToSegment };
}
export const toOnChangeAudience = (
  data,
  itemTypeId,
  dataSelectedExclude,
  dataSelectedInclude,
  currentData,
  sortFilter,
) => {
  const output = {
    isInit: false,
    dataSelected: data.listAudience,
    dataTypeBelongToSegment: data.dataTypeBelongToSegment,
    itemTypeId,
    dataSelectedInclude,
    dataSelectedExclude,
    currentData,
    sortFilter,
  };
  return output;
};
