/* eslint-disable react/prop-types */
import React, { memo } from 'react';
import classnames from 'classnames';

import { ButtonDrop } from './styled';
import { Icon } from '@antscorp/antsomi-ui';

const ButtonDropDown = props => (
  <React.Fragment>
    <ButtonDrop
      onClick={props.onClick}
      type="button"
      disabled={props.disabled}
      id={props.idButton}
      width={props.width}
      maxWidth={props.maxWidth}
      isErrBtnBottom={props.isErrBtnBottom}
      className={classnames('style-btn-dropdown', {
        'button-error': props.hasError,
      })}
      isDisabledBorder={props.isDisabledBorder}
    >
      {/* <LabelSelect>{props.children}</LabelSelect> */}
      {props.children}
      <Icon
        className="expand-more"
        type="icon-ants-expand-more"
        size={20}
        color="#595959"
      />
    </ButtonDrop>
  </React.Fragment>
);

export default memo(ButtonDropDown);
