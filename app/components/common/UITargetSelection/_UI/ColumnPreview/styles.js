/* eslint-disable no-nested-ternary */
import { Icon } from '@antscorp/antsomi-ui';
import styled from 'styled-components';

export const ItemLabel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  color: ${props =>
    !props.isDeleted ? '#000' : props.isId ? '#FF0000' : '#FFA500'};
  &:hover {
    .action-icon {
      visibility: visible;
    }
    .member-segment {
      visibility: hidden;
    }
    .td-action {
      .MuiButtonBase-root {
        color: #005eb8;
      }
    }
  }
  .td-action {
    right: 22px;
    .MuiIconButton-root {
      &:hover {
        background-color: unset !important;
      }
    }
  }
`;

export const Separator = styled.div`
  color: #595959;
  font-size: 10px;
  margin: 6px;
  text-transform: uppercase;
`;
export const StyleActionIcon = styled.div`
  display: flex;
  align-items: center;
  position: absolute;
  right: 25px;
  visibility: hidden;
  /* .td-action {
    right: 18px;
  } */
`;
export const IconStyled = styled(Icon)`
  color: #005eb8;
  margin-right: ${props => (props.isBlastCampaign ? '35px' : '10px')};
  // position: absolute;
  // right: 62px;
  font-size: 20px;
  cursor: pointer;
  // top: 2px;
  // visibility: hidden;
`;
