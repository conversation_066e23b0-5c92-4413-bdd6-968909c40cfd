/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import styled from 'styled-components';
import { CircularProgress } from '@material-ui/core';

import { useCapture } from '../../../../hooks';
import { JOURNEY_TEMPLATE_ID } from '../../constant';

export const StyledButton = styled.button`
  border: none;
  background-color: transparent;
  padding: 0;
  cursor: pointer;
  color: #007bff;
  text-decoration: underline;

  > span {
    margin-right: ${props => (props.disabled ? '5px' : 0)};
  }
`;

export const ContactButton = props => {
  const [isLoading, setIsLoading] = useState(false);
  const { isLowerCase = false } = props;

  const { capture } = useCapture({
    selector: `#${JOURNEY_TEMPLATE_ID}`,
  });

  const onClickContact = async () => {
    setIsLoading(true);
    const imageUrl = await capture();

    const messageContent = {
      type: 'open_antsomi_help',
      helpType: 'ISSUE',
      helpInfo: {
        feature: 'customer_journey',
        title: 'Journey Tactic',
        message:
          'I need assistance from the customer support team to resolve this situation.',
        files: [],
        referenceUrl: '',
      },
      dataImage: {
        imageName: '',
        dataUrl: imageUrl,
      },
    };

    window.postMessage(messageContent);
    setIsLoading(false);
  };

  return (
    <StyledButton type="button" onClick={onClickContact} disabled={isLoading}>
      <span> {isLowerCase ? 'contact' : 'Contact'}</span>
      {isLoading && <CircularProgress size={12} />}
    </StyledButton>
  );
};
