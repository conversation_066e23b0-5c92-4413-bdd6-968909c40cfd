// Libraries
import React, { useRef, useMemo, useState, memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { isArray, has, get, pick } from 'lodash';
import PropTypes from 'prop-types';

// Locales
import { translations, translate } from '@antscorp/antsomi-locales';

// Hooks
import { useDebounce, useDeepCompareEffect } from '../../../hooks';

// Actions
import { fetchAllocatedEvents } from '../../common/UIEditorPersonalization/WrapperPersonalization/libs/action';

// Selectors
import { makeSelectAllocatedEventsInfo } from '../../../modules/Dashboard/selector';

// Components
import {
  Col,
  Button,
  SearchPopover,
  Select,
  Typography,
  Flex,
  Scrollbars,
  Tooltip,
  Spin,
} from '@antscorp/antsomi-ui';
import SelectTree from 'components/form/UISelectCondition';
import {
  CollapseStyled,
  ConditionItem,
  ContentItem,
  SelectInputStyled,
} from './styled';
import {
  DeleteRemoveTrashIcon,
  ExpandMoreIcon,
  WarningIcon,
} from '@antscorp/antsomi-ui/es/components/icons';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { CONDITION_TYPE, OPERATOR_OPTIONS } from './constants';

// Utils
import { addMessageToQueue } from '../../../utils/web/queue';
import { buildEventTrackingKey } from '../../../modules/Dashboard/MarketingHub/Journey/Create/utils.flow';
import {
  isDisabledOperator,
  isDisabledType,
  mapOptionsWithTooltip,
} from './utils';

const { EVENT, DYNAMIC_CODE } = CONDITION_TYPE;

const PATH = 'app/components/Molecules/ConditionAllocatedCode/Condition.jsx';

const Condition = props => {
  // Props
  const {
    index,
    condition = {},
    allocatedPersTagAndEvents,
    onChangeAllocatedCode,
  } = props;

  const { tags, events } = allocatedPersTagAndEvents;
  const { type, operator, value = {} } = condition;

  // Selectors
  const {
    sourceEventAttributes,
    isLoading: isLoadingSourceEventAttribute,
  } = useSelector(makeSelectAllocatedEventsInfo());

  // State
  const [isOpenPopover, setIsOpenPopover] = useState(false);
  const [searchTxtContentItem, setSearchTxtContentItem] = useState('');

  // Hooks
  const dispatch = useDispatch();
  const searchTxtDebounced = useDebounce(searchTxtContentItem, 350);

  // Refs
  const wrapperInputRef = useRef(null);

  // Memoized
  const isDynamicCode = type === DYNAMIC_CODE;
  const isEvent = type === EVENT;

  const eventValue = useMemo(() => {
    if (!isEvent) return null;

    return {
      eventActionId: value?.eventActionId,
      eventCategoryId: value?.eventCategoryId,
    };
  }, [isEvent, value?.eventActionId, value?.eventCategoryId]);

  const sourceEventAttributeOpts = useMemo(() => {
    if (!eventValue) return [];

    const eventSavedKey = buildEventTrackingKey(eventValue);
    const eventAttributes = sourceEventAttributes[eventSavedKey]?.list;

    if (!isArray(eventAttributes)) return [];
    if (!isDisabledOperator(operator)) return eventAttributes;

    return eventAttributes.map(attr => {
      const { dataType, options = [] } = attr;

      const mappedOptions = mapOptionsWithTooltip(options);

      if (isDisabledType(dataType)) {
        return {
          ...attr,
          disabled: true,
          tooltip: translate(
            translations._PERSONALIZE_PROCESS_CODE_TOOLTIP,
            'Operator did not support Array data type',
          ),
          options: mappedOptions,
        };
      }

      return { ...attr, options: mappedOptions };
    });
  }, [eventValue, operator, sourceEventAttributes]);

  const { tagList, eventList } = useMemo(() => {
    if (!searchTxtDebounced) {
      return {
        tagList: tags?.list || [],
        eventList: events?.list || [],
      };
    }

    const predicateSearch = item =>
      item?.label?.toLowerCase().includes(searchTxtDebounced.toLowerCase());

    return {
      tagList: tags?.list?.filter(predicateSearch),
      eventList: events?.list?.filter(predicateSearch),
    };
  }, [searchTxtDebounced, tags?.list, events?.list]);

  const conditionDisplay = useMemo(() => {
    if (!value?.label) {
      return translate(translations._USER_GUIDE_SELECT_ITEM, 'Select an item');
    }

    if (isDynamicCode) {
      const { tagId, label } = value || {};

      return get(tags, ['map', tagId, 'label'], label);
    }

    return value?.label;
  }, [value, isDynamicCode, tags?.map]);

  const contentWidth = useMemo(() => {
    if (wrapperInputRef.current) {
      const offset = 15; // Padding
      return wrapperInputRef.current.offsetWidth - offset;
    }
    return undefined;
  }, [isOpenPopover]);

  const { warningMessage, isShowWarning } = useMemo(() => {
    if (isDynamicCode) {
      return {
        isShowWarning: !has(tags?.map, value?.tagId),
        warningMessage: translate(
          translations._PERSONALIZE_TAG_REMOVED,
          'This personalized content is removed',
        ),
      };
    }

    if (isEvent) {
      return {
        isShowWarning: value?.value && !has(events?.map, value?.value),
        warningMessage: translate(
          translations._PERSONALIZE_PERFORM_EVENT_REMOVE,
          'This event is removed',
        ),
      };
    }

    return { isShowWarning: false };
  }, [isDynamicCode, isEvent, tags, value, events]);

  const checkIsActiveContentItem = (conditionType, item) => {
    if (conditionType === DYNAMIC_CODE) {
      return item?.value === value?.tagId;
    }
    if (conditionType === EVENT) {
      return item?.value === value?.value;
    }
    return false;
  };

  const handleChangeOperator = newOperator => {
    if (isEvent) {
      const disabledOperator = isDisabledOperator(newOperator);
      const disabledType = isDisabledType(value?.attribute?.dataType);
      const isResetAttr = disabledOperator && disabledType;

      onChangeAllocatedCode('CHANGE_CONDITION', {
        index,
        condition: {
          ...condition,
          value: {
            ...value,
            attribute: isResetAttr ? {} : value?.attribute,
          },
          operator: newOperator,
        },
      });
    } else {
      onChangeAllocatedCode('CHANGE_CONDITION', {
        index,
        condition: { ...condition, operator: newOperator },
      });
    }
  };

  const handleSelectContentItem = (conditionType, selectedCondition) => {
    try {
      if (!conditionType) {
        throw new Error('Condition type not found');
      }

      if (conditionType === DYNAMIC_CODE) {
        const { label, value: tagId, nodeId } = pick(selectedCondition, [
          'value',
          'label',
          'nodeId',
        ]);

        onChangeAllocatedCode('CHANGE_CONDITION', {
          index,
          condition: {
            ...condition,
            type: conditionType,
            value: {
              label,
              tagId,
              nodeId,
              value: tagId,
            },
          },
        });
      } else if (conditionType === EVENT) {
        onChangeAllocatedCode('CHANGE_CONDITION', {
          index,
          condition: {
            ...condition,
            type: conditionType,
            value: {
              ...selectedCondition,
              attribute: {},
            },
          },
        });
      }

      setIsOpenPopover(false);
      setSearchTxtContentItem('');
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleSelectContentItem',
        data: {
          error: error.stack,
          args: { conditionType, conditionItem: selectedCondition },
        },
      });
    }
  };

  const handleChangeSourceAttribute = newSourceAttribute => {
    try {
      const newValue = {
        ...(condition.value || {}),
        attribute: {
          label: newSourceAttribute.label,
          value: newSourceAttribute.value,
          column: newSourceAttribute?.name,
          dataType: newSourceAttribute.dataType,
          itemTypeName: newSourceAttribute.itemTypeName,
          itemTypeId: newSourceAttribute.itemTypeId,
          itemPropertyName: newSourceAttribute.propertyName,
          eventPropertySyntax: newSourceAttribute.propertySyntax,
        },
      };

      onChangeAllocatedCode('CHANGE_CONDITION', {
        index,
        condition: { ...condition, value: newValue },
      });
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleChangeSourceAttribute',
        data: { error: error.stack, args: { newSourceAttribute } },
      });
    }
  };

  useDeepCompareEffect(() => {
    if (eventValue) {
      dispatch(fetchAllocatedEvents(eventValue));
    }
  }, [eventValue]);

  const renderContentItemList = (list, conditionType) => {
    if (!isArray(list) || list.length === 0)
      return (
        <Typography.Text
          style={{ margin: '0px 10px', textAlign: 'center', fontSize: '11px' }}
        >
          {translate(translations._, 'No data found')}
        </Typography.Text>
      );

    return list.map(item => (
      <ContentItem
        key={item.value}
        ellipsis={{ tooltip: item.label }}
        $isActive={checkIsActiveContentItem(conditionType, item)}
        onClick={() => {
          handleSelectContentItem(conditionType, item);
        }}
      >
        {item.label}
      </ContentItem>
    ));
  };

  const renderContentPopover = () => {
    return (
      <CollapseStyled
        ghost
        expandIconPosition="end"
        expandIcon={() => (
          <ExpandMoreIcon size={18} style={{ color: globalToken.bw8 }} />
        )}
        defaultActiveKey={['allocated_codes', 'performed_events']}
        style={{ width: contentWidth, padding: 0 }}
        items={[
          {
            key: 'allocated_codes',
            label: (
              <Typography.Text
                style={{ fontSize: '11px', color: globalToken.bw8 }}
              >
                {translate(
                  translations._PERSONALIZE_ALLOCATED_CODE,
                  'Allocated Codes',
                )}
              </Typography.Text>
            ),
            children: (
              <Scrollbars autoHeight autoHeightMax={72}>
                <Flex vertical style={{ width: contentWidth, minHeight: 24 }}>
                  {renderContentItemList(tagList, DYNAMIC_CODE)}
                </Flex>
              </Scrollbars>
            ),
          },
          {
            key: 'performed_events',
            label: (
              <Typography.Text
                style={{ fontSize: '11px', color: globalToken.bw8 }}
              >
                {translate(
                  translations._PERSONALIZE_PERFORM_EVENT,
                  'Performed Events',
                )}
              </Typography.Text>
            ),
            children: (
              <Scrollbars autoHeight autoHeightMax={72}>
                <Flex vertical style={{ width: contentWidth, minHeight: 24 }}>
                  {renderContentItemList(eventList, EVENT)}
                </Flex>
              </Scrollbars>
            ),
          },
        ]}
      />
    );
  };

  const renderWarning = () => {
    if (!warningMessage || !isShowWarning) return null;

    return (
      <Tooltip
        destroyTooltipOnHide
        mouseEnterDelay={0.5}
        title={warningMessage}
        placement="top"
      >
        <WarningIcon size={18} style={{ marginRight: 10, marginLeft: 4 }} />
      </Tooltip>
    );
  };

  return (
    <ConditionItem gutter={15}>
      {/* Operator */}
      <Col span={6}>
        <Select
          style={{ width: 120 }}
          value={operator}
          suffixIcon={<ExpandMoreIcon size={20} color={globalToken.bw8} />}
          options={OPERATOR_OPTIONS}
          onChange={handleChangeOperator}
        />
      </Col>

      <Col span={9} ref={wrapperInputRef}>
        <SearchPopover
          open={isOpenPopover}
          content={renderContentPopover()}
          placement="bottom"
          inputSearchProps={{
            onAfterChange: newSearchTxt => {
              setSearchTxtContentItem(newSearchTxt);
            },
          }}
          onOpenChange={() => {
            setIsOpenPopover(!isOpenPopover);
          }}
          rootClassName="antsomi-wrapper-personalization-allocated"
        >
          <SelectInputStyled>
            <Typography.Text
              ellipsis={{ tooltip: conditionDisplay }}
              style={{ flex: 1, lineHeight: '30px' }}
            >
              {conditionDisplay}
            </Typography.Text>

            {renderWarning()}

            <ExpandMoreIcon size={20} color={globalToken.bw8} />
          </SelectInputStyled>
        </SearchPopover>
      </Col>

      {isEvent && (
        <Col span={9}>
          <Spin
            spinning={isLoadingSourceEventAttribute}
            indicatorSize={18}
            className="custom-spin"
          >
            <SelectTree
              use="tree"
              isSearchable
              options={sourceEventAttributeOpts}
              value={value?.attribute}
              onChange={handleChangeSourceAttribute}
              placeholder={translate(
                translations._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
            />
          </Spin>
        </Col>
      )}

      <Button
        type="text"
        style={{
          position: 'absolute',
          top: '50%',
          right: '0',
          transform: 'translate(-10px,-50%)',
        }}
        icon={
          <DeleteRemoveTrashIcon color={globalToken.colorPrimary} size={16} />
        }
        onClick={() => {
          onChangeAllocatedCode('REMOVE_CONDITION', { index, condition });
        }}
      />
    </ConditionItem>
  );
};

Condition.propTypes = {
  index: PropTypes.number,
  condition: PropTypes.object,
  allocatedPersTagAndEvents: PropTypes.shape({
    events: PropTypes.shape({ list: PropTypes.array, map: PropTypes.object }),
    tags: PropTypes.shape({ list: PropTypes.array, map: PropTypes.object }),
  }),
  onChangeAllocatedCode: PropTypes.func,
};
Condition.defaultProps = {
  index: null,
  condition: {},
  allocatedPersTagAndEvents: {
    events: { list: [], map: {} },
    tags: { list: [], map: {} },
  },
  onChangeAllocatedCode: () => {},
};

export default memo(Condition);
