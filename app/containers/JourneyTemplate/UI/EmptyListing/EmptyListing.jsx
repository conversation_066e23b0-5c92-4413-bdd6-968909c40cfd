/* eslint-disable react/prop-types */
import React from 'react';
import { EditJourneyButton } from '../EditJourneyButton/EditJourneyButton';
import { EmptyListingRoot } from './styled';

export const EmptyListing = props => (
  <EmptyListingRoot>
    <div className="icon-wrapper">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="28"
        height="30"
        viewBox="0 0 28 30"
        fill="none"
      >
        <path
          d="M6.47656 21.007V24.0281H17.0234V21.007H6.47656ZM6.47656 15.0351V17.9859H21.5234V15.0351H6.47656ZM6.47656 8.99297V12.0141H21.5234V8.99297H6.47656ZM24.4766 3.02108C26.1641 3.02108 27.5 4.35597 27.5 6.04215V26.9789C27.5 28.6651 26.1641 30 24.4766 30H3.52344C3.3125 30 3.10156 30 2.89063 29.9297C2.32813 29.8595 1.76563 29.5082 1.41406 29.1569C1.13281 28.8759 0.921875 28.5246 0.710938 28.1733C0.570313 27.822 0.5 27.4005 0.5 26.9789V6.04215C0.5 5.62061 0.570313 5.19906 0.710938 4.84777C0.921875 4.49649 1.13281 4.1452 1.41406 3.93443C1.76563 3.51288 2.32813 3.23185 2.89063 3.09133C3.10156 3.02108 3.3125 3.02108 3.52344 3.02108H9.78125C10.4141 1.26464 12.0313 0 14 0C15.9688 0 17.5859 1.26464 18.2188 3.02108H24.4766ZM14 2.66979C13.3672 2.66979 12.875 3.16159 12.875 3.79391C12.875 4.35597 13.3672 4.91803 14 4.91803C14.6328 4.91803 15.125 4.35597 15.125 3.79391C15.125 3.16159 14.6328 2.66979 14 2.66979ZM24.4766 26.9789V6.04215H3.52344V26.9789H24.4766Z"
          fill="#BEBEBE"
        />
      </svg>
    </div>

    <div className="empty-title">
      <span>You don&apos;t have any item for verification</span>

      <div className="empty-sub-title">Click button to edit journey</div>
    </div>

    <EditJourneyButton
      onCancel={props.onCancel}
      variant="contained"
      theme="primary"
    />
  </EmptyListingRoot>
);
