export const convertConditionRefineToArrayString = (
  refine,
  sourceId,
  arrayDataSource = [],
  array = [],
) => {
  const arrayRefine = [];
  if (refine.size === 0) {
    return arrayRefine;
  }
  let arrayTMP = [];
  // console.log(array, refine)
  arrayTMP = array.filter(each => each !== sourceId);
  refine.forEach(item => {
    const property = item.get('property');
    const operator = item.get('operator');
    const value = item.get('value');

    if (property) {
      const { sources } = property;
      const isCheck = checkIncludesArray(sources, arrayTMP);
      if (arrayDataSource.length > 0) {
        if (sources.length === 1 && sources.indexOf(parseInt(sourceId)) > -1) {
          const stringConditionRefine = `"<strong>${property.label}</strong>" ${
            operator.label
          } "<strong>${value}</strong>"`;

          arrayRefine.push(stringConditionRefine);
        }
      } else if (arrayDataSource.length === 0) {
        const stringConditionRefine = `"<strong>${property.label}</strong>" ${
          operator.label
        } "<strong>${value}</strong>"`;

        arrayRefine.push(stringConditionRefine);
      } else if (!isCheck && sources.indexOf(parseInt(sourceId)) > -1) {
        const stringConditionRefine = `"<strong>${property.label}</strong>" ${
          operator.label
        } "<strong>${value}</strong>"`;

        arrayRefine.push(stringConditionRefine);
      }
    }
  });

  return arrayRefine;
};
export const checkIncludesArray = (arrayAttrSources, arraySource) => {
  let isCheck = false;
  if (arraySource.length > 0) {
    arraySource.forEach(each => {
      if (arrayAttrSources.indexOf(parseInt(each)) > -1) {
        isCheck = true;
      }
    });
  }

  return isCheck;
};
