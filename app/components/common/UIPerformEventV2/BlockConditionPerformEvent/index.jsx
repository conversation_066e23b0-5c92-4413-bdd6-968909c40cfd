/* eslint-disable no-empty */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { memo, useCallback, useEffect, useState } from 'react';
import isEqual from 'react-fast-compare';

import Grid from '@material-ui/core/Grid';
import styled from 'styled-components';
import {
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
  UICheckbox,
  UIIconButton as IconButton,
} from '@xlab-team/ui-components';

import HeaderCondition from '../HeaderCondition';
import ContainerCondition from '../GeneralContainerCondition';
import DataSources from './DataSources';
import Refine from './Refine/index';
import SelectEvent from './SelectEvent';
import { safeParse } from '../../../../utils/web/utils';
import { STATUS_ITEM_CODE } from '../../../../utils/constants';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import {
  TitleConversion,
  WrapperConversion,
} from '../../UIPerformEvent/HeaderCondition/styled';
import { UINumberStyled } from '../../UIFrequencyCapping/styled';
import UISelectCondition from '../../../form/UISelectCondition';
import {
  LIST_CS_WINDOW,
  MAP_CS_WINDOW,
  PERIOD_LIST_REPEAT,
  PERIOD_MAP_REPEAT,
} from '../../UIPerformEvent/BlockConditionPerformEvent/Refine/utils';
import { MAX_CONVERISON } from './Refine/utils';
import { keyBy } from 'lodash';

export const Title = styled.div`
  color: #7f7f7f;
`;

const BlockConditionPerformEvent = props => {
  const {
    item,
    options,
    groupIndex,
    itemIndex,
    version,
    showHeader = true,
    isConversion = false,
    rules,
    hiddeOption,
    defaultOption,
    trackingHasFilter = false,
    eventPropertyService,
    isUsingJourneyTemplate,
    labelPerfEvent = getTranslateMessage(
      TRANSLATE_KEY._TITL_PERFORM_EVENT,
      'Perform event',
    ),
  } = props;
  const [listDataSouces, setListDatasouces] = useState([]);
  const [loadingDataSources, setLoadingDataSources] = useState(true);
  const [loadingRefine, setLoadingRefine] = useState(true);

  useEffect(() => {
    if (!loadingDataSources && !loadingRefine) {
      const sourcesSelected = item.get('dataSources');
      const refineWithProperties = item.get('refineWithProperties');

      if (trackingHasFilter && sourcesSelected && refineWithProperties) {
        const hasSourceFilter = sourcesSelected.length < listDataSouces.length;
        const hasRefine = refineWithProperties.size > 0;

        callback('PERF_EVENT_CHANGE_HAS_FILTER', {
          groupIndex,
          itemIndex,
          hasFilter: hasSourceFilter || hasRefine,
        });
      }
    }
  }, [
    loadingDataSources,
    loadingRefine,
    listDataSouces,
    trackingHasFilter,
    item.get('dataSources'),
    item.get('refineWithProperties'),
  ]);

  const callback = (type, data) => {
    if (props.callback) {
      if (type === 'PERF_EVENT_CHANGE_REFINE_PROPERTIES') {
        setLoadingRefine(false);
      }
      props.callback(type, data);
    }
  };

  const changeSource = (value, isCallback = true) => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };

    props.callback('PERF_EVENT_CONDITION_CHANGE_SOURCE', { version, data });

    if (isCallback) {
      props.callback('UPDATE_CONDITIONS', data);
    }
  };

  const handleChangeHavingAttribute = value => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };

    props.callback('PERF_EVENT_CONDITION_CHANGE_HAVING', { version, data });
  };

  const deleteItem = useCallback(() => {
    props.callback('DELETE_ITEM', { groupIndex, itemIndex });
  }, []);

  const resetEvent = useCallback(() => {
    props.callback('COMP_PROP_CONDITION_RESET_PROPERTY', {
      data: {
        groupIndex,
        itemIndex,
        value: null,
      },
    });
  }, []);

  const onChangeConversionWindow = (name, value) => {
    const dataTmp = { ...item.get('conversionWindow') };
    const data = {
      ...dataTmp,
      [name]: value,
    };
    if (name === 'type') {
      data.value = 1;
    }

    props.callback('PERF_EVENT_CONDITION_CHANGE_CONVERSION_WINDOW', {
      version,
      data: {
        ...data,
      },
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
    });
  };

  const property = safeParse(props.item.get('property'), null);

  const mapOptionByValue = keyBy(options, 'value');

  const isExistProperty = mapOptionByValue[(property?.value)];

  useEffect(() => {
    if (!isExistProperty && property) {
      resetEvent();
    }
  }, [property]);

  const havingAttributes = safeParse(props.item.get('havingAttributes'), {});

  // const error = item.get('error');

  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  const onChangeListDatasources = value => {
    setListDatasouces(value);
    setLoadingDataSources(false);
  };

  const divDisabled = statusItemCode !== STATUS_ITEM_CODE.ACTIVE;
  return (
    <ContainerCondition>
      <Loading isLoading={props.isLoading} isWhite />
      {showHeader && (
        <HeaderCondition
          label={labelPerfEvent}
          onClick={deleteItem}
          showButtonClose={props.showButtonClose && !props.isViewMode}
        />
      )}

      <WrapperDisable disabled={divDisabled && !props.isViewMode}>
        <Grid container item xs={12}>
          <SelectEvent
            callback={callback}
            options={options}
            item={item}
            property={property}
            moduleConfig={props.moduleConfig}
            groupIndex={groupIndex}
            itemIndex={itemIndex}
            disabledEvent={props.disabledEvent}
            isViewMode={props.isViewMode}
            hidden={props.hiddenSelectEvent}
            rules={rules}
            hiddeOption={hiddeOption}
            defaultOption={defaultOption}
            // mapEventsTracking={dataEventsTracking.map}
          />

          <DataSources
            isViewMode={props.isViewMode}
            paramsFetchEvent={props.paramsFetchEvent}
            eventValue={property}
            sourcesSelected={item.get('dataSources')}
            error={item.get('errorDataSources')}
            isInitDataSources={
              item.get('isInitDataSources') ||
              (item.get('dataSources') && item.get('dataSources').length > 0)
            }
            onChange={changeSource}
            onChangeListDatasources={onChangeListDatasources}
            disabledEventConditions={props.disabledEventConditions}
            refineWithProperties={item.get('refineWithProperties')}
            hidden={props.hiddenDatasources}
            showAllSource={props.showAllSource}
          />

          <Refine
            // title="Where"
            // callback={callback}
            isViewMode={props.isViewMode}
            refineWithProperties={item.get('refineWithProperties')}
            moduleConfig={props.moduleConfig}
            triggerInfo={props.triggerInfo}
            groupIndex={groupIndex}
            itemIndex={itemIndex}
            eventValue={property}
            callback={callback}
            useHavingCondition={props.useHavingCondition}
            havingAttributes={havingAttributes}
            mapEventSchema={props.mapEventSchema}
            isInitHavingCondition={props.isInitHavingCondition}
            onInitHavingCondition={props.onInitHavingCondition}
            onChangeHavingAttribute={handleChangeHavingAttribute}
            sourcesSelected={item.get('dataSources')}
            disabledEventConditions={
              props.disabledEventConditions ||
              (item.get('dataSources') &&
                (item.get('dataSources').length === 0 && !props.isHideSource))
            }
            hidden={props.hiddenRefine}
            eventPropertyService={eventPropertyService}
            isUsingJourneyTemplate={isUsingJourneyTemplate}
            isUsingCustomerJourney={props.isUsingCustomerJourney}
            sourceTags={props.sourceTags}
            triggerTargetAudienceId={props.triggerTargetAudienceId}
            channelActive={props.channelActive}
          />

          {isConversion && (
            <WrapperConversion>
              <TitleConversion>Conversion window</TitleConversion>
              {item.get('conversionWindow').options &&
              item.get('conversionWindow').options ===
                MAP_CS_WINDOW.after.value ? (
                <>
                  <UISelectCondition
                    use="tree"
                    isSearchable={false}
                    options={LIST_CS_WINDOW}
                    value={MAP_CS_WINDOW[item.get('conversionWindow').options]}
                    onChange={value =>
                      onChangeConversionWindow('options', value.value)
                    }
                    fullWidthPopover={false}
                    labelWidth="103px"
                    isViewMode={props.isViewMode}
                    // width={100}
                    // placeholderTranslateCode={props.placeholderTranslateCode}
                  />
                  {props.isViewMode ? (
                    <span>{item.get('conversionWindow').value}</span>
                  ) : (
                    <UINumberStyled
                      name="times"
                      onChange={e => onChangeConversionWindow('value', e)}
                      value={item.get('conversionWindow').value}
                      min={1}
                      max={
                        ['view_pageview', 'view_product'].includes(
                          property && property.eventTrackingCode,
                        )
                          ? 180
                          : MAX_CONVERISON[item.get('conversionWindow').type]
                      }
                      defaultValue={1}
                      width={70}
                    />
                  )}

                  <UISelectCondition
                    use="tree"
                    isSearchable={false}
                    options={PERIOD_LIST_REPEAT}
                    value={PERIOD_MAP_REPEAT[item.get('conversionWindow').type]}
                    onChange={value =>
                      onChangeConversionWindow('type', value.value)
                    }
                    fullWidthPopover={false}
                    labelWidth="auto"
                    isViewMode={props.isViewMode}
                    // width={100}
                    // placeholderTranslateCode={props.placeholderTranslateCode}
                  />

                  {item.get('conversionWindow').type === 'minutes' && (
                    <WrapperDisable>
                      <UICheckbox
                        disabled={props.isViewMode}
                        checked={item.get('conversionWindow').absolute}
                        onChange={value =>
                          onChangeConversionWindow('absolute', value)
                        }
                      >
                        <span
                          style={{
                            fontSize: '12px',
                            fontWeight: '400',
                            color: '#595959',
                          }}
                        >
                          Absolute
                        </span>
                      </UICheckbox>
                    </WrapperDisable>
                  )}
                </>
              ) : (
                <UISelectCondition
                  use="tree"
                  isSearchable={false}
                  options={LIST_CS_WINDOW}
                  value={MAP_CS_WINDOW[item.get('conversionWindow').options]}
                  onChange={value =>
                    onChangeConversionWindow('options', value.value)
                  }
                  fullWidthPopover={false}
                  labelWidth="103px"
                  isViewMode={props.isViewMode}
                  // width={100}
                  // placeholderTranslateCode={props.placeholderTranslateCode}
                />
              )}
            </WrapperConversion>
          )}

          {isConversion && (
            <div
              style={{
                position: 'absolute',
                right: '10px',
                top: '10px',
              }}
            >
              {props.showButtonClose && !props.isViewMode && (
                <IconButton onClick={deleteItem} iconName="close" size="20px" />
              )}
            </div>
          )}
        </Grid>
      </WrapperDisable>
    </ContainerCondition>
  );
};

export default memo(BlockConditionPerformEvent, (prevProps, props) => {
  const { item: prevItem, ...otherPrevProps } = prevProps;
  const { item, ...otherProps } = props;

  return isEqual(otherPrevProps, otherProps) && prevItem.equals(item);
});
