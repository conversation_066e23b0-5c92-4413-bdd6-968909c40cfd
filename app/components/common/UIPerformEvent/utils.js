/* eslint-disable dot-notation */
/* eslint-disable no-else-return */
/* eslint-disable no-undef */
/* eslint-disable camelcase */
import { OrderedMap, Map } from 'immutable';
import { validateItemCondition } from 'containers/Segment/Content/Condition/utils';
import { generateKey, safeParse } from '../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../utils/constants';
import {
  safeParseArrayNumber,
  safeParseArrayString,
} from '../../../utils/web/attribute';
import { MAP_SEGMENT_VALUES, MAP_SEGMENT_TYPES } from './constants';
import { toRefinePropertiesAPI } from './BlockConditionPerformEvent/Refine/utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { toArrayApiWithDataType } from '../../../services/map';
import { checkIncludesArray } from './BlockConditionPerformEvent/DataSources/utils';
import { getStatusItemCode } from '../../../utils/web/properties';
import { randomV2 } from '../../../utils/web/utils';
import { omit } from 'lodash';

export const toAPIPerformEvent = conditions => {
  if (Object.keys(safeParse(conditions, {})).length === 0) {
    return {};
  }

  let rules = {};
  // if (conditions.has(version)) {
  conditions.forEach(condition => {
    // const rule = { AND: [] };

    // console.log('conditions===>', condition.toJS());
    const isInit = condition.get('isInit');
    if (isInit === true) {
      rules = condition.get('backup');
    } else {
      condition.forEach(item => {
        const conditionType = safeParse(item.get('conditionType'), {});
        if (conditionType.value === MAP_SEGMENT_VALUES.perf_event) {
          const tempt = toConditionPerfEventAPI(item);
          if (tempt !== null) {
            rules = tempt;
          }
        }
      });
    }

    // if (rule.AND.length > 0) {
    //   rules['event'] = tempt;
    // }
  });
  // }

  return rules;
};

const toConditionPerfEventAPI = item => {
  // console.log('validateItemCondition(item) ===>', validateItemCondition(item));
  // console.log('validateItemCondition(item) ===>', item.toJS());
  const isInit = item.get('isInit');
  if (isInit === true) {
    return item.get('backup');
  }
  if (validateItemCondition(item)) {
    // const value = item.get('value');

    const property = item.get('property');
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    let tempt = {};
    // const objValue = buildValueConditionFromUI(item);

    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      // let insight_property_ids = [-1];
      let insight_property_ids = [];
      const dataSources = safeParse(item.get('dataSources'), []);
      if (Array.isArray(dataSources) && dataSources.length > 0) {
        insight_property_ids = safeParseArrayNumber(dataSources);
      }
      // console.log('property', property);
      tempt = {
        condition_type: item.get('conditionType').value,
        conditionType: 'event_attribute',
        eventCategoryId: property.eventCategoryId,
        eventActionId: property.eventActionId,
        eventTrackingName: property.eventTrackingName,
        eventKey: item.get('eventKey'),
        // data_type: 'number',
        insightPropertyIds: insight_property_ids,
        // operator: item.get('operator').value,
        // ...objValue,
        filters: toRefinePropertiesAPI(item.get('refineWithProperties')),
        // time_window: toTimeWindowAPI(item.get('timeWindow')),
      };
      // console.log('tempt ===>', tempt);
    } else {
      tempt = item.get('backup');

      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt };
        return tempt;
      }
    }

    // const tempt = {
    //   condition_type: item.get('conditionType').value,
    //   event_category_id: property.eventCategoryId,
    //   event_action_id: property.eventActionId,
    //   data_type: 'number',
    //   operator: item.get('operator').value,
    //   ...objValue,
    //   refine_with_properties: toRefinePropertiesAPI(
    //     item.get('refineWithProperties'),
    //   ),
    //   time_window: toTimeWindowAPI(item.get('timeWindow')),
    // };

    return tempt;
  }
  return null;
};

export const getInputLookupFromRule = rules => {
  const output = {
    perf_event: [],
    comp_attr: [],
    data_sources: [],
    map_refine_with_properties: {},
  };
  const arrDataSource = [];

  //   const rulesAND = safeParse(ruleOR.AND, []);
  if (rules.condition_type === 'perf_event') {
    output.perf_event.push({
      eventCategoryId: rules.eventCategoryId,
      eventActionId: rules.eventActionId,
    });
    // output.map_refine_with_properties.push(
    //   ...getRefineWithProperties(
    //     safeParse(ruleAND.map_refine_with_properties, {}),
    //   ),
    // );
    output.map_refine_with_properties[
      `${rules.eventCategoryId}-${rules.eventActionId}`
    ] = getRefineWithProperties(safeParse(rules.filters, {}));
    arrDataSource.push(
      ...getDataSource(safeParse(rules.insightPropertyIds, [])),
    );
  } else if (rules.condition_type === MAP_SEGMENT_VALUES.comp_attr) {
    output.comp_attr.push({
      itemTypeId: rules.item_type_id,
      itemPropertyName: rules.property_name,
    });
  }
  output.data_sources = [...new Set(arrDataSource)];
  return output;
};

const getRefineWithProperties = refine => {
  const res = [];
  const ruleOR = safeParse(refine.OR, []);

  if (ruleOR.length === 0) {
    return res;
  }

  const ruleAND = safeParse(ruleOR[0], {});
  if (Object.keys(ruleAND).length === 0) {
    return res;
  }

  if (safeParse(ruleAND.AND, []).length > 0) {
    ruleAND.AND.forEach(item => {
      res.push({
        eventPropertyName: item.column,
        itemTypeId: item.item_type_id,
      });
    });
  }
  return res;
};

function getDataSource(inputs = []) {
  if (inputs.length === 0) {
    return [];
  }
  if (inputs.length === 1 && inputs[0] == '-1') {
    return [];
  }
  return inputs;
}

export function toUIPerformEvent(objRules) {
  // console.log('toConditionUI data', data);
  // let conditions = OrderedMap({});
  // console.log('objRules', objRules);

  if (Object.keys(omit(objRules, 'eventKey')).length === 0) {
    return OrderedMap({});
  }

  const condition = OrderedMap({
    'data-init': OrderedMap({
      backup: safeParse(objRules, {}),
      isInit: true,
    }),
  });

  return condition;
}

export function toComponentPerformEvent(objRules, data) {
  // console.log('toConditionUI data', data);
  let conditions = OrderedMap({});

  if (Object.keys(objRules).length === 0) {
    return conditions;
  }

  let condition = OrderedMap({});
  if (objRules.condition_type === MAP_SEGMENT_VALUES.perf_event) {
    const tempt = toConditionPerfEventUI(
      objRules,
      MAP_SEGMENT_TYPES.perf_event,
      data.map.eventSchema,
      data.info.eventSchema,
    );

    if (tempt !== null) {
      condition = condition.set(generateKey(), tempt);
    }
  }
  if (condition.size > 0) {
    conditions = conditions.setIn([generateKey()], condition);
  }
  return conditions;
}

const initData = {
  event: {
    conditionType: 'event_attribute',
    condition_type: 'perf_event',
    eventActionId: -102,
    eventCategoryId: -11,
    eventTrackingName: 'view product',
    filters: {
      OR: [
        {
          AND: [
            {
              column: 'name',
              dataType: 'string',
              item_type_id: 1,
              operator: 'contains',
              type: 'item',
              value: 'Ổ CỨNG DI ĐỘNG SEAGATE 1TB',
            },
            {
              column: 'name',
              dataType: 'string',
              item_type_id: 1,
              operator: 'contains',
              type: 'item',
              value: 'ASUS ZENFONE 2 LASER ZE500KG 16GB',
            },
          ],
        },
      ],
    },
    insightPropertyIds: [554926188],
    value: '1',
  },
};

const toConditionPerfEventUI = (
  item,
  conditionType,
  mapEventSchema,
  // eventProperty,
  mapInfoEventSchema = {},
  // mapInfoEventProperty = {},
  // mapInfoDataSource = {},
) => {
  const keySchema = `${item.eventCategoryId}-${item.eventActionId}`;
  let property = mapEventSchema[keySchema];
  // console.log('property', property);
  // const mapEventProperty = safeParse(eventProperty[keySchema], {
  //   map: {},
  //   list: [],
  // });
  if (property === undefined) {
    property = mapInfoEventSchema[keySchema];

    // case event deleted
    if (!property) {
      const tempt = Map({
        conditionType,
        property: {
          isInit: true,
        },
        eventKey: item.eventKey,
        statusItemCode: '',
        // operator: MAP_PERF_EVENT_OPERATORS.more_than, // hard operator for by pass validate
        dataSources: [],
        isInitDataSources: false,
        dataType: '',
        refineWithProperties: OrderedMap({
          'data-init': OrderedMap({
            backup: { OR: [{ AND: [] }] },
            isInit: true,
          }),
        }),
        backup: {},
      });

      return tempt;
    }
  }
  if (property !== undefined) {
    // const objValue = buildValueConditionFromAPI(item, property);

    const tempt = Map({
      conditionType,
      property,
      statusItemCode: property.statusItemCode,
      // operator: MAP_PERF_EVENT_OPERATORS.more_than, // hard operator for by pass validate
      dataSources: toRuleDataSourceUI(item.insightPropertyIds),
      isInitDataSources: true,
      eventKey: item.eventKey || randomV2(10),
      // keyEvent: `${property.eventCategoryId}-${property.eventActionId}`,
      dataType: 'number',
      // ...objValue,
      refineWithProperties: OrderedMap({
        'data-init': OrderedMap({
          backup: safeParse(item.filters, { OR: [{ AND: [] }] }),
          isInit: true,
        }),
      }),
      // toRefinePropertiesUI(
      // item.refine_with_properties,
      // mapEventProperty.map,
      // safeParse(mapInfoEventProperty[keySchema], {}),
      // ),
      // refineWithProperties: OrderedMap({}),
      // timeWindow: toTimeWindowUI(item.time_window),
      backup: item,
    });
    return tempt;
  }
  return null;
};

function toRuleDataSourceUI(dataSources = []) {
  if (Array.isArray(dataSources) === false) {
    return [];
  }
  if (dataSources.length === 1 && dataSources[0] == -1) {
    return [];
  }
  return safeParseArrayString(dataSources);
}

export const toEventTrackingFE = (events, use = 'info') => {
  const validatedData = safeParse(events, []);
  const data = {
    list: [],
    map: {},
  };
  if (Array.isArray(validatedData) && validatedData.length === 0) {
    return data;
  }
  validatedData.forEach(event => {
    const dataType = 'number';
    const temp = {
      // label: event.translateLabel,
      label: event.eventTrackingName,
      // value: {
      //   eventCategoryId: event.eventCategoryId,
      //   eventActionId: event.eventActionId,
      // },
      statusItemCode: getStatusItemCode(use, parseInt(event.status)),
      value: `${event.eventCategoryId}-${event.eventActionId}`,
      eventCategoryId: event.eventCategoryId,
      eventActionId: event.eventActionId,
      eventKey: event?.eventKey || randomV2(10),
      // value: `${event.eventCategoryId}-${event.eventActionId}`,
      // displayFormat: safeParseDisplayFormat(
      //   safeParse(event.displayFormat, null),
      //   { dataType },
      // ),
      displayFormat: initNumberWithoutDecimal(),
      dataType,
    };
    data.list.push(temp);
    data.map[temp.value] = temp;
  });
  return data;
};

export const deleteRefinesBySourceId = (dataRefine, sourceId, array = []) => {
  const dataKeyRefine = [];
  let ruleRefine = OrderedMap({});
  let arrayTMP = [];
  arrayTMP = array.filter(each => each !== sourceId);

  if (Object.keys(safeParse(dataRefine, {})).length === 0) {
    return dataRefine;
  }

  dataRefine.forEach((item, key) => {
    const property = item.get('property');

    if (property) {
      const { sources } = property;
      const isCheck = checkIncludesArray(sources, arrayTMP);

      if (sources.length === 1 && sources.indexOf(parseInt(sourceId)) > -1) {
      } else if (!isCheck && sources.indexOf(parseInt(sourceId)) > -1) {
      } else {
        ruleRefine = ruleRefine.setIn([generateKey()], item);
      }
    }
  });
  // console.log('dataKeyRefine ===>', dataKeyRefine);
  // dataKeyRefine.forEach(key => {
  //   dataRefine.delete(key);
  // });

  return ruleRefine;
};

// căng quá, chưa get dc. can doc vao data de get data
export function safeParsePeformEventData(data = OrderedMap({})) {
  try {
    let isInit = false;
    let backup = {};
    if (data.size > 0) {
      isInit = data.first().get('isInit');
      backup = data.first().get('backup');
    }
    if (isInit === true) {
      return {
        eventActionId: backup.eventActionId,
        eventCategoryId: backup.eventCategoryId,
        insightPropertyIds: backup.insightPropertyIds,
      };
    }

    const parent = data.first();
    const child = parent.first();
    const property = child.get('property');
    return {
      eventActionId: property.eventActionId,
      eventCategoryId: property.eventCategoryId,
      insightPropertyIds: toArrayApiWithDataType(
        child.get('dataSources'),
        'number',
      ),
    };
    // return data.get()toJS();
  } catch (err) {
    addMessageToQueue({
      path: 'app/components/common/UIPerformEvent/utils.js',
      func: 'safeParsePeformEventData',
      data: err.stack,
    });
    console.error(err);
  }
  return {};
}
export const getDataDefautEvenActionBased = data => {
  let dataOut = {};
  if (data && data.length > 0) {
    data.forEach(item => {
      if (item.label.toLocaleLowerCase() === 'view page') {
        dataOut = { ...item };
      }
    });
  }
  return dataOut;
};
