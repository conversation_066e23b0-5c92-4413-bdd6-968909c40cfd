// Libraries
import styled, { css } from 'styled-components';

// Components
import { Flex } from '@antscorp/antsomi-ui';

export const FlexContainer = styled(Flex)`
  width: 100%;
  padding: 10px 20px;

  button[type='button'].style-btn-dropdown {
    border: none !important;
    height: 29px;
    cursor: pointer;
  }

  .antsomi-spin .antsomi-spin-dot {
    margin: -10px !important;
  }

  ${({ $isViewMode }) =>
    $isViewMode
      ? css`
          pointer-events: none;

          .antsomi-divider-between-select {
            margin-right: 6px !important;
          }
        `
      : css``};
`;
