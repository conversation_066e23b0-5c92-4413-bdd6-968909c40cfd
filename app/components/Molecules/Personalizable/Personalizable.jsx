/* eslint-disable import/no-cycle */
/* eslint-disable no-param-reassign */
// Libraries
import React, {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { original } from 'immer';
import _, { isEmpty, isFunction, isObject, isNumber } from 'lodash';
import { useImmer } from 'use-immer';
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import ModalPersonalization from './ModalPersonalization';
import { ModalShortlink } from '../ModalShortlink';
import {
  Button,
  Dropdown,
  SHORT_LINK_V2,
  SHORT_LINK_TYPE,
  TAG_TYPE,
  useDeepCompareMemo,
  useDeepCompareEffect,
} from '@antscorp/antsomi-ui';
import { PersonUserAccountIcon } from '@antscorp/antsomi-ui/es/components/icons';

// Constants
import {
  EXTRA_ATTR_TYPE,
  INCLUDE_TYPE_MAP,
  initCustomFunction,
} from './constants';

// Utils
import {
  ADD_DYNAMIC_OPTION,
  genShortLinkValue,
  getAddDynamicContentOptions,
  mapDataCustomFucntion,
  mapDataFormatAttribute,
} from '../../common/UIEditorPersonalization/Input/utils';
import {
  DATA_ATTR_WITH_TYPE,
  getInitialSelected,
} from '../../common/UIEditorPersonalization/components/PopupPersonalization/utils';
import {
  STATUS_CREATE,
  STATUS_UPDATE,
} from 'components/common/UIEditorPersonalization/utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { safeParse } from '../../../utils/common';
import { detectContentEdit } from '../../common/UIEditorPersonalization/utils.extends';
import {
  buildFormatAttributeKey,
  buildPersonalizeTag,
  buildPersonalizeTagOldVersion,
  editTagState,
  generateTagId,
  getRemoveFormatKey,
} from './utils';
import { updateValue } from '../../../redux/actions';
import { DATA_GROUPS } from '../../common/UIEditorPersonalization/WrapperPersonalization/constants';

const PATH = 'app/components/Molecules/Personalizable/Personalizable.jsx';

const { INDIVIDUAL } = SHORT_LINK_TYPE;

const Personalizable = forwardRef(function Personalization(props, ref) {
  // Props
  const {
    disabled,
    dataState,
    moduleConfig,
    enableShortLink,
    hiddenDynamicOption,
    showMergeTagDisplay,
    isNewCustomTag,
    useAllocatedCode,
    showShortLinkWithBroadcast,
    onlyShowGeneralShortlink,
    isForceHideBtnPersonalization,
    isViewMode,
    tagProperties,
    mapAttributes,
    listTemplateCustom,
    variantExtraData,
    onAddNewTag,
    onUpdateTag,
    onChangeExtraDataTagProperty,
  } = props;

  // Refs
  const shortlinkRef = useRef(null);
  const timerUpdatePropertyTagRef = useRef(null);

  // Dispatcher
  const dispatch = useDispatch();

  // States
  const [shortlinkState, setShortlinkState] = useImmer({
    isOpen: false,
  });

  const [personalizeState, setPersonalizeState] = useImmer({
    isOpen: false,
    design: STATUS_CREATE,
    selectedProperties: getInitialSelected(dataState),
    formatAttributes: variantExtraData?.formatAttributes || {},
    extraData: {
      customFnAttribute:
        mapDataCustomFucntion(variantExtraData?.customFunction || {}) || {},
    },
    cacheTag: null,
  });

  const [customFunctionState, setCustomFunctionState] = useImmer(
    initCustomFunction,
  );

  // Exposed functions
  useImperativeHandle(
    ref,
    () => ({
      onUpdateShortlink: (
        url = '',
        shortlinkType = SHORT_LINK_TYPE.INDIVIDUAL,
        tagElement,
        shortener,
      ) => {
        if (shortlinkRef.current && tagElement) {
          setShortlinkState(draft => {
            draft.isOpen = true;
          });

          shortlinkRef.current.onUpdateShortlink(
            url,
            shortlinkType,
            tagElement,
            shortener,
          );
        }
      },
      onUpdatePersonalizationTag: (tagElement, tagData) => {
        const {
          value: tagValue,
          type: tagType,
          status = '',
          statusMsg = '',
          label,
          attributeName = '',
        } = tagData;

        try {
          // New version
          if (tagType === TAG_TYPE.CUSTOM_TAG) {
            setPersonalizeState(draft => {
              const pickDataState = _.pick(dataState, [
                'personalizationData',
                'personalizationType',
                'promotionCodeAttr',
                'contentSources',
              ]);

              const newState = editTagState({
                state: original(draft),
                tagData,
                tagElement,
                tagProperties,
                dataState: pickDataState,
                callbackCustomFn: newCustomFn => {
                  if (newCustomFn && isObject(newCustomFn)) {
                    setCustomFunctionState(() => {
                      return newCustomFn;
                    });
                  }
                },
              });

              return newState;
            });

            return;
          }

          // Old version
          if (tagType === TAG_TYPE.CUSTOM_FN) {
            const found = tagValue.replace(/#|{|}|/g, '').split('.');

            setPersonalizeState(draft => {
              const originalPersonalize = original(draft);

              const newCustomFn = _.cloneDeep(
                originalPersonalize.extraData.customFnAttribute[found[1]],
              );

              draft.selectedProperties.personalType =
                DATA_GROUPS[TAG_TYPE.CUSTOM_FN];
              draft.selectedProperties.customFunction = newCustomFn;
              draft.selectedProperties.tagId = generateTagId();
              if (newCustomFn?.personalizationName) {
                draft.selectedProperties.displayName =
                  newCustomFn.personalizationName;
              }

              if (newCustomFn) {
                setCustomFunctionState(draftCustomFn => {
                  Object.keys(newCustomFn).forEach(key => {
                    draftCustomFn[key] = newCustomFn[key];
                  });
                });
              }
            });
          } else {
            // Old version
            setPersonalizeState(draft => {
              const originalPersonalize = original(draft);

              const detectContent = safeParse(
                detectContentEdit(
                  tagValue,
                  dataState.personalizationType,
                  dataState.personalizationData,
                  dataState.promotionCodeAttr,
                  { formatAttributes: originalPersonalize.formatAttributes },
                ),
                {},
              );

              if (!isEmpty(detectContent)) {
                const {
                  personalType = { value: tagType },
                  attributeType = {
                    label,
                    bracketed_code: label,
                    value: attributeName || label,
                  },
                  valueString = '',
                  attributePromotionCode = {},
                  displayFormat = {
                    dataType: '',
                    format: {},
                  },
                  csIndexAttribute = {
                    label: 1,
                    value: 1,
                  },
                } = detectContent;

                draft.selectedProperties = {
                  ...(originalPersonalize.selectedProperties || {}),
                  personalType,
                  attribute: attributeType,
                  value: valueString,
                  promotionCodeAttr: attributePromotionCode,
                  displayFormat,
                  contentSources: dataState.contentSources || [],
                  csIndexAttribute,
                  status,
                  statusMsg,
                  displayName: attributeType?.label || attributeType?.value,
                  tagId: generateTagId(),
                };
              }
            });
          }
        } catch (error) {
          addMessageToQueue({
            path: PATH,
            function: 'onUpdatePersonalizationTag',
            data: { tagElement, tagData },
          });
        }

        setPersonalizeState(draft => {
          draft.isOpen = true;
          draft.design = STATUS_UPDATE;
          draft.cacheTag = tagElement;
        });
      },
    }),
    [
      tagProperties,
      dataState.personalizationType,
      dataState.personalizationData,
      dataState.promotionCodeAttr,
      dataState.contentSources,
    ],
  );

  // Memoizations
  const hideOptionShortlinkType = useMemo(() => {
    // Hide INDIVIDUAL if showShortLinkWithBroadcast or onlyShowGeneralShortlink enabled
    // => not allow dynamic url
    if (
      (showShortLinkWithBroadcast && isForceHideBtnPersonalization) ||
      onlyShowGeneralShortlink
    ) {
      return INDIVIDUAL;
    }

    return null;
  }, [
    showShortLinkWithBroadcast,
    isForceHideBtnPersonalization,
    onlyShowGeneralShortlink,
  ]);

  const addDynamicOptions = useMemo(
    () =>
      getAddDynamicContentOptions({
        enableShortLink,
      }).filter(option => !hiddenDynamicOption.includes(option.value)),
    [enableShortLink, hiddenDynamicOption],
  );

  const initialSelectedProperties = useDeepCompareMemo(
    () => getInitialSelected(dataState),
    [dataState],
  );

  const listTemplateCustomFn = useMemo(() => listTemplateCustom?.list || [], [
    listTemplateCustom,
  ]);

  const personalizationProps = useMemo(
    () => ({
      listTemplateCustom,
      mapAttributes,
      otherData: {
        variantExtraData,
      },
    }),
    [listTemplateCustom, mapAttributes, variantExtraData],
  );

  const toggleModalShortlink = useCallback(() => {
    setShortlinkState(draft => {
      draft.isOpen = !draft.isOpen;
    });
  }, []);

  const onCloseModalShortlink = useCallback(() => {
    setShortlinkState(draft => {
      draft.isOpen = false;
    });
  }, []);

  const toggleModalPersonalize = useCallback(() => {
    setPersonalizeState(draft => {
      draft.isOpen = !draft.isOpen;
    });
  }, []);

  const onCloseModalPersonalize = useCallback(() => {
    setPersonalizeState(draft => {
      draft.isOpen = false;
    });
  }, []);

  const resetPersonalizable = useCallback(() => {
    setCustomFunctionState(() => initCustomFunction);
    setPersonalizeState(draft => {
      draft.selectedProperties = initialSelectedProperties;
      draft.design = STATUS_CREATE;
    });
  }, [initialSelectedProperties]);

  const handleOpenAddDynamicOption = useCallback(
    option => {
      if (option.value === ADD_DYNAMIC_OPTION.addPer.value) {
        resetPersonalizable();
        toggleModalPersonalize();
      }

      if (option.value === ADD_DYNAMIC_OPTION.shortLink.value) {
        toggleModalShortlink();
      }
    },
    [toggleModalShortlink, toggleModalPersonalize, resetPersonalizable],
  );

  const handleOkShortlink = useCallback(
    shortlinkInfo => {
      const {
        url = '',
        shortlinkType = '',
        design = 'create',
        cacheTag = null,
        shortener = '',
        dynamicSettings,
      } = shortlinkInfo;

      const type =
        shortlinkType === SHORT_LINK_TYPE.INDIVIDUAL ? 'individual' : 'general';

      const shortLink = genShortLinkValue(url, type, shortener);

      const newTag = {
        label: shortLink.label,
        value: shortLink.value,
        type: SHORT_LINK_V2,
        shortlinkType,
        shortener,
        url,
      };

      if (design === 'create' && isFunction(onAddNewTag)) {
        onAddNewTag(newTag);
      }

      if (design === 'update' && isFunction(onUpdateTag) && cacheTag) {
        onUpdateTag(cacheTag, newTag);
      }

      if (!isEmpty(dynamicSettings)) {
        const { customFunction, formatAttributes } = dynamicSettings;

        setPersonalizeState(draft => {
          draft.formatAttributes = {
            ...personalizeState.formatAttributes,
            ...formatAttributes,
          };

          draft.extraData.customFnAttribute = customFunction;
        });
      }
    },
    [onAddNewTag, onUpdateTag],
  );

  const handleChangeSelectedProperties = useCallback(
    (newSelectedProperties = {}) => {
      setPersonalizeState(draft => {
        draft.selectedProperties = newSelectedProperties;
      });
    },
    [],
  );

  const handleChangeAllocatedCode = useCallback((type, newValue) => {
    switch (type) {
      case 'CHANGE_INCLUDE_TYPE': {
        setPersonalizeState(draft => {
          draft.selectedProperties.allocatedCode.includeType = newValue;
          draft.selectedProperties.displayName =
            INCLUDE_TYPE_MAP[newValue]?.label;
        });
        break;
      }
      case 'CHANGE_DELIMITER': {
        setPersonalizeState(draft => {
          draft.selectedProperties.allocatedCode.delimiter = newValue;
        });
        break;
      }
      case 'ADD_CONDITION': {
        setPersonalizeState(draft => {
          draft.selectedProperties.allocatedCode.conditions.push(newValue);
        });
        break;
      }
      case 'CHANGE_CONDITION': {
        if (isNumber(newValue?.index)) {
          const { index, condition: newCondition } = newValue;

          setPersonalizeState(draft => {
            draft.selectedProperties.allocatedCode.conditions[
              index
            ] = newCondition;
          });
        }

        break;
      }
      case 'REMOVE_CONDITION': {
        if (isNumber(newValue?.index)) {
          setPersonalizeState(draft => {
            draft.selectedProperties.allocatedCode.conditions.splice(
              newValue?.index,
              1,
            );
          });
        }

        break;
      }
      default: {
        break;
      }
    }
  }, []);

  const handleChangeDisplayName = useCallback(newName => {
    setPersonalizeState(draft => {
      draft.selectedProperties.displayName = newName;
    });
  }, []);

  const handleChangeReplaceSpecChars = useCallback(newStatus => {
    setPersonalizeState(draft => {
      draft.selectedProperties.isReplaceSpecialChars = newStatus;
    });
  }, []);

  const handleAddTagProperties = useCallback(
    ({ nodeId, tagId, tag = {} }) => {
      if (nodeId && tagId && moduleConfig?.key && tag) {
        if (timerUpdatePropertyTagRef.current) {
          clearTimeout(timerUpdatePropertyTagRef.current);
        }

        // Delayed state update to prevent React's "setState in render" error.
        // `setTimeout` ensures the update happens after the current render cycle.
        // described in https://fb.me/setstate-in-render
        timerUpdatePropertyTagRef.current = setTimeout(() => {
          dispatch(
            updateValue(`${moduleConfig.key}@@UPDATE_NODE_TAG_PROPERTIES@@`, {
              nodeId,
              tagId,
              tag,
            }),
          );
        }, 200);
      }
    },
    [moduleConfig?.key, dispatch],
  );

  const handleAddPersonalizeTag = useCallback(
    ({
      nodeId,
      selectedProperties = {},
      customFunction = {},
      design = 'create',
    } = {}) => {
      const isCreate = design === STATUS_CREATE;
      const isUpdate = design === STATUS_UPDATE;

      try {
        if (
          typeof onAddNewTag !== 'function' ||
          typeof onUpdateTag !== 'function'
        )
          return;

        const { tagId, tag, property } = isNewCustomTag
          ? buildPersonalizeTag({ design, selectedProperties })
          : buildPersonalizeTagOldVersion({ design, selectedProperties });

        if (!tagId || !tag || !property?.type) {
          throw new Error('Error building personalize tag');
        }

        const extraAttribute = {};

        setPersonalizeState(draft => {
          switch (property.type) {
            case TAG_TYPE.CUSTOM_FN: {
              draft.extraData.customFnAttribute[
                property.fnCode
              ] = customFunction;
              extraAttribute.type = EXTRA_ATTR_TYPE.CUSTOM_FN;
              extraAttribute.formatKey = property.fnCode;
              extraAttribute.data = customFunction;

              if (isUpdate) {
                extraAttribute.removeFormatKey = getRemoveFormatKey({
                  tagId,
                  tagProperties,
                  newFormatKey: property.fnCode,
                });
              }

              break;
            }
            case TAG_TYPE.VISITOR:
            case TAG_TYPE.CUSTOMER:
            case TAG_TYPE.EVENT:
            case TAG_TYPE.PROMOTION_CODE:
            case TAG_TYPE.CONTENT_SOURCE_GROUP:
            case TAG_TYPE.JOURNEY:
            case TAG_TYPE.CAMPAIGN:
            case TAG_TYPE.VARIANT:
            case TAG_TYPE.OBJECT_WIDGET: {
              const tagDataType = _.get(
                selectedProperties,
                'displayFormat.dataType',
                '',
              );
              const formatKey = buildFormatAttributeKey(property);
              if (formatKey) {
                if (isUpdate) {
                  extraAttribute.type = EXTRA_ATTR_TYPE.FORMAT;
                  extraAttribute.removeFormatKey = getRemoveFormatKey({
                    tagId,
                    tagProperties,
                    newFormatKey: formatKey,
                  });
                }

                if (tagDataType && DATA_ATTR_WITH_TYPE.includes(tagDataType)) {
                  const newFormatAttributeItem = mapDataFormatAttribute(
                    selectedProperties.displayFormat,
                  );

                  extraAttribute.type = EXTRA_ATTR_TYPE.FORMAT;
                  extraAttribute.formatKey = formatKey;
                  extraAttribute.data = newFormatAttributeItem;
                  draft.formatAttributes[formatKey] = newFormatAttributeItem;
                }
              }

              break;
            }
            default: {
              break;
            }
          }

          if (isCreate) {
            onAddNewTag(tag);
          }

          if (isUpdate && draft.cacheTag) {
            onUpdateTag(draft.cacheTag, tag);
          }

          if (isFunction(onChangeExtraDataTagProperty)) {
            onChangeExtraDataTagProperty({ tagId, property, extraAttribute });
          }

          if (nodeId && isNewCustomTag) {
            const tagProperty = {
              nodeId,
              tagId,
              tag: { ...property, displayName: tag.label },
            };

            handleAddTagProperties(tagProperty);
          }
        });
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          function: 'handleAddPersonalizeTag',
          data: {
            error: error.stack,
            args: { selectedProperties, customFunction },
          },
        });
      }

      setPersonalizeState(draft => {
        draft.isOpen = false;
        draft.selectedProperties.tagId = null;

        if (!isUpdate) {
          draft.cacheTag = null;
        }
      });
      resetPersonalizable();
    },
    [
      isNewCustomTag,
      handleAddTagProperties,
      tagProperties,
      onAddNewTag,
      onUpdateTag,
      resetPersonalizable,
      onChangeExtraDataTagProperty,
    ],
  );

  // useDeepCompareEffect(() => {
  //   if (
  //     variantExtraData?.formatAttributes &&
  //     Object.keys(variantExtraData?.formatAttributes || {}).length > 0
  //   ) {
  //     setPersonalizeState(draft => {
  //       draft.formatAttributes = variantExtraData.formatAttributes;
  //     });
  //   }
  // }, [variantExtraData?.formatAttributes]);

  useDeepCompareEffect(() => {
    if (
      variantExtraData?.customFunction &&
      Object.keys(variantExtraData?.customFunction || {}).length > 0
    ) {
      const objTmp = mapDataCustomFucntion(variantExtraData?.customFunction);
      setPersonalizeState(draft => {
        draft.extraData.customFnAttribute = { ...objTmp };
      });
    }
  }, [variantExtraData?.customFunction]);

  const renderPersonalize = useCallback(() => {
    if (isForceHideBtnPersonalization) {
      if (showShortLinkWithBroadcast) {
        return (
          <Button
            type="link"
            icon={<PersonUserAccountIcon />}
            disabled={isViewMode || disabled}
            onClick={() =>
              handleOpenAddDynamicOption(ADD_DYNAMIC_OPTION.shortLink)
            }
          />
        );
      }

      return null;
    }

    // Only one option
    if (addDynamicOptions.length === 1) {
      return (
        <Button
          type="link"
          icon={<PersonUserAccountIcon />}
          disabled={isViewMode || disabled}
          onClick={() => handleOpenAddDynamicOption(addDynamicOptions[0])}
        />
      );
    }

    // Multiple options
    const items = addDynamicOptions.map(option => ({
      key: option.value,
      label:
        translate(translations?.[option.labelTranslateCode], option.label) ||
        '--',
      onClick: () => handleOpenAddDynamicOption(option),
    }));

    return (
      <Dropdown
        trigger={['click']}
        menu={{ items }}
        placement="bottomLeft"
        arrow
        disabled={isViewMode || disabled}
      >
        <Button type="link" icon={<PersonUserAccountIcon />} />
      </Dropdown>
    );
  }, [
    disabled,
    addDynamicOptions,
    isViewMode,
    showShortLinkWithBroadcast,
    isForceHideBtnPersonalization,
    handleOpenAddDynamicOption,
  ]);

  return (
    <ErrorBoundary path={PATH}>
      {renderPersonalize()}

      <ModalPersonalization
        open={personalizeState.isOpen}
        design={personalizeState.design}
        moduleConfig={moduleConfig}
        customFunction={customFunctionState}
        listTemplateCustomFn={listTemplateCustomFn}
        dataState={dataState}
        showMergeTagDisplay={showMergeTagDisplay}
        useAllocatedCode={useAllocatedCode}
        selectedProperties={personalizeState.selectedProperties}
        showReplaceSpecialChars={isNewCustomTag}
        onChangeReplaceSpecialChars={handleChangeReplaceSpecChars}
        onChangeDisplayName={handleChangeDisplayName}
        onChangeAllocatedCode={handleChangeAllocatedCode}
        onChangeSelectedProperties={handleChangeSelectedProperties}
        onChangeCustomFunction={setCustomFunctionState}
        onCancel={onCloseModalPersonalize}
        onAddTag={handleAddPersonalizeTag}
      />

      <ModalShortlink
        ref={shortlinkRef}
        open={shortlinkState.isOpen}
        showMergeTagDisplay={false}
        onClose={onCloseModalShortlink}
        onOk={handleOkShortlink}
        hideOption={hideOptionShortlinkType}
        personalizationProps={personalizationProps}
        dataState={dataState}
      />
    </ErrorBoundary>
  );
});

Personalizable.propTypes = {
  disabled: PropTypes.bool,
  dataState: PropTypes.object,
  enableShortLink: PropTypes.bool,
  isViewMode: PropTypes.bool,
  hiddenDynamicOption: PropTypes.array,
  showShortLinkWithBroadcast: PropTypes.bool,
  tagProperties: PropTypes.object,
  onlyShowGeneralShortlink: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  listTemplateCustom: PropTypes.object,
  moduleConfig: PropTypes.object,
  showMergeTagDisplay: PropTypes.bool,
  isNewCustomTag: PropTypes.bool,
  useAllocatedCode: PropTypes.bool,
  variantExtraData: PropTypes.object,
  onAddNewTag: PropTypes.func,
  onUpdateTag: PropTypes.func,
  onChangeExtraDataTagProperty: PropTypes.func,
  mapAttributes: PropTypes.object,
};

Personalizable.defaultProps = {
  disabled: false,
  showMergeTagDisplay: true,
  useAllocatedCode: false,
  isNewCustomTag: false,
  dataState: {
    personalizationType: { list: [], map: {} },
    promotionCodeAttr: { list: [], map: {} },
    personalizationData: {},
  },
  isViewMode: false,
  tagProperties: {},
  enableShortLink: true,
  hiddenDynamicOption: [],
  showShortLinkWithBroadcast: false,
  onlyShowGeneralShortlink: false,
  moduleConfig: {},
  isForceHideBtnPersonalization: false,
  listTemplateCustom: { list: [], map: {} },
  variantExtraData: {
    formatAttributes: {},
    customFunction: {},
  },
  onAddNewTag: () => {},
  onUpdateTag: () => {},
  mapAttributes: {},
};

export default memo(Personalizable);
