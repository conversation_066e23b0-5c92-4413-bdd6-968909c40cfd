/* eslint-disable indent */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, {
  memo,
  useState,
  useEffect,
  useMemo,
  useRef,
  useCallback,
} from 'react';
import { isEmpty, replace, isArray } from 'lodash';
import cls from 'classnames';
import PropTypes from 'prop-types';
import { Grid, Paper } from '@material-ui/core';
import Icon from '@material-ui/core/Icon';
import { makeStyles } from '@material-ui/core/styles';
import styled from 'styled-components';
import TinymceEditor from '../UIEditorPersonalization/WrapperPersonalization';
import './style.scss';
import InputPreview from '../../Atoms/InputPreview';
import { Flex } from '@antscorp/antsomi-ui';
import BasicInput from '../../Atoms/BasicInput';

// Hooks
import useDebounce from '../../../hooks/useDebounce';

// Utils
import { REQUIRED_KEYS_TEXT, getSuggestionsByLang } from './utils';
import { getObjectPropSafely } from '../UIEditorPersonalization/utils.3rd';

// Components
import UIIconXlab from 'components/common/UIIconXlab';
import { SuggestionItem, WrapperSuggestion } from './styled';

// Constants
import { TYPE_RENDER_PAIR } from './index';
import { CATALOG_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';

const inputStyle = { height: 35 };
const limitWidthStyle = {
  position: 'relative',
  marginRight: 20,
  maxWidth: 200,
  width: 200,
};
const maxW = { flex: 1, maxWidth: '100%' };
const widthFull = { boxShadow: 'none', width: '100%', position: 'relative' };

const IconWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  height: 35px;

  span.icon-xlab {
    font-size: 20px;
    color: #666;
  }
`;

const WrapperEditor = styled.div`
  &.customize-input-editor {
    position: relative;

    .customize-antsomi-profile-outter {
      top: 1rem !important;
      right: 0rem !important;
    }
  }
`;

const IconWrapperCancel = styled.div`
  position: relative;
  display: inline-block;
  margin-left: 13px;
`;
const useStyles = makeStyles(() => ({
  paper: {
    borderLeft: '4px solid rgb(0, 94, 184)',
    padding: '10px',
    margin: '10px 0px',
    width: '100%',
  },
  paperNoBox: {
    borderLeft: 'none',
    boxShadow: 'none',
    padding: '0px',
  },
  icon: {
    color: 'rgb(112, 112, 112)',
    position: 'absolute',
    top: '-10px',
  },
  keyName: {
    padding: '0.5rem',
    // backgroundColor: '#f5f5f5',
  },
  button: {
    // marginTop: '20px',
    // margin: 'auto',
  },
  inputKey: {
    marginLeft: '5px',
    // maxWidth: '25%',
  },
  inputFull: {
    width: '100%',
  },
  inputCustom: {
    width: '100%',

    '& .MuiInputBase-root': {
      height: '35px !important',
    },
  },
  inputHeight: {
    '& > .MuiInputBase-root': {
      height: '35px !important',
    },
  },
  inputValue: { maxWidth: '90%' },
  iconClose: {
    color: '#005eb8',
    position: 'absolute',
    top: '-0.45rem',
    left: '-0.57rem',
    cursor: 'pointer',
    fontSize: '20px',
  },
}));

const simplifyStyle = {
  position: 'absolute',
  right: '0px',
  top: '50%',
  transform: 'translate(100%, -50%)',
  display: 'flex',
  alignItems: 'center',
};

function ItemKeyValue(props) {
  const {
    item,
    callback,
    itemId,
    errors,
    global,
    canAddPersonalization,
    isViewMode,
    isUsePairKeyValue,
    hasSuggestions = false,
    data = {},
    index = '',
    dataLength = 1,
    showPersonalizationInKey = false,
    messageType = {},
    isTransactionTemplate = false,
    typeRender = TYPE_RENDER_PAIR.KEY_VALUE,
    onChangeOthers,
    isSampleFormMode = false,
    keyAsText = false,
    isSimplifyUI = false,
  } = props;
  const renderItemKeyAsText = isSampleFormMode || keyAsText;
  const classes = useStyles();

  const [isShowSuggestion, setIsShowSuggestion] = useState(false);

  const inputLabelRef = useRef(null);

  const isTableMode = useMemo(() => typeRender === TYPE_RENDER_PAIR.TABLE, [
    typeRender,
  ]);

  const closeGridStyle = useMemo(() => {
    if (isSimplifyUI) return simplifyStyle;
    if (isTableMode) return { width: 30 };

    return {};
  }, [isTableMode, isSimplifyUI]);

  const isZaloOA = getObjectPropSafely(
    () => props.catalogCode === CATALOG_CODE.ZALO_OA,
    false,
  );
  const flagLang = getObjectPropSafely(
    () => messageType && messageType.flag,
    '',
  );
  const debounceKey = useDebounce(item.key, 800);

  const checkDisabledKey = (itemData = {}) => {
    const { key = '' } = itemData;
    if (!isZaloOA || typeof key !== 'string' || !REQUIRED_KEYS_TEXT[flagLang])
      return false;

    return key.trim() === REQUIRED_KEYS_TEXT[flagLang].NAME;
  };

  const handleDetectToggleSuggestion = useCallback(
    event => {
      if (
        !isShowSuggestion &&
        inputLabelRef &&
        inputLabelRef.current &&
        inputLabelRef.current.contains(event.target)
      ) {
        setIsShowSuggestion(true);
      } else if (isShowSuggestion) {
        setIsShowSuggestion(false);
      }
    },
    [isShowSuggestion, hasSuggestions, isTableMode],
  );

  const checkHideRemoveBtnZaloOA = () => {
    if (!isZaloOA || !REQUIRED_KEYS_TEXT[flagLang] || !isTransactionTemplate)
      return true;

    const listKeys = Object.keys(data);
    const firstKey = listKeys[0];
    const secondKey = listKeys[1];
    const { key: dataKeyFirst = '' } = getObjectPropSafely(
      () => data[firstKey] || {},
    );
    const { key: dataKeySecond = '' } = getObjectPropSafely(
      () => data[secondKey] || {},
    );
    const isValidName = dataKeyFirst === REQUIRED_KEYS_TEXT[flagLang].NAME;
    const isValidCode = dataKeyFirst.includes(
      REQUIRED_KEYS_TEXT[flagLang].RAW_CODE,
    );
    const hasCodeSecond = dataKeySecond.includes(
      REQUIRED_KEYS_TEXT[flagLang].RAW_CODE,
    );

    if (
      index === 0 &&
      ((!isValidName && isValidCode) || (isValidName && !hasCodeSecond))
    )
      return false;

    return true;
  };

  // Detect to hide popup suggestion
  useEffect(() => {
    if (isTableMode && hasSuggestions) {
      document.addEventListener('click', handleDetectToggleSuggestion);
    }

    return () => {
      document.removeEventListener('click', handleDetectToggleSuggestion);
    };
  }, [isTableMode, isShowSuggestion, hasSuggestions]);

  useEffect(() => {
    if (isTableMode && hasSuggestions && isZaloOA && isTransactionTemplate) {
      callback('UPDATE_ITEM_DEBOUNCE_KEY', {
        id: itemId,
        value: debounceKey,
        name: 'key',
      });
    }
  }, [debounceKey]);

  const renderListSuggestions = ({
    lang = '',
    updateId = '',
    dataKey = '',
  }) => {
    if (!isShowSuggestion || !hasSuggestions || isEmpty(data)) return null;

    const list = getSuggestionsByLang(lang);

    if (!Array.isArray(list) || !updateId) return null;

    const dataList = Object.values(data);

    const excludeExistKeys = list.filter(eachItem => {
      const { key: keyParent = '' } = eachItem;

      const indexItem = dataList.findIndex(itemInner => {
        const { key = '' } = itemInner;

        return (
          key === keyParent ||
          (key.includes(REQUIRED_KEYS_TEXT[lang].RAW_CODE) &&
            key.includes(replace(keyParent, '...', '')))
        );
      });

      return indexItem === -1;
    });

    const dataFiltered = excludeExistKeys.filter(each => {
      const { key = '' } = each;
      return key.toLowerCase().indexOf(dataKey.toLowerCase().trim()) !== -1;
    });

    if (dataFiltered.length === 0) return null;

    const content = dataFiltered.map(each => (
      <SuggestionItem
        key={each.key}
        onClick={() =>
          callback('UPDATE_ITEM', {
            id: updateId,
            value: each.key,
            name: 'key',
          })
        }
      >
        {each.key}
      </SuggestionItem>
    ));

    return <WrapperSuggestion>{content}</WrapperSuggestion>;
  };

  return (
    <div style={{ position: 'relative' }}>
      {renderItemKeyAsText ? (
        <Grid
          container
          style={{ marginTop: index > 0 && isSimplifyUI ? 15 : 0 }}
        >
          <Paper
            className={cls({
              [classes.paper]: !isSimplifyUI,
              [classes.paperNoBox]: isTableMode,
            })}
            elevation={2}
            style={isSimplifyUI ? widthFull : {}}
          >
            <Grid container spacing={1}>
              <Grid
                item
                xs={isTableMode ? 5 : isSimplifyUI ? 6 : 4}
                style={isTableMode ? limitWidthStyle : {}}
              >
                <Flex
                  align="center"
                  id={`${itemId}-key`}
                  style={{ fontSize: '12px', height: '100%' }}
                >
                  {item.key}

                  {props.required && (
                    <span style={{ color: 'red', marginLeft: 4 }}> *</span>
                  )}
                </Flex>
              </Grid>
              <Grid
                item
                xs={isViewMode ? 6 : 5}
                style={isTableMode ? maxW : {}}
              >
                {canAddPersonalization ? (
                  <div className="sample-form-mode personalization-area-input">
                    <TinymceEditor
                      {...props}
                      {...global}
                      componentKey={`${props.componentKey}-${props.itemId}`}
                      typeComponent="input"
                      isForceHideBtnPersonalization={
                        props.isForceHideBtnPersonalization
                      }
                      onChange={value =>
                        callback('UPDATE_ITEM', {
                          id: itemId,
                          value,
                          name: 'value',
                        })
                      }
                      errors={
                        isArray(props.errors)
                          ? props.errors.filter(Boolean)
                          : props.errors
                      }
                      onChangeOthers={onChangeOthers}
                      initData={item.initValue}
                      initValue={item.initValue}
                      isViewMode={isViewMode}
                      isUsePairKeyValue={isUsePairKeyValue}
                      placeholder={props.placeHolder || ''}
                      isSampleFormMode
                    />
                    {props.errors && props.errors.length ? (
                      <span style={{ color: 'red', fontSize: 11 }}>
                        {props.errors}
                      </span>
                    ) : null}
                  </div>
                ) : (
                  <BasicInput
                    id={`${itemId}-value`}
                    value={item.value}
                    onChange={value =>
                      callback('UPDATE_ITEM', {
                        id: itemId,
                        value,
                        name: 'value',
                      })
                    }
                    errors={item.errors}
                    style={inputStyle}
                  />
                )}
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      ) : (
        <Grid
          container
          style={{ marginTop: index > 0 && isSimplifyUI ? 15 : 0 }}
        >
          <Paper
            className={cls({
              [classes.paper]: !isSimplifyUI,
              [classes.paperNoBox]: isTableMode,
            })}
            elevation={2}
            style={isSimplifyUI ? widthFull : {}}
          >
            <Grid container spacing={1}>
              <>
                {!isTableMode && !isSimplifyUI && (
                  <Grid
                    item
                    xs={1}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: 'fit-content',
                    }}
                  >
                    <IconWrapper>
                      {/* <Icon className={classes.icon}>vpn_key</Icon> */}
                      <UIIconXlab name="key" />
                    </IconWrapper>
                  </Grid>
                )}
                <Grid
                  item
                  // eslint-disable-next-line no-nested-ternary
                  xs={isTableMode ? 5 : isSimplifyUI ? 6 : 4}
                  style={isTableMode ? limitWidthStyle : {}}
                >
                  <InputPreview
                    value={item.key}
                    isViewMode={isViewMode}
                    type="input"
                    className={classes.keyName}
                  >
                    {canAddPersonalization && showPersonalizationInKey ? (
                      <WrapperEditor className="personalization-area-input customize-input-editor">
                        <TinymceEditor
                          {...global}
                          typeComponent="input"
                          isForceHideBtnPersonalization={
                            props.isForceHideBtnPersonalization
                          }
                          onChange={value =>
                            callback('UPDATE_ITEM', {
                              id: itemId,
                              value,
                              name: 'key',
                            })
                          }
                          showPersonalization
                          initData={item.key}
                          isViewMode={isViewMode}
                          isUsePairKeyValue={isUsePairKeyValue}
                        />
                      </WrapperEditor>
                    ) : (
                      <BasicInput
                        id={`${itemId}-key`}
                        value={item.key}
                        ref={inputLabelRef}
                        placeholder={isSimplifyUI ? 'key' : ''}
                        onChange={value =>
                          callback('UPDATE_ITEM', {
                            id: itemId,
                            value,
                            name: 'key',
                          })
                        }
                        disabled={
                          isZaloOA && hasSuggestions && isTransactionTemplate
                            ? checkDisabledKey(item)
                            : false
                        }
                        errors={item.errors}
                        style={inputStyle}
                      />
                    )}
                  </InputPreview>
                  {hasSuggestions &&
                    renderListSuggestions({
                      lang: flagLang,
                      dataKey: item.key,
                      updateId: itemId,
                    })}
                </Grid>
                {!isTableMode && (
                  <Grid
                    item
                    xs={1}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: 'fit-content',
                    }}
                  >
                    <IconWrapper>
                      <UIIconXlab name="outline-right-arrow" />
                    </IconWrapper>
                  </Grid>
                )}
                <Grid
                  item
                  xs={isViewMode ? 6 : 5}
                  style={isTableMode ? maxW : {}}
                >
                  {canAddPersonalization ? (
                    <div
                      className={cls('personalization-area-input', {
                        'custom-width-input no-padding-top': isTableMode,
                      })}
                      style={
                        isSimplifyUI
                          ? { position: 'relative', height: '100%' }
                          : {}
                      }
                    >
                      <TinymceEditor
                        {...props}
                        {...global}
                        isSimplifyUI={isSimplifyUI}
                        componentKey={`${props.componentKey}-${props.itemId}`}
                        typeComponent="input"
                        isForceHideBtnPersonalization={
                          props.isForceHideBtnPersonalization
                        }
                        placeHolder={isSimplifyUI ? 'value' : ''}
                        onChange={value =>
                          callback('UPDATE_ITEM', {
                            id: itemId,
                            value,
                            name: 'value',
                          })
                        }
                        errors={
                          isArray(props.errors)
                            ? props.errors.filter(Boolean)
                            : props.errors
                        }
                        onChangeOthers={onChangeOthers}
                        initData={item.initValue}
                        initValue={item.initValue}
                        isViewMode={isViewMode}
                        isUsePairKeyValue={isUsePairKeyValue}
                      />
                    </div>
                  ) : (
                    <BasicInput
                      id={`${itemId}-value`}
                      value={item.value}
                      onChange={value =>
                        callback('UPDATE_ITEM', {
                          id: itemId,
                          value,
                          name: 'value',
                        })
                      }
                      placeholder={isSimplifyUI ? 'value' : ''}
                      errors={item.errors}
                      style={inputStyle}
                    />
                  )}
                </Grid>
                {/* {isTableMode && dataLength === 1 && !isViewMode && (
                  <Grid item style={{ width: 30 }} />
                )} */}
                {dataLength > 1 && !isViewMode ? (
                  checkHideRemoveBtnZaloOA() ? (
                    <Grid
                      item
                      xs={isTableMode ? undefined : 1}
                      style={closeGridStyle}
                    >
                      <IconWrapperCancel>
                        <Icon
                          className={classes.iconClose}
                          onClick={() =>
                            callback('DELETE_ITEM', { id: itemId })
                          }
                        >
                          close
                        </Icon>
                      </IconWrapperCancel>
                    </Grid>
                  ) : (
                    <Grid
                      item
                      xs={isTableMode ? undefined : 1}
                      style={closeGridStyle}
                    />
                  )
                ) : null}
              </>
            </Grid>
          </Paper>
        </Grid>
      )}
    </div>
  );
}
ItemKeyValue.prototype = {
  data: PropTypes.Object,
  callBack: PropTypes.func,
};

export default memo(ItemKeyValue);
