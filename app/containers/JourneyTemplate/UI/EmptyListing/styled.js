import styled from 'styled-components';

export const EmptyListingRoot = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  .icon-wrapper {
    background-color: #f0f0f0;
    aspect-ratio: 1 / 1;
    height: 60px;
    clip-path: circle();
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .empty-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;

    .empty-sub-title {
      margin-top: 4px;
      font-weight: bold;
      font-size: 12px;
    }
  }
`;
