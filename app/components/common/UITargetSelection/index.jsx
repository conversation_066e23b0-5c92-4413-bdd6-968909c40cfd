/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable indent */
import React, { useState, useEffect, useRef, memo } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { useImmer } from 'use-immer';
import SelectTree from 'components/form/UISelectCondition';
import {
  UITextSearch as TextSearch,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  UITippy,
  UIButton,
} from '@xlab-team/ui-components';
import {
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Icon,
  TableCell,
  makeStyles,
  Popover,
} from '@material-ui/core';
import _isEmpty from 'lodash/isEmpty';
// import { StyleWrapper } from 'modules/Dashboard/Profile/Segment/Create/styles';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import {
  ContainerTargetAudience,
  HeaderTargetAudience,
  ContainerSelectAudience,
  WrapperSelectItem,
  WrapperItemsSelect,
  WrapperHeaderSelect,
  DivError,
  WrapperStyleLoadingBottom,
  RemoveAllButton,
  StyledTable,
  TextTableLeft,
  TextTableRight,
  TextTableVisible,
  WrapperButtonFooter,
  WrapperLabelFooter,
  TextLabel,
  TextFooter,
  TextTableLeftLink,
  TextLink,
} from './styled';
import {
  MAP_TRANSLATE,
  OPTIONS_SELECT_SEGMENTS,
  serializeData,
  validate,
} from './utils';
import { SEGMENT_STATUS } from '../constant';
import LabeledSelection from './_UI/LabelSelection';
import ColumnPreview from './_UI/ColumnPreview';
import { Nodata } from '../../form/UISelect/DropdownAction/Tree/styled';
import { useFetchSelection, useFetchSelected } from './useFetchData';
import { TableCellStyle } from '../../../containers/UIDev/UIDocument/styled';
import { TextSearchWrapper } from '../../form/AutoComplete/Dropdown/styled';
import { UISelectConditionWrapper } from '../../form/UISelectCondition/styled';
import {
  WrapperIcon,
  WrapperTextLeftTable,
} from '../UITargetUpdateSegment/styled';
import UIIconXlabColor from '../UIIconXlabColor';
import { makeSelectConfigureMainCreateWorkflow } from '../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';
import { updateValue, addNotification } from '../../../redux/actions';
import { AUDIENCE_TYPE, SEGMENT } from '../contants';
// import Design from '../../../modules/Dashboard/Profile/Segment/Create/Desgin';
// import Content from '../../../modules/Dashboard/Settings/Uploads/BO/Uploads/ContentV0';
// import UILinearProgress from '../UILinearProgress';
import { makeSelectMainCreateSegment } from '../../../modules/Dashboard/Profile/Segment/Create/selectors';
import IndeterminateCheckBoxIcon from '@material-ui/icons/IndeterminateCheckBox';
import Checkbox from '../../Molecules/CheckBox';
// import styled from 'styled-components';
import APP from '../../../appConfig';
import { getCurrentOwnerIds, getPortalId } from '../../../utils/web/cookie';
import { useCreateSegmentInJourney } from '../../../containers/Drawer/DrawerSegment/hooks';
import DropdownViewAccess from '../DropdownViewAccess';
import SegmentServices from 'services/Segment';

const LIMIT = 10;

const initState = () => ({
  dataOptions: { list: [], map: {}, options: [] },
  dataSelected: [],
  mapDataSelected: {},
  search: '',
  viewId: undefined,
  viewObject: undefined,
  limit: LIMIT,
  page: 1,
  total: 0,
  isLoadMore: false,
  errors: [],
  triggerOut: 1,
  triggerFetch: 0,
  isInitDone: false,
  dataTypeBelongToSegment: OPTIONS_SELECT_SEGMENTS[0],
  hasNextPage: true,
});

const useStyles = makeStyles(() => ({
  styleTextLeft: {
    width: '266px',
  },
  styleTextRight: {
    width: '92px',
  },
}));

const alertMessage = {
  [SEGMENT_STATUS.ARCHIVE]: getTranslateMessage(
    TRANSLATE_KEY._ARCHIVED_SEGMENT,
    `This segment is archived`,
  ),
  [SEGMENT_STATUS.REMOVE]: getTranslateMessage(
    TRANSLATE_KEY._,
    'This segment does not exist',
  ),
};

const SegmentLabel = memo(({ segmentId, use, onClick }) => {
  if (segmentId === `${use}-${AUDIENCE_TYPE.visitorSegmentAudiences}`) {
    return (
      <WrapperLabelFooter>
        <TextLabel onClick={() => onClick(SEGMENT.VISITOR_SEGMENT)}>
          {SEGMENT.VISITOR_SEGMENT}
        </TextLabel>
        <TextLabel onClick={() => onClick(SEGMENT.VISITORS_AND_SEGMENT)}>
          {SEGMENT.VISITORS_AND_SEGMENT}
        </TextLabel>
      </WrapperLabelFooter>
    );
  }
  if (segmentId === `${use}-${AUDIENCE_TYPE.customerSegmentAudiences}`) {
    return (
      <WrapperLabelFooter>
        <TextLabel onClick={() => onClick(SEGMENT.CUSTOMER_SEGMENT)}>
          {SEGMENT.CUSTOMER_SEGMENT}
        </TextLabel>
        <TextLabel onClick={() => onClick(SEGMENT.CUSTOMERS_AND_SEGMENT)}>
          {SEGMENT.CUSTOMERS_AND_SEGMENT}
        </TextLabel>
      </WrapperLabelFooter>
    );
  }
  return null;
});

const UITargetSelection = props => {
  const {
    itemTypeId,
    audienceTypes,
    titleSelect,
    titleHeader,
    id,
    initData,
    disabledRemove,
    isViewMode,
    isHiddentListViewMode,
    isHiddenIconRemove,
    // main,
    disabledSelect,
    use,
  } = props;
  const ownerId = getCurrentOwnerIds();

  const { dispatchOpenWorkspaceSegment } = useCreateSegmentInJourney();

  const [openPopover, setOpenPopover] = useState(false);
  const [state, setState] = useImmer(initState());
  const [checkedAll, setStateCheckedAll] = useState(false);

  const [isLoadedDataAccess, setIsLoadedDataAccess] = useState(false);
  const [dataAccessList, setDataAccessList] = useState();
  const [selectedDataView, setSelectedDataView] = useState(
    () => initData?.viewObject,
  );

  const isEmptyDataAccessList = dataAccessList
    ? _isEmpty(dataAccessList?.businessObject) &&
      _isEmpty(dataAccessList?.owner) &&
      _isEmpty(dataAccessList?.shareWithMe)
    : true;

  const selectedViewId = selectedDataView && selectedDataView?.item?.viewId;

  const viewIdList = dataAccessList
    ? [
        ...dataAccessList?.owner?.map(item => item.viewId),
        ...dataAccessList?.shareWithMe?.map(item => item.viewId),
      ]
    : [];
  // /** The current data view selected is in viewAccessInfo */
  const isSelectedDataViewExist = viewIdList.includes(selectedViewId);
  const isSelectedDataObjectExist =
    selectedDataView?.item?.type === 'BO' &&
    selectedDataView?.item?.itemTypeDisplay ===
      dataAccessList?.businessObject?.itemTypeDisplay;

  const isHasPermissionWithCurrentValue =
    isSelectedDataViewExist || isSelectedDataObjectExist;

  const btnRef = useRef(null);

  const isVisitorCustomer = audienceTypes === 'specificAudiences';
  const isSegment = audienceTypes === 'segmentAudiences';

  const [dataAPI, isLoading, total] = useFetchSelection({
    itemTypeId,
    viewId: selectedViewId || state.viewId,
    audienceTypes,
    search: state.search,
    limit: state.limit,
    page: state.page,
    triggerFetch: state.triggerFetch,
    componentId: props.componentId,
    isFilterStatus: true,
  });
  const [dataLookupAPI, isLoadingLookup] = useFetchSelected(
    {
      itemTypeId,
      audienceTypes,
      search: state.search,
      limit: state.limit,
      page: state.page,
      audienceIds: initData.audienceIds || [],
      dependencies: [props.componentId],
    },
    [],
  );

  const classes = useStyles();

  const handleSegment = title => {
    dispatchOpenWorkspaceSegment({ title });
    setOpenPopover(false);
  };

  useEffect(() => {
    // if (selectedViewId && typeof selectedViewId === 'number')
    setState(draft => {
      draft.viewId = selectedViewId;
      draft.viewObject = selectedDataView;
      draft.triggerFetch += 1;
      draft.isLoadMore = false;
      draft.page = 1;
      draft.hasNextPage = true;
    });
  }, [selectedViewId]);

  useEffect(() => {
    if (state.isInitDone) {
      const { errors } = validate(state);
      setState(draft => {
        draft.errors = errors;
      });
    }
  }, [props.validateKey]);

  useEffect(() => {
    if (isLoading || isLoadingLookup) return;

    setState(draft => {
      const dataOptions = serializeData(dataAPI, itemTypeId, audienceTypes);
      draft.hasNextPage = dataOptions.list.length > 0;

      if (state.page === 1 && total < state.limit) {
        draft.hasNextPage = false;
      }
      if (!state.isInitDone) {
        const dataLookup = serializeData(
          dataLookupAPI,
          itemTypeId,
          audienceTypes,
        );

        draft.dataOptions.map = {
          ...state.dataOptions.map,
          ...dataOptions.map,
          ...dataLookup.map,
        };
        if (Object.keys(initData).length > 0) {
          //
          (initData.audienceIds || []).forEach(each => {
            const dataSelected = dataLookup.map[each];

            const item = { ...dataSelected };

            if (!_isEmpty(item)) {
              draft.dataSelected.push(item);
              draft.mapDataSelected[each] = true;
            }
          });
          draft.dataTypeBelongToSegment = initData.dataTypeBelongToSegment;
        }
        draft.search = '';
        draft.isInitDone = true;
        draft.triggerOut += 1;
      } else {
        draft.dataOptions.map = {
          ...state.dataOptions.map,
          ...dataOptions.map,
        };
      }
      draft.total = total > 0 ? total : draft.dataOptions.list.length; // case fetch data lần cuối khi chạm đáy, không có data nên total = 0
      if (state.page > 1) {
        draft.dataOptions.list = state.dataOptions.list.concat(
          dataOptions.list,
        );
      } else {
        draft.dataOptions.list = dataOptions.list;
      }
    });
  }, [isLoading, isLoadingLookup, selectedViewId]);

  useEffect(() => {
    if (state.isInitDone) {
      onChange();
    }
  }, [state.triggerOut]);
  useEffect(() => {
    setStateCheckedAll(state.dataSelected.length !== 0);
  }, [state.dataSelected]);

  useEffect(() => {
    // if (initData.isInit) return;

    const getListViewAccess = async () => {
      try {
        const data = await SegmentServices.fetch.getListViewAccess({
          body: {
            itemTypeId: +itemTypeId,
            ownerId,
          },
        });

        setDataAccessList(data.data);
        setIsLoadedDataAccess(true);
        const { owner, shareWithMe, businessObject } = data?.data || {};
        const dataList = [].concat(owner, shareWithMe);

        const isHaveBO = businessObject && !_isEmpty(businessObject);

        if (
          (dataList.length > 0 || isHaveBO) &&
          (!selectedDataView || _isEmpty(selectedDataView))
        ) {
          setSelectedDataView(
            isHaveBO
              ? { item: { ...businessObject, type: 'BO' } }
              : { item: dataList[0] },
          );
        }
      } catch (error) {
        // console.log('🚀 ~ useEffect ~ error:', error);
      }
    };

    getListViewAccess();
  }, []);

  const operatorLabel =
    state.dataTypeBelongToSegment.value === OPTIONS_SELECT_SEGMENTS[0].value
      ? MAP_TRANSLATE.or
      : MAP_TRANSLATE.and;

  const onDragEnd = item => {
    const indexOfItem = state.dataSelected.findIndex(
      each => `${each.value}` === `${item.draggableId}`,
    );

    if (!item.destination || indexOfItem < 0) {
      return;
    }
    setState(draft => {
      const splicedItem = state.dataSelected[indexOfItem];
      draft.dataSelected.splice(indexOfItem, 1);
      draft.dataSelected.splice(item.destination.index, 0, splicedItem);
      draft.triggerOut += 1;
    });
  };

  const onRemoveItem = index => {
    setState(draft => {
      const item = state.dataSelected[index];
      draft.dataSelected.splice(index, 1);
      delete draft.mapDataSelected[item.value];
      draft.triggerOut += 1;
    });
  };

  const onChangeCheckbox = item => {
    setState(draft => {
      const indexOfItem = state.dataSelected.findIndex(
        each => `${each.value}` === `${item.value}`,
      );

      if (indexOfItem < 0 && !disabledSelect) {
        draft.dataSelected.push(item);
        draft.mapDataSelected[item.value] = true;
      } else {
        draft.dataSelected.splice(indexOfItem, 1);
        delete draft.mapDataSelected[item.value];
      }
      draft.triggerOut += 1;
      const { errors } = validate(draft);
      draft.errors = errors;
    });
  };
  const onSearch = value => {
    if (value !== state.search) {
      const el = document.getElementById(`target-selection-${audienceTypes}`);
      el.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth', // or can get `auto` variable
      });
    }
    setState(draft => {
      draft.search = value;
      draft.triggerFetch += 1;
      draft.isLoadMore = false;
      draft.page = 1;
      draft.hasNextPage = true;
    });
  };

  const onClearAll = () => {
    setState(draft => {
      draft.dataSelected = [];
      draft.mapDataSelected = {};
      draft.triggerOut += 1;
    });
  };

  const onChangeSelectTree = item => {
    setState(draft => {
      draft.dataTypeBelongToSegment = item;
      draft.triggerOut += 1;
    });
  };

  const onChange = () => {
    const output = {
      id,
      data: {
        viewId: selectedViewId,
        viewObject: selectedDataView,
        audienceIds: Object.keys(state.mapDataSelected),
        listAudience: state.dataSelected,
        dataTypeBelongToSegment: state.dataTypeBelongToSegment,
      },
    };
    props.onChange(output);
  };

  const onLoadMore = () => {
    // if (state.total > state.dataOptions.list.length) {
    if (state.hasNextPage) {
      setState(draft => {
        draft.page += 1;
        draft.triggerFetch += 1;
        draft.isLoadMore = true;
      });
    }
  };
  const handleClickCheckboxAll = () => {
    if (checkedAll) {
      onClearAll();
    } else {
      setState(draft => {
        draft.dataSelected = state.dataOptions.list;
        draft.triggerOut += 1;
      });
      state.dataOptions.list.forEach(each => {
        if (each.value) {
          setState(draft => {
            draft.mapDataSelected[each.value] = true;
          });
        }
      });
    }
    setStateCheckedAll(!checkedAll);
  };

  const onScroll = e => {
    if (isLoading) {
      e.preventDefault();
      return;
    }

    const { scrollHeight, scrollTop, clientHeight } = e.target;

    if (scrollHeight - scrollTop - clientHeight < 20) {
      onLoadMore();
    }
  };
  const onClickRemoveItem = () => {
    props.callback('ON_REMOVE_ITEM', id);
  };
  if (isViewMode) {
    if (isLoading) {
      return <Loading isLoading={isLoading} isWhite />;
    }

    return (
      <>
        {!isHiddentListViewMode && (
          <UISelectConditionWrapper bsNone>
            <SelectTree
              options={OPTIONS_SELECT_SEGMENTS}
              onlyParent={props.onlyParent}
              use="tree"
              label={capitalize(titleHeader)}
              isSearchable={false}
              isParentOpen={props.isParentOpen}
              value={state.dataTypeBelongToSegment}
              onChange={onChangeSelectTree}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              isViewMode={isViewMode}
            />
          </UISelectConditionWrapper>
        )}
        <TableContainer
          className="no-margin-bottom -margin-top-10"
          style={{ marginBottom: '20px', marginTop: '10px', width: '358px' }}
        >
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell align="left" className={classes.styleTextLeft}>
                  {isVisitorCustomer ? (
                    <TextTableVisible>{titleSelect}</TextTableVisible>
                  ) : (
                    <TextTableLeft>{titleSelect}</TextTableLeft>
                  )}
                </TableCell>
                {!isVisitorCustomer && (
                  <TableCell align="right" className={classes.styleTextRight}>
                    <TextTableRight>{MAP_TRANSLATE.members}</TextTableRight>
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {state.dataSelected &&
                state.dataSelected.map(data => (
                  <TableRow key={data.value}>
                    <TableCellStyle
                      align="left"
                      className={classes.styleTextLeft}
                    >
                      {isVisitorCustomer ? (
                        <TextTableVisible>{data.label}</TextTableVisible>
                      ) : [
                          SEGMENT_STATUS.ARCHIVE,
                          SEGMENT_STATUS.REMOVE,
                        ].includes(data.status) ? (
                        <WrapperTextLeftTable>
                          <UITippy
                            content={data.label}
                            placement="top-start"
                            distance={0}
                            arrow={false}
                          >
                            <TextLink
                              onClick={() => {
                                window.open(
                                  `${
                                    APP.PREFIX
                                  }/${getPortalId()}/profile/segments?tab=configure&segment-id=${data.value ||
                                    {}}&use-segment-into=segment-menu&design=update`,
                                  '_blank',
                                );
                              }}
                            >
                              {data.label}
                            </TextLink>
                          </UITippy>
                          <UITippy
                            content={alertMessage[data.status]}
                            placement="top-start"
                            distance={0}
                            arrow={false}
                          >
                            <WrapperIcon>
                              <UIIconXlabColor
                                name="warning"
                                fontSize="14px"
                                style={{ marginRight: '-10px' }}
                              />
                            </WrapperIcon>
                          </UITippy>
                        </WrapperTextLeftTable>
                      ) : (
                        <UITippy
                          content={data.label}
                          placement="top-start"
                          distance={0}
                          arrow={false}
                        >
                          <TextTableLeftLink
                            onClick={() => {
                              window.open(
                                `${
                                  APP.PREFIX
                                }/${getPortalId()}/profile/segments?tab=configure&segment-id=${data.value ||
                                  {}}&use-segment-into=segment-menu&design=update`,
                                '_blank',
                              );
                            }}
                          >
                            {data.label}
                          </TextTableLeftLink>
                        </UITippy>
                      )}
                    </TableCellStyle>
                    {!isVisitorCustomer && (
                      <TableCellStyle
                        align="right"
                        className={classes.styleTextRight}
                      >
                        <TextTableRight>{data.size}</TextTableRight>
                      </TableCellStyle>
                    )}
                  </TableRow>
                ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      </>
    );
  }

  // console.log(state);

  return (
    <>
      <ContainerTargetAudience
        errors={state.errors.length > 0}
        data-test="target-selection"
      >
        <HeaderTargetAudience className="header-target-audience wrapper-center">
          {String(titleHeader).toUpperCase()}
          {!isHiddenIconRemove && (
            <WrapperDisable disabled={disabledRemove}>
              <div className="delete-button wrapper-center">
                <Icon onClick={onClickRemoveItem} fontSize="small">
                  close
                </Icon>
              </div>
            </WrapperDisable>
          )}
        </HeaderTargetAudience>

        <ContainerSelectAudience>
          <WrapperHeaderSelect borderLeft padding="5px 20px">
            {isVisitorCustomer && (
              <DropdownViewAccess
                label="Data access:"
                isCheckPermission={isLoadedDataAccess}
                isRequired={false}
                value={selectedDataView && selectedDataView.item}
                callback={(eventType, item) => {
                  if (eventType === 'DATA_ACCESS_ITEM')
                    setSelectedDataView(item);
                  setState(draft => {
                    draft.triggerOut += 1;
                  });
                }}
                viewAccessInfo={dataAccessList}
                classNameContainer="data-access_selected"
                adjustColumn={{ left: 4, right: 8 }}
                useGridColumn={false}
                isShowLabelLeft
                containerStyles={{
                  marginBottom: '10px',
                }}
              />
            )}

            <TextSearchWrapper
              className="no-icon-padding"
              data-test="target-selection-search"
            >
              <TextSearch
                classDiv="search-filter"
                placeholder={
                  isSegment
                    ? `Search ${props.titleSelect.toLowerCase()}`
                    : getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'Search')
                }
                translateCode={TRANSLATE_KEY._ACT_SEARCH}
                isTimeout
                onChange={onSearch}
                value={state.search}
                type="text"
              />
            </TextSearchWrapper>
          </WrapperHeaderSelect>

          <WrapperHeaderSelect
            borderLeft={false}
            padding="5px 20px"
            style={{ height: '41px' }}
          >
            <div
              style={{ height: !isSegment && '32px' }}
              className="wrapper-center"
            >
              <div className="select-dropdown">
                {isSegment && (
                  <UISelectConditionWrapper
                    bsNone
                    data-test="data-type-belong-to-segment"
                  >
                    <SelectTree
                      options={OPTIONS_SELECT_SEGMENTS}
                      onlyParent={props.onlyParent}
                      use="tree"
                      isSearchable={false}
                      isParentOpen={props.isParentOpen}
                      value={state.dataTypeBelongToSegment}
                      onChange={onChangeSelectTree}
                      placeholder={getTranslateMessage(
                        TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                        'Select an item',
                      )}
                      fullWidthPopover
                    />
                  </UISelectConditionWrapper>
                )}
              </div>

              <div>
                <RemoveAllButton onClick={onClearAll}>
                  {String(
                    getTranslateMessage(
                      TRANSLATE_KEY._ACT_REMOVE_ALL,
                      'CLEAR ALL',
                    ),
                  ).toUpperCase()}
                </RemoveAllButton>
              </div>
            </div>
          </WrapperHeaderSelect>
        </ContainerSelectAudience>

        <ContainerSelectAudience>
          <WrapperSelectItem
            // id="target-selection-select-item"
            id={`target-selection-${audienceTypes}`}
            borderLeft
            padding="14px 16px"
            isLoading={isLoading}
            onScroll={onScroll}
          >
            <div
              className="wrapper-center"
              style={{ fontSize: '12px', fontWeight: 'bold' }}
            >
              <div style={{ display: 'flex' }}>
                {isSegment && (
                  <Checkbox
                    checked={checkedAll}
                    onClick={handleClickCheckboxAll}
                    // name={item.value.toString()}
                    margin="0px 10px 0px 0"
                    // disabled={disabled}
                    checkedIcon={<IndeterminateCheckBoxIcon />}
                  />
                )}
                {titleSelect}&nbsp;
                <span style={{ fontWeight: 'bold' }}>
                  {isSegment && `(${state.total})`}
                </span>
              </div>
              {isSegment && <div>{MAP_TRANSLATE.members}</div>}
            </div>
            <WrapperItemsSelect data-test="target-selection-wrapper-item">
              {/* Not show options archive (status = 4) */}
              {state.dataOptions.list.filter(
                ({ status }) => status !== SEGMENT_STATUS.ARCHIVE,
              ).length === 0 &&
                !state.hasNextPage && (
                  <Nodata>
                    {getTranslateMessage(
                      TRANSLATE_KEY._INFO_NO_DATA,
                      'No data',
                    )}
                  </Nodata>
                )}
              {(!isEmptyDataAccessList &&
                isVisitorCustomer &&
                isHasPermissionWithCurrentValue) ||
              isSegment ? (
                state.dataOptions.list
                  .filter(({ status }) => status !== SEGMENT_STATUS.ARCHIVE)
                  .map(item => {
                    const checked =
                      state.mapDataSelected[item.value] !== undefined;

                    return (
                      <LabeledSelection
                        item={item}
                        name={item.value}
                        checked={checked}
                        onClick={onChangeCheckbox}
                        label={item.label}
                        key={item.value}
                        isSegment={isSegment}
                        audienceTypes={audienceTypes}
                        disabled={disabledSelect && !checked}
                        data-test="target-selection-item"
                      />
                    );
                  })
              ) : (
                <Nodata>
                  {getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
                </Nodata>
              )}

              {(isEmptyDataAccessList && isVisitorCustomer) ||
              !isHasPermissionWithCurrentValue
                ? null
                : state.hasNextPage && (
                    <WrapperStyleLoadingBottom>
                      <Loading isLoading isWhite />
                    </WrapperStyleLoadingBottom>
                  )}
            </WrapperItemsSelect>
          </WrapperSelectItem>
          <WrapperSelectItem
            borderLeft={false}
            padding="14px 16px"
            // style={{ marginTop: '-40px', height: '340px' }}
            style={
              isVisitorCustomer ? { marginTop: '-40px', height: '340px' } : {}
            }
          >
            <div
              className="wrapper-center"
              style={{
                fontSize: '12px',
                fontWeight: 'bold',
                marginBottom: '15px',
              }}
            >
              <div>
                {`Selected ${
                  isSegment
                    ? `${props.titleSelect.toLowerCase()}`
                    : props.titleSelect
                }`}
                <span style={{ fontWeight: 'bold' }}>
                  {isSegment && ` (${state.dataSelected.length})`}
                </span>
              </div>
            </div>
            <div data-test="target-selection-wrapper-selected-item">
              <ColumnPreview
                dataSelecteds={state.dataSelected}
                onRemoveItem={onRemoveItem}
                onDragEnd={onDragEnd}
                prefixLabel={isSegment ? operatorLabel : ''}
                isSegment={isSegment}
                audienceTypes={audienceTypes}
                data-test="target-selection-selected-item"
              />
            </div>
          </WrapperSelectItem>
        </ContainerSelectAudience>

        {[
          `${use}-${AUDIENCE_TYPE.customerSegmentAudiences}`,
          `${use}-${AUDIENCE_TYPE.visitorSegmentAudiences}`,
        ].includes(id) && (
          <WrapperButtonFooter ref={btnRef} data-test="target-selection-footer">
            <UIButton
              className="m-bottom-1"
              iconName="add"
              theme="text-link"
              reverse
              iconSize="24px"
              style={{
                fontSize: '12px',
                marginLeft: '4px',
                marginTop: '5px',
                color: '#1F5FAC',
              }}
              onClick={() => setOpenPopover(true)}
            >
              <span style={{ color: '#1F5FAC', fontSize: '12px' }}>
                CREATE NEW AUDIENCE
              </span>
              <i
                style={{ marginLeft: '5px' }}
                className="icon-antsomi-angle-down"
              />
            </UIButton>
            <TextFooter>Create a new custom or lookalike audience</TextFooter>
            <Popover
              open={openPopover}
              anchorEl={btnRef.current}
              onClose={() => setOpenPopover(false)}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              // transformOrigin={{
              //   vertical: 'top',
              //   horizontal: 'left',
              // }}
            >
              {/* {renderLabel(id)} */}
              <SegmentLabel segmentId={id} use={use} onClick={handleSegment} />
            </Popover>
          </WrapperButtonFooter>
        )}
      </ContainerTargetAudience>
      {state.errors.length > 0 && (
        <DivError className="m-top-1">{state.errors}</DivError>
      )}
    </>
  );
};

function capitalize(string) {
  return string.replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase());
}

UITargetSelection.defaultProps = {
  initData: {},
  onChange: () => {},
  validateKey: 1,
  disabledRemove: false,
  isHiddentListViewMode: false,
  disabledSelect: false,
  isHiddenIconRemove: false,
};

const mapStateToProps = createStructuredSelector({
  mainConfigure: makeSelectConfigureMainCreateWorkflow(),
  main: makeSelectMainCreateSegment(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig && props.moduleConfig.key;

  return {
    onUpdateFlattenNode: value =>
      dispatch(updateValue(`${prefix}@@FLATTEN_NODES@@`, value)),
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(memo(UITargetSelection));
