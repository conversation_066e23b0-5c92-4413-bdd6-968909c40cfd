/* eslint-disable import/no-cycle */
// Libraries
import React, { Fragment, useCallback } from 'react';
import PropTypes from 'prop-types';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import Typography from '@material-ui/core/Typography';
import { Button, Icon } from '@antscorp/antsomi-ui';

// Styled
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  WrapperIcon,
  useStyles,
} from './styled';

// Utils
import { RenderComponent } from './utils';

const PATH = 'app/components/Molecules/ActionButtons/ItemButton/index.jsx';

function ItemButton(props) {
  const classes = useStyles();

  const {
    id,
    configureFields,
    dynamicFields,
    isShowRemoveIcon,
    isViewMode,
    label,
    callback,
    isNewCustomTag,
    moduleConfig,
    journeyNodeTagProperties,
    onTagRemove,
    onChangeExtraDataTagProperty,
    ...dataConfigs
  } = props;

  const handleRemoveBtnItem = (e, buttonId) => {
    e.stopPropagation();

    if (typeof callback === 'function') {
      callback('REMOVE_BUTTON_ITEM', { buttonId });
    }
  };

  const handleChangeData = useCallback(
    name => value => {
      if (typeof callback === 'function') {
        callback('ON_CHANGE_ITEM', { buttonId: id, name, value });
      }
    },
    [callback, dataConfigs, id],
  );

  const renderContentItem = () => {
    const combineFields = [];

    if (Array.isArray(configureFields)) {
      combineFields.push(...configureFields);
    }
    if (Array.isArray(dynamicFields)) {
      combineFields.push(...dynamicFields);
    }

    const content = combineFields.map(eachField => (
      <RenderComponent
        key={`${id}-${eachField}`}
        {...dataConfigs[eachField]}
        classes={classes}
        isViewMode={isViewMode}
        isNewCustomTag={isNewCustomTag}
        moduleConfig={moduleConfig}
        useAllocatedCode={props.useAllocatedCode}
        journeyNodeTagProperties={journeyNodeTagProperties}
        onTagRemove={onTagRemove}
        onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
        onChange={handleChangeData}
      />
    ));

    return <Fragment>{content}</Fragment>;
  };

  return (
    <ErrorBoundary path={PATH}>
      <Accordion defaultExpanded>
        <AccordionSummary
          id={id}
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1a-content"
        >
          <Typography className={classes.heading}>{label}</Typography>
          {isShowRemoveIcon && !isViewMode && (
            <WrapperIcon onClick={e => handleRemoveBtnItem(e, id)}>
              <Button
                type="text"
                icon={<Icon type="icon-ants-remove-slim" size={12} />}
              />
            </WrapperIcon>
          )}
        </AccordionSummary>
        <AccordionDetails>{renderContentItem()}</AccordionDetails>
      </Accordion>
    </ErrorBoundary>
  );
}

ItemButton.defaultProps = {
  id: '',
  isShowRemoveIcon: false,
  isViewMode: false,
  label: 'Button',
  configureFields: [],
  useAllocatedCode: false,
  isNewCustomTag: true,
  dynamicFields: [],
  callback: () => {},
};
ItemButton.propTypes = {
  id: PropTypes.string,
  isShowRemoveIcon: PropTypes.bool,
  isViewMode: PropTypes.bool,
  label: PropTypes.string,
  configureFields: PropTypes.array,
  useAllocatedCode: PropTypes.bool,
  isNewCustomTag: PropTypes.bool,
  journeyNodeTagProperties: PropTypes.object,
  moduleConfig: PropTypes.object,
  onTagRemove: PropTypes.func,
  onChangeExtraDataTagProperty: PropTypes.func,
  dynamicFields: PropTypes.array,
  callback: PropTypes.func,
};
export default ItemButton;
