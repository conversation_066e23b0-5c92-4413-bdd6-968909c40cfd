// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

export const LIMIT_CONDITION = 5;

export const CONDITION_TYPE = Object.freeze({
  DYNAMIC_CODE: 'dynamic_code',
  EVENT: 'event',
});

export const OPERATOR = Object.freeze({
  CONTAINS: 'contains',
  NOT_CONTAIN: 'not_contain',
  EQUAL_TO: 'equal_to',
  NOT_EQUAL_TO: 'not_equal_to',
});

export const OPERATOR_OPTIONS = Object.freeze([
  {
    label: translate(translations._OPERATOR_EQUAL, 'equal to'),
    value: OPERATOR.EQUAL_TO,
  },
  {
    label: translate(translations._OPERATOR_NOT_EQUAL, 'not equal to'),
    value: OPERATOR.NOT_EQUAL_TO,
  },
  {
    label: translate(translations._OPERATOR_CONTAIN, 'contains'),
    value: OPERATOR.CONTAINS,
  },
  {
    label: translate(translations._OPERATOR_NOT_CONTAIN, 'not contain'),
    value: OPERATOR.NOT_CONTAIN,
  },
]);
