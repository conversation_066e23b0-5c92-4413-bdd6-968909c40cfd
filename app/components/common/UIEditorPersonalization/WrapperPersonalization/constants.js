// Constants
import { TAG_COLOR, TAG_TYPE } from '@antscorp/antsomi-ui';

// Locales
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';

const { JOURNEY, CAMPAIGN, VARIANT, ALLOCATED_CODE } = TAG_TYPE;

export const {
  COMMON_CODE,
  VISITOR_CODE,
  CUSTOMER_CODE,
  EVENT_CODE,
  PROMOTION_CODE,
  OBJECT_WIDGET_CODE,
  JOURNEY_CUSTOM_FN_CODE,
  PRODUCT_TEMPLATE_CODE,
  MARKETING_HUB_CODE,
  JOURNEY_CODE,
  CAMPAIGN_CODE,
  VARIANT_CODE,
  SHORT_LINK_V2,
} = Object.freeze({
  COMMON_CODE: 'common', // common code prefer for attr visitor, customer, event
  VISITOR_CODE: 'visitor',
  CUSTOMER_CODE: 'customer',
  EVENT_CODE: 'event',
  PROMOTION_CODE: 'promotion_code',
  OBJECT_WIDGET_CODE: 'objectWidget',
  JOURNEY_CUSTOM_FN_CODE: 'custom',
  PRODUCT_TEMPLATE_CODE: 'productTemplate',
  MARKETING_HUB_CODE: 'marketing_hub', // Marketing Hub prefer for journey, campaign, variant list attr
  JOURNEY_CODE: JOURNEY,
  CAMPAIGN_CODE: CAMPAIGN,
  VARIANT_CODE: VARIANT,
  SHORT_LINK_V2: TAG_TYPE.SHORT_LINK_V2,
});

export const DEFAULT_GROUP_CODES = [
  VISITOR_CODE,
  CUSTOMER_CODE,
  EVENT_CODE,
  JOURNEY_CODE,
  CAMPAIGN_CODE,
  VARIANT_CODE,
];

export const DEFAULT_DATA_TYPES = [
  'string',
  'number',
  'datetime',
  'array_string',
];

export const setInput = new Set(['input']);

export const DATA_GROUPS = {
  [PROMOTION_CODE]: {
    value: PROMOTION_CODE,
    label: getTranslateMessage(
      TRANSLATE_KEY._PERSONALIZE_PROMO_CODE,
      'Promotion Code',
    ),
    color: '#6750A7',
  },
  [OBJECT_WIDGET_CODE]: {
    value: OBJECT_WIDGET_CODE,
    label: 'Object Widget',
    color: '#24abb9',
  },
  [JOURNEY_CUSTOM_FN_CODE]: {
    value: JOURNEY_CUSTOM_FN_CODE,
    label: getTranslateMessage(TRANSLATE_KEY._PERSONALIZE_CUSTOM, 'Custom'),
    color: '#bbefbe',
  },
  [PRODUCT_TEMPLATE_CODE]: {
    value: PRODUCT_TEMPLATE_CODE,
    label: 'Product Template',
    color: '#9238b5',
  },
  [JOURNEY_CODE]: {
    value: JOURNEY_CODE,
    label: getTranslateMessage(
      TRANSLATE_KEY._PERSONALIZE_STORY,
      'Journey Attribute',
    ),
    color: TAG_COLOR[JOURNEY_CODE],
  },
  [CAMPAIGN_CODE]: {
    value: CAMPAIGN_CODE,
    label: getTranslateMessage(
      TRANSLATE_KEY._PERSONALIZE_CAMPAIGN,
      'Campaign Attribute',
    ),
    color: TAG_COLOR[CAMPAIGN_CODE],
  },
  [VARIANT_CODE]: {
    value: VARIANT_CODE,
    label: getTranslateMessage(
      TRANSLATE_KEY._PERSONALIZE_VARIANT,
      'Variant Attribute',
    ),
    color: TAG_COLOR[VARIANT_CODE],
  },
  [ALLOCATED_CODE]: {
    value: ALLOCATED_CODE,
    label: getTranslateMessage(
      TRANSLATE_KEY._PERSONALIZE_PROCESS_CODE,
      'Process Allocated Code',
    ),
    color: TAG_COLOR[ALLOCATED_CODE],
  },
  [SHORT_LINK_V2]: {
    value: SHORT_LINK_V2,
    label: 'Short link',
  },
};
