/* eslint-disable consistent-return */
// Libraries
import React, { memo, useCallback } from 'react';
import { isFunction } from 'lodash';
import PropTypes from 'prop-types';

// Locales
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';

// Components
import { Input } from '@antscorp/antsomi-ui';
import WrapperPersonalization from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import MultipleDropdownList from './MultipleDropdownList';
import SingleDropdownList from './SingleDropdownList';

// Constants
import { INPUT_TYPES } from '../constants';

const {
  TEXT,
  DATE,
  TEXT_AREA,
  LINK,
  STAGE,
  NUMBER,
  SINGLE_DROPDOWN,
  MULTI_DROPDOWN,
} = INPUT_TYPES;

function DynamicFields(props) {
  // Props
  const {
    type,
    name,
    value,
    isViewMode,
    enableShortLink,
    componentKey,
    groupCodes,
    options,
    mapOptions,
    isForceHideBtnPersonalization,
    otherData,
    onTagRemove,
    onChangeExtraDataTagProperty,
    onChange,
    onChangeOthers,
  } = props;

  const handleChange = useCallback(
    newValue => {
      if (isFunction(onChange)) {
        onChange({ type, changeKey: name, value: newValue });
      }
    },
    [type, name, onChange],
  );

  switch (type) {
    case STAGE:
    case SINGLE_DROPDOWN: {
      return (
        <SingleDropdownList
          componentKey={componentKey}
          value={value}
          isViewMode={isViewMode}
          options={options}
          mapOptions={mapOptions}
          onChange={handleChange}
          groupCodes={groupCodes}
          isForceHideBtnPersonalization={isForceHideBtnPersonalization}
          enableShortLink={enableShortLink}
          otherData={otherData}
          onChangeOthers={onChangeOthers}
        />
      );
    }
    case MULTI_DROPDOWN: {
      return (
        <MultipleDropdownList
          componentKey={componentKey}
          value={value}
          isViewMode={isViewMode}
          options={options}
          mapOptions={mapOptions}
          onChange={handleChange}
          groupCodes={groupCodes}
          isForceHideBtnPersonalization={isForceHideBtnPersonalization}
          enableShortLink={enableShortLink}
          otherData={otherData}
          onChangeOthers={onChangeOthers}
        />
      );
    }
    case TEXT:
    case DATE:
    case TEXT_AREA:
    case NUMBER:
    case LINK: {
      return (
        <WrapperPersonalization
          componentKey={componentKey}
          typeComponent="input"
          initValue={value}
          useAllocatedCode={props.useAllocatedCode}
          moduleConfig={props.moduleConfig}
          journeyNodeTagProperties={props.journeyNodeTagProperties}
          isNewCustomTag={props.isNewCustomTag}
          value={value}
          placeholder={type === DATE ? 'YYYY/MM/DD' : undefined}
          groupCodes={groupCodes}
          isForceHideBtnPersonalization={isForceHideBtnPersonalization}
          isViewMode={isViewMode}
          enableShortLink={enableShortLink}
          otherData={otherData}
          onTagRemove={onTagRemove}
          onChangeExtraDataTagProperty={onChangeExtraDataTagProperty}
          onChange={handleChange}
          onChangeOthers={onChangeOthers}
        />
      );
    }
    default: {
      // Fallback case for unknown type
      // or custom field have been removed
      return (
        <Input
          value={JSON.stringify(value)}
          errorMsg={getTranslateMessage(
            TRANSLATE_KEY._NOTI_FAIL_FIELD_REMOVE_BY_3RD_ERR,
            'This custom field has been removed',
          )}
          status="error"
          readOnly
          onChange={() => {}}
        />
      );
    }
  }
}

DynamicFields.propTypes = {
  name: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  value: PropTypes.any,
  componentKey: PropTypes.string.isRequired,
  isViewMode: PropTypes.bool,
  groupCodes: PropTypes.array,
  enableShortLink: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.any),
  isNewCustomTag: PropTypes.bool,
  mapOptions: PropTypes.object,
  otherData: PropTypes.object,
  onTagRemove: PropTypes.func,
  onChangeExtraDataTagProperty: PropTypes.func,
  onChange: PropTypes.func,
  onChangeOthers: PropTypes.func,
};
DynamicFields.defaultProps = {
  isForceHideBtnPersonalization: false,
  isNewCustomTag: true,
  groupCodes: ['visitor', 'customer', 'event'],
  options: [],
  mapOptions: {},
  otherData: {},
  onChange: () => {},
  onChangeOthers: () => {},
};
export default memo(DynamicFields);
