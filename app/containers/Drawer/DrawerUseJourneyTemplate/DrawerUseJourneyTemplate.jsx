/* eslint-disable react/prop-types */
import React from 'react';
import { Header } from './styled';
import { Button, DrawerDetail } from '@antscorp/antsomi-ui';
import { useDispatch, useSelector } from 'react-redux';
import { isFunction } from 'lodash';

import {
  JourneyTemplate,
  JourneyTemplateProvider,
  MODE as JOURNEY_TEMPLATE_MODE,
} from '../../JourneyTemplate';
import TicketApp from '../../TicketApp';
import {
  selectAllowApplyJT,
  selectIsApplying,
} from '../../JourneyTemplate/selector';
import { applyJT } from '../../JourneyTemplate/actions';

export const DrawerUseJourneyTemplate = props => {
  const { moduleConfig = {}, onApply, template, open = false, onClose } = props;

  const dispatch = useDispatch();

  const allowApply = useSelector(selectAllowApplyJT);
  const isApplying = useSelector(selectIsApplying);

  const handleClose = () => {
    if (isFunction(onClose)) {
      onClose();
    }
  };

  const handleApplyJT = () => {
    if (!allowApply) return;

    dispatch(applyJT());
  };

  return (
    <DrawerDetail
      open={open}
      menuProps={{
        show: false,
        showExpandButton: false,
      }}
      destroyOnClose
    >
      <JourneyTemplateProvider
        data={template}
        mode={JOURNEY_TEMPLATE_MODE.Use}
        moduleConfig={moduleConfig}
      >
        <Header>
          {template && <span className="header-title">{template.name}</span>}

          <div className="header-right">
            <TicketApp />
            <Button
              loading={isApplying}
              onClick={handleApplyJT}
              disabled={!allowApply}
              type="primary"
            >
              Apply
            </Button>
            <Button onClick={handleClose}>Cancel</Button>
          </div>
        </Header>

        {template && <JourneyTemplate onApply={onApply} />}
      </JourneyTemplateProvider>
    </DrawerDetail>
  );
};
