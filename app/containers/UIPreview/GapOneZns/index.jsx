// Libraries
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';

// Assets
import iphoneImg from 'assets/images/simulator/zalo-zns-v2.png';

// services
import DestinationServices from 'services/Destination';

// Hooks
import { useDeepCompareEffect } from '../../../hooks';

// Components
import {
  StyledContainInfoNetwork,
  StyledContainTextWrapper,
  StyledWrapper,
  StyledWrapperZNSPreview,
} from './styled';
import { Spin, useUpdateEffect } from '@antscorp/antsomi-ui';

// Selectors
import {
  makeSelectDashboardNetworkInfo,
  makeSelectPersonalizations,
} from '../../../modules/Dashboard/selector';

// Utils
import { processTemplateToEl, replaceNbsps } from '../utils';
import { addMessageToQueue } from '../../../utils/web/queue';

const { getGapOneTemplateDesc } = DestinationServices.info;

const PATH = 'app/containers/UIPreview/GapOneZns/index.jsx';

const GapOneZns = ({ data, networkInfo, personalizations, isCapture }) => {
  const { templateId: templateSetting = {}, templateData = {} } = data;
  const templateValue = templateData?.value;
  const refContain = useRef(null);
  const wrapperText = useRef(null);
  const abortControllerRef = useRef(null);
  const templateDataRef = useRef(templateValue);
  const cacheRef = useRef(new Map()); // Cache storage
  const [isLoading, setIsLoading] = useState(false);

  templateDataRef.current = templateValue;
  const { networkName } = networkInfo;

  const { listKey, previewUrl } = useMemo(() => {
    const templateParams = templateSetting?.value?.params;

    return {
      previewUrl: templateSetting?.value?.template_object?.previewUrl,
      listKey:
        typeof templateParams === 'string' ? templateParams.split(',') : [],
    };
  }, [templateSetting]);

  const personalizationData = useMemo(() => {
    return personalizations?.settings?.personalizationData || {};
  }, [personalizations]);

  const fetchTemplateContent = useCallback(
    async newPreviewUrl => {
      // Check cache first
      const cacheKey = `${newPreviewUrl}-${listKey}-${isCapture}`;
      if (cacheRef.current.has(cacheKey)) {
        const cachedData = cacheRef.current.get(cacheKey);
        processTemplateToEl({
          el: refContain.current,
          template: cachedData,
          isCapture,
          listKey,
          dynamicAsDefault: true,
          templateData: templateDataRef.current,
          wrapperTextEl: wrapperText.current,
        });
        return;
      }

      setIsLoading(true);

      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      try {
        const response = await getGapOneTemplateDesc({
          signal: abortControllerRef.current.signal,
          body: { previewUrl: newPreviewUrl },
        });

        if (response.code === 200 && response?.data?.[0]) {
          const { previewUrlDescription } = response.data[0];

          if (previewUrlDescription) {
            // Store in cache
            cacheRef.current.set(cacheKey, previewUrlDescription);

            processTemplateToEl({
              el: refContain.current,
              template: previewUrlDescription,
              isCapture,
              listKey,
              dynamicAsDefault: true,
              templateData: templateDataRef.current,
              wrapperTextEl: wrapperText.current,
            });
          }
        }
      } catch (error) {
        console.error('Error fetching template content:', error);
        addMessageToQueue({
          path: PATH,
          func: 'fetchTemplateContent',
          data: { error: error.stack },
        });
      } finally {
        setIsLoading(false);
      }
    },
    [listKey, isCapture],
  );

  const updateContentElements = useCallback(
    updateData => {
      if (!refContain.current) return;

      try {
        const iframeElement = refContain.current?.querySelector('iframe');
        const iframeDocument = iframeElement?.contentDocument;
        const contentIframeHTML = iframeDocument?.documentElement;

        if (!iframeDocument || !contentIframeHTML) return;

        const addTransitionStyles = () => {
          const existingStyle = iframeDocument?.head?.querySelector(
            '#smooth-update-styles',
          );
          if (!existingStyle) {
            const style = iframeDocument.createElement('style');
            style.id = 'smooth-update-styles';
            style.textContent = `
            .dynamic-text-element {
              transition: opacity 0.3s ease, transform 0.3s ease;
              will-change: opacity, transform;
            }
            .updating {
              opacity: 0.5;
              transform: scale(0.98);
            }
          `;
            iframeDocument.head.appendChild(style);
          }
        };
        addTransitionStyles();

        const elementsToUpdate = [];
        Object.entries(updateData).forEach(([key, value]) => {
          const elDynamicText = contentIframeHTML.querySelector(
            `.${key}.dynamic-text-element`,
          );
          if (elDynamicText) {
            const innerText =
              replaceNbsps(value, personalizationData) || `{${key}}`;
            elementsToUpdate.push({ element: elDynamicText, text: innerText });
          }
        });

        // Use requestAnimationFrame for smooth updates
        requestAnimationFrame(() => {
          elementsToUpdate.forEach(({ element, text }) => {
            element.classList.add('updating');
            element.innerText = text;
          });

          // Remove updating class after a short delay
          setTimeout(() => {
            elementsToUpdate.forEach(({ element }) => {
              element.classList.remove('updating');
            });
          }, 50);
        });
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'updateContentElements',
          data: { error: error.stack },
        });
      }
    },
    [personalizationData],
  );

  useDeepCompareEffect(() => {
    if (!previewUrl) return;

    fetchTemplateContent(previewUrl);

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (refContain.current) {
        refContain.current.replaceChildren();
      }
    };
  }, [previewUrl, fetchTemplateContent]);

  useUpdateEffect(() => {
    if (Object.keys(templateValue || {}).length > 0) {
      updateContentElements(templateValue);
    }
  }, [templateValue, updateContentElements]);

  return (
    <StyledWrapperZNSPreview>
      <StyledContainInfoNetwork>
        <div className="portal-name">
          <span>{networkName}</span>
          <span className="status">Official Account</span>
        </div>
      </StyledContainInfoNetwork>

      <img alt="preview" src={iphoneImg} />

      <StyledContainTextWrapper ref={wrapperText}>
        <StyledWrapper $spinning={isLoading}>
          <Spin spinning={isLoading} delay={400}>
            <div id="template-preview" ref={refContain} />
          </Spin>
        </StyledWrapper>
      </StyledContainTextWrapper>
    </StyledWrapperZNSPreview>
  );
};

const mapStateToProps = createStructuredSelector({
  networkInfo: makeSelectDashboardNetworkInfo(),
  personalizations: makeSelectPersonalizations(),
});

export default connect(
  mapStateToProps,
  null,
)(GapOneZns);
