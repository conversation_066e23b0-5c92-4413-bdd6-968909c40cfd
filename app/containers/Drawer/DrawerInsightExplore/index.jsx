/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable react/prop-types */

// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { useImmer } from 'use-immer';

// components
import IconButton from '@material-ui/core/IconButton';
import { Drawer } from '@antscorp/antsomi-ui';
import { UIButton as Button } from '@xlab-team/ui-components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import ModalExploreSegmentPlan from '../../modals/ModalExploreSegmentPlan';
import UIIconXlab from '../../../components/common/UIIconXlab';

// redux
import { makeSelectMainCreateSegment } from '../../../modules/Dashboard/Profile/Segment/Create/selectors';

// utils
import { toAPISegmentMember } from '../../Segment/Content/SegmentMember/utils';
import {
  getCurrentAccessUserId,
  getPortalId,
  getWindowParam,
} from '../../../utils/web/cookie';
import { checkChangeSegment, getBuildTime, getCondition } from './utils';
import { getTranslateMessage } from '../../Translate/util';
import { isProduction } from '../../../utils/common';
import { getAppCodeByMenuCode } from '../../../utils/web/permission';

// styles
import { WrapperStyleIcon } from './styles';
import WrapperDisable from '../../../components/common/WrapperDisable';

// actions
import { addNotification } from '../../../redux/actions';

// services
import { handleBuildAndCheckSegment } from '../../Segment/util';

// constants
import TRANSLATE_KEY from '../../../messages/constant';

const notification = {
  BUILD_DONE: {
    message: getTranslateMessage(
      TRANSLATE_KEY._NOTIFICATION_SUCCESS,
      'Build segment done.',
    ),
    timeout: 2000,
    type: 'success',
  },
  BUILD_ERROR: {
    message: getTranslateMessage(
      TRANSLATE_KEY._NOTIFICATION_ERROR,
      'Build segment failed, please try again',
    ),
    timeout: 2000,
    type: 'danger',
  },
};

function DrawerInsightExplore(props) {
  const {
    use = 'button',
    labelExplore = '',
    activeRow = {},
    configure,
    design,
    main,
    styledWrapper = {},
  } = props;

  const [state, setState] = useImmer({
    isOpenDrawer: false,
    isOpenExploreModal: false,
    isBuilding: false,
    isAllowRebuild: false,
    ruleResponse: null, // res {ruleId} sau khi call build api lan dau
    ruleId: null, // ==> de luu rule Id có computeStatus: 3 de bắn ruleId nay sang insight
    prevState: null,
  });
  const [progress, setProgress] = useState(0);

  const {
    isOpenDrawer,
    isOpenExploreModal,
    isBuilding,
    isAllowRebuild,
    ruleResponse,
    ruleId,
    prevState,
  } = state;
  const conditionRule = useMemo(() => {
    setState(draft => {
      (draft.isBuilding = false), // Nếu condition bị thay đổi thì sẽ ngưng tiến trình build trước đó
        (draft.isAllowRebuild = false); // Khi thay đổi condition thì cho ko cho rebuild lại condition bị đã bị build fail
    });
    if (conditionRule) {
      setState(draft => (draft.prevState = conditionRule));
    }
    if (configure && main) {
      const condition = getCondition(configure, main) || {};
      const refine = toAPISegmentMember(configure.main.segmentMember);
      return { condition, refine };
    }
  }, [configure]);

  const isChanging = useMemo(() => {
    if (isBuilding) {
      return false;
    }
    if (conditionRule) {
      const segmentRule = {
        condition: activeRow.conditions || {},
        refine: activeRow.refine || {},
      };
      return checkChangeSegment(conditionRule, prevState || segmentRule);
    }
  }, [configure, isBuilding, conditionRule]);

  const [isDisplayExplore, isDisableExplore] = useMemo(() => {
    let isValid = false;
    let isDisplay = true;

    if (conditionRule && configure && configure.main) {
      isDisplay = [1, 2].includes(+configure.main.type);

      isValid =
        configure.main.type === 1
          ? !!conditionRule.condition.OR.length
          : configure.main.type === 2
          ? !!conditionRule.condition.mapping_fields.length &&
            conditionRule.condition.mapping_fields.every(
              each => !!each.header_code,
            )
          : false;
    }
    return [isDisplay, isValid];
  }, [conditionRule]);

  const envProduction = isProduction();
  const domain = envProduction
    ? 'https://insights.antsomi.com'
    : 'https://sandbox-antalyser.antsomi.com';
  const portal = envProduction ? getPortalId() : '33167';
  const userId = getCurrentAccessUserId();
  const menuCode = getWindowParam('menuCodeActive') || '';
  const appCode = getAppCodeByMenuCode(menuCode);
  const url = `${domain}/${portal}#/${userId}/report/explorers/segment?oid=${userId}&_app_code=${appCode}&_menu_code=${menuCode}`;

  let itemTypeId;
  let segmentType;
  const initRuleId =
    (configure && configure.main && configure.main.initRuleId) ||
    (activeRow && activeRow.initRuleId);

  if (design === 'create') {
    itemTypeId = +main.itemTypeId;
    segmentType = configure.main.type;
  }

  if (design === 'update') {
    itemTypeId = activeRow.item_type_id;
    segmentType = +activeRow.segment_type;
  }

  useEffect(() => {
    // const initRuleId = get(configure, 'main.initRuleId', null);

    if (!initRuleId || !itemTypeId || !segmentType) return;

    (async () => {
      const res = await handleBuildAndCheckSegment.checkBuildSegment({
        ruleId: initRuleId,
        data: {
          itemTypeId,
          type: +segmentType,
        },
      });

      if (res.computeStatus === 3) {
        setState(draft => {
          (draft.isOpenDrawer = true), (draft.ruleId = initRuleId);
        });
      }
    })();
  }, [itemTypeId, initRuleId]);

  useEffect(() => {
    if (isOpenDrawer) {
      const timer = setInterval(() => {
        const iframe = document.querySelector('iframe');
        const itemTypeId =
          props.item_type_id ||
          activeRow.item_type_id ||
          +props.main.itemTypeId;
        const segmentId = props.segment_id || activeRow.segment_id || null;
        const exploreName = props.segment_display;
        if (iframe) {
          (async () => {
            const buildTime = await getBuildTime(ruleResponse, segmentId);
            const data = {
              type: 'CDP_SEGMENT_EXPLORE',
              data: {
                ruleId,
                itemTypeId,
                segmentId,
                lastBuildTime: buildTime,
              },
            };

            if (exploreName) {
              data.data.exploreDataName = exploreName;
            }

            console.log('data iframe', data);
            iframe.contentWindow.postMessage(data, '*'); // post data to insight explore segment

            window.addEventListener('message', event => {
              if (event.data.type === 'GET_SEGMENT_DONE') {
                clearInterval(timer);
              }
            });
          })();
        }
      }, 2000);
    }
  }, [isOpenDrawer, ruleId]);

  useEffect(() => {
    if (isChanging) {
      setProgress(0);
      setState(draft => {
        (draft.ruleId = null), (draft.ruleResponse = null);
      });
    }
  }, [isChanging]);

  useEffect(() => {
    window.addEventListener('message', event => {
      if (event.data.type === 'EXPLORE_ANTALYSER_CLOSE') {
        setState(draft => {
          draft.isOpenDrawer = false;
        });
      }
    });
  }, []);

  useEffect(() => {
    if (!ruleResponse || !itemTypeId || !segmentType || !isOpenExploreModal)
      return;
    let timer;
    if (ruleResponse.computeStatus !== 3) {
      let count = progress;
      timer = setInterval(() => {
        const randomNum = Math.floor(Math.random() * 6);
        if (count + randomNum >= 95) {
          count = 95;
          setProgress(count);
        } else {
          count += randomNum;
          setProgress(count);
        }
        handleBuildAndCheckSegment
          .checkBuildSegment({
            data: {
              itemTypeId,
              type: segmentType,
            },
            ruleId: ruleResponse.ruleId,
          })
          .then(res => {
            if (res.computeStatus === 3) {
              setState(draft => {
                (draft.isBuilding = false),
                  (draft.ruleId = ruleResponse.ruleId),
                  (draft.ruleResponse = res),
                  (draft.isOpenExploreModal = false),
                  (draft.isOpenDrawer = true);
              });
              count = 100;
              setProgress(100);
              clearInterval(timer);
              props.addNotification(notification.BUILD_DONE);
            } else if (res.computeStatus === 4) {
              setState(draft => {
                (draft.isBuilding = false),
                  (draft.isOpenExploreModal = false),
                  (draft.isAllowRebuild = true);
              });
              setProgress(0);
              clearInterval(timer);
              props.addNotification(notification.BUILD_ERROR);
            }
          });
      }, 5000);
    }
    return () => {
      if (isOpenExploreModal) {
        clearInterval(timer);
      }
    };
  }, [ruleResponse, isOpenExploreModal]);

  const handleOpenDrawer = () => {
    setState(draft => {
      draft.isOpenDrawer = true;
    });
  };

  const onCloseExplore = () => {
    setState(draft => {
      draft.isOpenExploreModal = false;
    });
  };

  const handleProcessingSegment = async () => {
    if (isChanging || isAllowRebuild) {
      // Nếu thay đổi condition của segment hoặc tạo condition mới khi create segment mới thì tiến hành vào build and check ruleId
      setState(draft => {
        (draft.isOpenExploreModal = true), // mở modal chạy tiến trình processing...
          (draft.isBuilding = true),
          (draft.prevState = conditionRule);
      });

      const data = {
        itemTypeId: +main.itemTypeId,
        conditions: conditionRule.condition,
        refine: conditionRule.refine,
        segmentId: activeRow.segment_id || 0,
        segmentDisplay: configure.main.name,
        type: configure.main.type,
        segmentLink: window.location.href,
      };
      const res = await handleBuildAndCheckSegment.buildSegment({ data });
      setState(draft => {
        draft.ruleResponse = res;
      });
    } else if (
      ((+activeRow.process_status === 2 ||
        +activeRow.process_status === 3 ||
        +activeRow.process_status === 6) &&
        !isBuilding) || // Nếu segemnt đã đc build từ trước và ko có tiến trình build mới ruleId nào thì mở Drawer insight
      (!isBuilding && ruleId)
    ) {
      // Nếu segment đã build mới xong OR đã build có ruleID rồi nhưng ko thay đổi gì codition thì mở lại Drawe vào insight
      setState(draft => {
        draft.isOpenDrawer = true;
      });
    } else {
      setState(draft => {
        draft.isOpenExploreModal = true;
      });
    }
  };

  // Hide explore if segment type empty / unscribe
  if (!isDisplayExplore) {
    return null;
  }

  return (
    <>
      {use === 'icon' ? (
        <WrapperStyleIcon className="td-action" styledWrapper={styledWrapper}>
          <WrapperDisable>
            <IconButton
              onClick={handleOpenDrawer}
              variant="contained"
              aria-label="delete"
              size="small"
            >
              <UIIconXlab name="explorer" fontSize="20px" color="#595959" />
            </IconButton>
          </WrapperDisable>
        </WrapperStyleIcon>
      ) : (
        <WrapperDisable
          disabled={
            (design === 'update' &&
              !isChanging &&
              +props.activeRow.process_status !== 2 &&
              +props.activeRow.process_status !== 3 &&
              +props.activeRow.process_status !== 6) || // build failed
            !isDisableExplore // when create and add condition but empty condition
          }
        >
          <Button theme="outline" onClick={handleProcessingSegment}>
            <span className="" style={{ color: '#005fb8', fontSize: '12px' }}>
              {labelExplore}
            </span>
          </Button>
        </WrapperDisable>
      )}

      <Drawer // Drawer Explore segments
        destroyOnClose
        open={isOpenDrawer}
        placement="right"
        keyboard
        onClose={() => {
          setState(draft => {
            draft.isOpenDrawer = false;
          });
        }}
        closeIcon={false}
        width="calc(100vw - 70px)"
        styles={{
          body: {
            padding: 0,
          },
        }}
      >
        <iframe
          id="iframe-explore"
          allowFullScreen
          // src='https://localhost:8080/#/1600084823/report/explorers/segment?oid=1600084823'
          src={url}
          style={{ height: '100vh', width: '100%', border: 'none' }}
          title="Explorer"
        />
      </Drawer>

      <ModalExploreSegmentPlan
        progress={progress}
        open={isOpenExploreModal}
        onClose={onCloseExplore}
      />
    </>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectMainCreateSegment(),
});

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(DrawerInsightExplore);
