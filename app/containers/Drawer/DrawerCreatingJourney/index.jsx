/* eslint-disable no-nested-ternary */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
// libraries
import React, {
  forwardRef,
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import propsTypes from 'prop-types';
import { useImmer } from 'use-immer';
// redux
import {
  dashboardSetOwnerIdFromData,
  getList,
  init,
  reset,
  updateValue,
} from '../../../redux/actions';
import { connect, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Hooks
import { useThirdPartyCampaigns } from '../../../hooks/useThirdPartyCampaigns';

// components
import { UILoading } from '@xlab-team/ui-components';
import CustomSquare from '../../../modules/Dashboard/MarketingHub/Journey/CreateTemplate/_UI/TeamplateConfigs/components/BlastAppPush/components/CustomSquare';
import { ListTemplates } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/_UI/ListTemplates/Loadable';
import {
  DrawerDetail,
  EmptyData,
  Flex,
  Icon,
  QueryClientProviderAntsomiUI,
  Typography,
  queryClientAntsomiUI,
  useDeepCompareEffect,
  useLayoutStore,
} from '@antscorp/antsomi-ui';
import TemplateListing from '../../TemplateListing';

import {
  DrawerHeader,
  LeftSidePaneContent,
  MainContent,
  StyledLabel,
  TemplateCreateContainer,
  WrapperAppPush,
  WrapperContent,
  WrapperContentCreate,
  WrapperZaloTemplate,
} from './styles';
import TemplatesByCategory from './components/TemplatesByCategory';
import CreatingJourneyType from '../../Journey/Content/CreatingJourneyType';
import TemplateCreateScratch from './components/TemplateCreateScratch';
import StoriesCreateBlastCampaign from '../../../modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/Loadable';
import StoriesCreateModule from '../../../modules/Dashboard/MarketingHub/Journey/Create/Loadable';
import JSONTemplateEditor from '../../../components/common/UIEditorPersonalization/WrapperPersonalization/JSONTemplateEditor';
import StoriesDetailModuleV2 from '../../../modules/Dashboard/MarketingHub/Journey/Detail/indexV2';
import ThirdPartyCampaigns from './components/ThirdPartyCampaigns';

// utils
import { getDefaultActiveTypeCreate } from '../../Journey/utils';
import { getTranslateMessage } from '../../Translate/util';
import { getChannelCodeById } from '../../../modules/Dashboard/MarketingHub/Journey/utils';

// constants
import {
  CREATE_ITEM,
  CREATE_ITEM_VALUE,
  DISPLAY_CATEGORY_LAYOUT_TYPES,
  DISPLAY_TEMPLATE_FROM_SCRATCH,
  THIRD_PARTY_CAMPAIGN,
} from '../../Journey/constant';
import { CATALOG_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import TRANSLATE_KEY from '../../../messages/constant';
import { CHANNEL } from '../../../modules/Dashboard/MarketingHub/Journey/constant';
import { METHODS_KEY } from '../../../modules/Dashboard/MarketingHub/Journey/CreateTemplate/utils';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// MODULE CONFIG
import { MODULE_CONFIG } from '../../../modules/Dashboard/MarketingHub/Journey/config';
import { MODULE_CONFIG as MODULE_CONFIG_CREATE_BLAST_CAMPAIGN } from '../../../modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/config';
import { MODULE_CONFIG as MODULE_CONFIG_CREATE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/config';
import { MODULE_CONFIG as MODULE_CONFIG_LISTING } from '../../../modules/Dashboard/MarketingHub/Journey/List/config';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from '../../../modules/Dashboard/MarketingHub/Journey/Detail/config';

// utils
import {
  makeSelectClosableDrawerCreateJr,
  makeSelectIsOpenLayoutJourney,
  makeSelectIsOpenSubDrawer,
  makeSelectLoadingOnStepTwo,
} from '../../../modules/Dashboard/MarketingHub/Journey/selectors';
import { getTemplateType } from './utils';
import { get, isEmpty, isNaN } from 'lodash';
import { isProduction } from '../../../utils/common';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
  getPortalId,
} from '../../../utils/web/cookie';
import { KEY_PARAMS, VALUES_PARAMS } from '../DrawerIntegration/constants';
import { useHistory, useParams } from 'react-router-dom';
import { DRAWER_NAME_CACHE } from '../../../utils/constants';
import { DestinationsIcon } from '@antscorp/antsomi-ui/es/components/icons';
import APP from '../../../appConfig';
import {
  Actions,
  getPrefixCreate,
  useHeaderContext,
  Header,
} from '../../../modules/Dashboard/MarketingHub/Journey/Header';
import { KEY_PARAMS as KEY_PARAMS_JOURNEY } from '../../../modules/Dashboard/MarketingHub/Journey/Main/constants';
// Constants
const defaultMenuItems = [
  {
    icon: <Icon type="icon-ants-pencil" />,
    label: 'Settings',
    key: 'settings',
  },
];

const initState = channelId => ({
  activeTab: 'settings',
  typeCreateJourney: getDefaultActiveTypeCreate(channelId),
  channelCode: '',
  menuItems: defaultMenuItems,
});

const DrawerCreatingJourney = forwardRef((props, ref) => {
  const {
    isOpen,
    toggle,
    channelId,
    callback = () => {},
    drawerJourneyInfo,
    isOpenLayoutJourney,
    closableCreateJr,
  } = props;

  const history = useHistory();
  const { dispatch } = useHeaderContext();
  const { setHeaderState } = useLayoutStore();

  const { design, storyId, versionId } = drawerJourneyInfo;

  const [state, setState] = useImmer(initState(channelId));

  const mainDetail = useSelector(
    store => store.get(MODULE_CONFIG_DETAIL.key)?.main,
  );

  const isBlastCampaignDetail = useMemo(() => {
    return !!mainDetail?.isBlastCampaign;
  }, [mainDetail]);

  const activeRowDetail = useMemo(() => {
    return mainDetail?.activeRow || {};
  }, [mainDetail]);

  const {
    selectedKey,
    typeCreateJourney,
    channelCode,
    activeTab,
    menuItems,
  } = state;

  const { channelId: channelIdParams } = useParams();

  const [isCreateNormalJourney, setIsCreateNormalJourney] = useState(false);
  const [isOpenCurrentCollapse, setIsOpenCurrentCollapse] = useState('');

  // Memo
  const searchParams = new URLSearchParams(window.location.search);

  // check display list template categorry
  const isDisplayCategoryLayout = useMemo(() => {
    const defaultActiveValue =
      typeCreateJourney || getDefaultActiveTypeCreate(channelId);

    return DISPLAY_CATEGORY_LAYOUT_TYPES.includes(defaultActiveValue);
  }, [channelId, typeCreateJourney]);

  const { disabled: disabledFetchingThirdParty, channelSpec } = useMemo(() => {
    let channelSpecThirdParty = channelId;

    // In case journey-orchestration, need to check for channel sms
    if (CHANNEL.JOURNEY_ORCHESTRATION.id === +channelId) {
      channelSpecThirdParty = CHANNEL.SMS.id;
    }

    const disabled =
      !isOpen ||
      ![CHANNEL.SMS.id, CHANNEL.JOURNEY_ORCHESTRATION.id].includes(+channelId);

    return {
      disabled,
      channelSpec: channelSpecThirdParty,
    };
  }, [channelId, isOpen]);

  // check display list create template scratch (web personal)
  const isDisplayTemplateFromScratch = useMemo(() => {
    return (
      typeCreateJourney === CREATE_ITEM.SCRATCH.value &&
      DISPLAY_TEMPLATE_FROM_SCRATCH.includes(channelId)
    );
  }, [channelId, typeCreateJourney]);

  // Using to check and stored data if it is a third party campaign
  useThirdPartyCampaigns({
    channelId: channelSpec,
    options: {
      disabled: disabledFetchingThirdParty,
    },
  });

  useEffect(() => {
    if (searchParams.get('useTactic') === 'true') {
      props.toggleLayoutJourney(true);
      setIsCreateNormalJourney(true);
      if (searchParams.has('isBlastCampaign')) {
        setIsCreateNormalJourney(false);
      }
    }
  }, [searchParams.get('useTactic')]);

  useDeepCompareEffect(() => {
    if (!isOpenLayoutJourney || design !== 'create' || !ref.current) return;

    let moduleConfig = {};
    if (design === 'create') {
      if (isCreateNormalJourney) {
        moduleConfig = MODULE_CONFIG_CREATE;
      } else {
        moduleConfig = MODULE_CONFIG_CREATE_BLAST_CAMPAIGN;
      }

      ref.current.onChangeModuleConfig({
        key: moduleConfig.key,
      });
    }
  }, [design, isCreateNormalJourney, isOpenLayoutJourney]);

  useDeepCompareEffect(() => {
    if (
      !isOpenLayoutJourney ||
      design === 'create' ||
      isEmpty(activeRowDetail) ||
      !ref.current
    )
      return;

    const prefixCreate = getPrefixCreate(
      MODULE_CONFIG_DETAIL.key,
      !!isBlastCampaignDetail,
    );

    ref.current.onChangeModuleConfig({
      key: prefixCreate,
    });

    ref.current.onChangeActiveRow(activeRowDetail);
  }, [design, isOpenLayoutJourney, isBlastCampaignDetail, activeRowDetail]);

  useEffect(() => {
    // channel code
    const channelCodeById = getChannelCodeById(channelId);

    // default active value
    const defaultActiveValue = getDefaultActiveTypeCreate(channelId);

    setState(draft => {
      draft.typeCreateJourney = defaultActiveValue;
      draft.channelCode = channelCodeById;
    });
  }, [channelId, isOpen]);

  useEffect(() => {
    if (design !== 'create') {
      const newMenuItems = [
        {
          icon: <Icon type="icon-ants-pencil" />,
          label: 'Settings',
          key: 'settings',
        },
        {
          icon: <Icon type="icon-ants-version-history" />,
          label: 'Version History',
          key: 'version-history',
        },
      ];
      setState(draft => {
        draft.menuItems = newMenuItems;
      });
    }
  }, [design]);

  useEffect(() => {
    if (isOpenLayoutJourney) {
      toggle(false);
      if (!isCreateNormalJourney) {
        // use case : cập nhật lại trigger trong reducer của blast campaign trường hợp create từ EVENT BASED sang SCHEDULED
        props.updateTrigger({ channelId });
      }
    } else if (isCreateNormalJourney) {
      setIsCreateNormalJourney(false);
      props.resetJourney({ moduleConfig: MODULE_CONFIG_CREATE });
    } else {
      props.resetJourney({ moduleConfig: MODULE_CONFIG_CREATE_BLAST_CAMPAIGN });
    }

    if (searchParams.get('copyId')) {
      setIsCreateNormalJourney(true);
      if (searchParams.has('isBlastCampaign')) {
        setIsCreateNormalJourney(false);
      }
    }
  }, [isOpenLayoutJourney, isOpenLayoutJourney]);

  const onChangeData = (type, data) => {
    switch (type) {
      case 'ON_CHANGE_TYPE_CREATE':
        setState(draft => {
          draft.typeCreateJourney = data;
        });
        break;
      case 'USE_TEMPLATE': {
        const { model, templateType } = data;
        props.updateCreatingJourneyInfo({
          channelCode,
          templateType,
          data: {},
          selectedTemplateTemp: model,
        });
        props.updateLoadingOnStepTwo(true);
        props.toggleLayoutJourney(true);
        break;
      }

      case 'CREATE_NORMAL_JOURNEY': {
        setIsCreateNormalJourney(true);
        props.toggleLayoutJourney(true);
        break;
      }

      case 'CREATE_CAMPAIGN': {
        if (channelId === CHANNEL.EMAIL.id) {
          const templateType = getTemplateType(channelId, typeCreateJourney);
          const blankTemplate = {
            template_name: '',
            template_type: 4,
            device_type: 1,
            template_setting: {},
            properties: {
              deviceType: 1,
              template: {
                id: 4,
                type: 'inline',
                name: 'Inline',
              },
            },
            thumbnail: '',
          };

          props.updateCreatingJourneyInfo({
            channelCode,
            templateType,
            data: {},
            selectedTemplateTemp: blankTemplate,
          });
        } else if (channelId === CHANNEL.JOURNEY_ORCHESTRATION.id) {
          setIsCreateNormalJourney(true);
        }
        props.toggleLayoutJourney(true);
        break;
      }

      case 'ON_APPLY_TACTIC': {
        const { templateType, storySettings, isBlastCampaign } = data;
        props.updateCreatingJourneyInfo({
          data: {
            storySettings,
          },
          channelCode,
          templateType,
        });
        if (!isBlastCampaign) {
          setIsCreateNormalJourney(true);
        }

        props.toggleLayoutJourney(true);
        break;
      }

      case 'USE_FORM_TEMPLATE': {
        const { templateType } = data;
        const catalogInfo = get(data, 'template.catalogInfo', '');

        props.updateCreatingJourneyInfo({
          channelCode,
          templateType,
          catalogInfo,
          data,
        });
        props.toggleLayoutJourney(true);
        break;
      }

      case 'UPDATE_CURRENT_COLLAPSE': {
        if (typeof data === 'boolean') {
          setIsOpenCurrentCollapse(data);
        }
        break;
      }

      case 'ON_CREATE_CAMPAIGN_THIRD_PARTY': {
        const catalogInfo = get(data, 'template.catalogInfo', {});
        const sendMethod = get(data, 'sendMethod', '');

        props.updateCreatingJourneyInfo({
          data,
          catalogInfo,
          channelCode,
          sendMethod,
          templateType: THIRD_PARTY_CAMPAIGN.value,
        });
        props.toggleLayoutJourney(true);
        break;
      }

      default:
        break;
    }
  };

  const handleEndProcessCreateJourney = () => {
    let search = '';
    if (
      drawerJourneyInfo.tempOwnerId === 'all' ||
      isNaN(drawerJourneyInfo.tempOwnerId)
    ) {
      props.dashboardSetOwnerIdFromData('all');
    } else {
      const oid = drawerJourneyInfo.tempOwnerId || searchParams.get('oid');
      if (oid) {
        search = `?oid=${oid}`;
      }
    }

    // link to listing
    history.push({
      search,
    });

    if (drawerJourneyInfo.isSaveDone) {
      props.resetDrawer();
      props.init({
        moduleConfig: MODULE_CONFIG_LISTING,
        channelId: channelIdParams,
      });
    } else {
      props.resetDrawer();
    }
    setState(draft => initState());
  };

  const onCloseDrawer = (onCloseFunc = () => {}) => {
    onCloseFunc();
    handleEndProcessCreateJourney();
    props.resetCreatingJourneyInfo();
    props.resetInitDetailDone();
  };

  const onChangeTab = useCallback(
    tab => {
      setState(draft => {
        draft.activeTab = tab;
      });

      searchParams.set(KEY_PARAMS_JOURNEY.TAB, tab);
      searchParams.delete(KEY_PARAMS_JOURNEY.VERSION_ID);
      searchParams.delete(KEY_PARAMS_JOURNEY.COMPARE);

      history.push({
        search: searchParams.toString(),
      });
    },
    [history, setState, searchParams],
  );

  const renderContent = useCallback(() => {
    let element = null;
    if (isDisplayTemplateFromScratch) {
      element = (
        <TemplateCreateScratch
          channelId={channelId}
          callback={onChangeData}
          activeValue={typeCreateJourney}
        />
      );
    } else if (typeCreateJourney === CREATE_ITEM_VALUE.MEDIA_JSON) {
      element = (
        <TemplateCreateContainer>
          <JSONTemplateEditor
            templateType={METHODS_KEY.MEDIA_JSON}
            onChange={onChangeData}
            isCreateJourneyV2
            isBlastCampaign
          />
        </TemplateCreateContainer>
      );
    } else if (typeCreateJourney === CREATE_ITEM_VALUE.LINE_MESSAGE) {
      element = (
        <ListTemplates
          channelId={channelId}
          styles={{
            wrapperCard: { padding: 0 },
            head: { left: 0, marginTop: 10 },
            wrapperIconLabel: { height: '100%' },
            wrapperImage: { width: 310, height: 210 },
          }}
          // catalogCode={
          //   typeCreateJourney === CREATE_ITEM_VALUE.ZALO_OA
          //     ? CATALOG_CODE.ZALO_OA
          //     : CATALOG_CODE.ZNS
          // }
          catalogCode={CATALOG_CODE.LINE}
          onChangeActive={({ template }) => {
            if (typeof onChangeData === 'function') {
              onChangeData('USE_FORM_TEMPLATE', {
                template,
              });
            }
          }}
          styleComponent={{
            wrapper: {
              height: 'fit-content',
              padding: '0 15px',
            },
          }}
          isNewLayout
        />
      );
    } else if (typeCreateJourney === CREATE_ITEM_VALUE.RICH_MENU) {
      element = (
        <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}>
          <TemplateListing
            isDashboard
            channelId={channelId}
            catalogCode={CATALOG_CODE.LINE_RICH_MENU}
            onChange={({ template }) => {
              if (typeof onChangeData === 'function') {
                onChangeData('USE_FORM_TEMPLATE', { template });
              }
            }}
            styleComponent={{
              wrapper: {
                padding: '0 15px',
                display: 'block',
              },
              tab: {
                borderBottom: '1px solid #E5E5E5',
                boxShadow: 'none',
                height: 'fit-content',
              },
            }}
            contentEmpty={
              <EmptyData
                icon={<DestinationsIcon />}
                title="No destination available"
                description={
                  <span>
                    Create a{' '}
                    <Typography.Link
                      href={`${
                        APP.PREFIX
                      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/destinations/${channelId}`}
                      target="_blank"
                    >
                      {' '}
                      Line Rich Menu
                    </Typography.Link>{' '}
                    destination to use this feature
                  </span>
                }
              />
            }
          />
        </QueryClientProviderAntsomiUI>
      );
    } else if (
      [CREATE_ITEM_VALUE.ZALO_OA, CREATE_ITEM_VALUE.ZALO_ZNS].includes(
        typeCreateJourney,
      )
    ) {
      const isZaloOA = CREATE_ITEM_VALUE.ZALO_OA === typeCreateJourney;
      const isZaloZNS = CREATE_ITEM_VALUE.ZALO_ZNS === typeCreateJourney;

      const DOMAIN = isProduction()
        ? 'https://cdp.antsomi.com'
        : 'https://sandbox-cdp.antsomi.com';

      const portalId = getPortalId();
      const userId = getCurrentAccessUserId();

      const externalCreatePath = `${DOMAIN}/gen2/${portalId}/${userId}/marketing-hub/destinations/${channelId}`;

      element = (
        <Flex vertical gap={15} style={{ padding: '0px 15px', width: '100%' }}>
          {isZaloOA && (
            <Flex vertical gap={15} style={{ padding: '0px 15px' }}>
              <Typography.Text style={{ fontSize: '20px' }}>
                {getTranslateMessage(
                  TRANSLATE_KEY._ZALO_TEMP_SELECT_1,
                  'Various styles by Zalo Official Account',
                )}
              </Typography.Text>
              <Typography.Text style={{ color: globalToken?.bw8 }}>
                {getTranslateMessage(
                  TRANSLATE_KEY._ZALO_TEMP_SELECT_2,
                  'These templates are special for Zalo Official Account only. Click',
                )}
                &nbsp;
                <Typography.Link href={externalCreatePath} target="_blank">
                  {getTranslateMessage(
                    TRANSLATE_KEY._ZALO_TEMP_SELECT_3,
                    'here',
                  )}
                </Typography.Link>
                &nbsp;
                {getTranslateMessage(
                  TRANSLATE_KEY._ZALO_TEMP_SELECT_4,
                  'to create a destination.',
                )}
              </Typography.Text>
            </Flex>
          )}
          <WrapperZaloTemplate isZaloZNS={isZaloZNS}>
            <ListTemplates
              channelId={channelId}
              styles={{
                wrapperCard: { padding: 0 },
                head: {
                  fontSize: '12px',
                  fontWeight: 400,
                  textAlign: 'left',
                  position: 'relative',
                  left: 0,
                  marginTop: 10,
                },
                wrapperIconLabel: { height: '100%' },
                wrapperImage: { width: 310, height: 210 },
              }}
              catalogCode={
                typeCreateJourney === CREATE_ITEM_VALUE.ZALO_OA
                  ? CATALOG_CODE.ZALO_OA
                  : CATALOG_CODE.ZNS
              }
              onChangeActive={({ template }) => {
                if (typeof onChangeData === 'function') {
                  onChangeData('USE_FORM_TEMPLATE', {
                    template,
                  });
                }
              }}
              isNewLayout
            />
          </WrapperZaloTemplate>
        </Flex>
      );
    } else if (typeCreateJourney === CREATE_ITEM_VALUE.SMART_TEMPLATE) {
      // chờ gắn list template này
      element = <></>;
    } else if (typeCreateJourney === CREATE_ITEM_VALUE.NOTIFICATION_TEMPLATE) {
      const DOMAIN = isProduction()
        ? 'https://cdp.antsomi.com'
        : 'https://sandbox-cdp.antsomi.com';

      const portalId = getPortalId();
      const userId = getCurrentAccessUserId();
      const ownerId =
        getCurrentOwnerId() === 'all' ? userId : getCurrentOwnerId();

      const createDestinationURL = `${DOMAIN}/gen2/${portalId}/${userId}/marketing-hub/destinations/4?design=create&tab=settings&${
        KEY_PARAMS.MODE
      }=${VALUES_PARAMS.SUB_CREATE}&oid=${ownerId}`;

      element = (
        <WrapperAppPush>
          <div className="label">
            {getTranslateMessage(
              TRANSLATE_KEY._,
              'Exclusive styles by Antsomi App Push',
            )}
          </div>
          <div className="info-app-noti">
            {getTranslateMessage(
              TRANSLATE_KEY._,
              `These templates are special for Antsomi App Push only. `,
            )}
            {'Click '}
            <a href={createDestinationURL} target="_blank">
              here
            </a>
            {' to create destination'}
          </div>
          <ListTemplates
            channelId={channelId}
            catalogCode={CATALOG_CODE.ANTSOMI_APP_PUSH}
            onChangeActive={({ template }) => {
              if (typeof onChangeData === 'function') {
                onChangeData('USE_FORM_TEMPLATE', {
                  template,
                });
              }
            }}
            CustomSquare={CustomSquare}
            customSquareInfo={{
              labelUseTemplate: 'Create Campaign',
            }}
          />
        </WrapperAppPush>
      );
    } else if (typeCreateJourney === CREATE_ITEM_VALUE.THIRD_PARTY_CAMPAIGN) {
      element = (
        <ThirdPartyCampaigns
          channelId={CHANNEL.SMS.id}
          onChange={dataOut => {
            if (typeof onChangeData === 'function') {
              onChangeData('ON_CREATE_CAMPAIGN_THIRD_PARTY', dataOut);
            }
          }}
        />
      );
    }

    return element;
  }, [isDisplayTemplateFromScratch, typeCreateJourney]);

  return (
    <>
      <DrawerDetail
        key="journey-layer-1"
        open={isOpen}
        onClose={() => onCloseDrawer(toggle(false))}
        anchor="right"
        menuProps={{
          show: false,
          showExpandButton: false,
        }}
        width="calc(100vw - 70px)"
        closeIconProps={{
          show: true,
          style: {
            top: '12.5px',
          },
        }}
        destroyOnClose
        style={{ borderTopLeftRadius: 10, borderBottomLeftRadius: 10 }}
      >
        <StyledLabel>
          {getTranslateMessage(TRANSLATE_KEY._, 'What will you create today?')}
        </StyledLabel>
        <WrapperContent isDisplayCategoryLayout={isDisplayCategoryLayout}>
          {isDisplayCategoryLayout ? (
            <TemplatesByCategory
              onChange={onChangeData}
              channelId={channelId}
              activeValue={typeCreateJourney}
              isOpenCurrentCollapse={isOpenCurrentCollapse}
            />
          ) : (
            <>
              <LeftSidePaneContent>
                <CreatingJourneyType
                  channelId={channelId}
                  callback={onChangeData}
                  activeValue={typeCreateJourney}
                  wrapperStyle={{ width: 250 }}
                  isOpenCurrentCollapse={isOpenCurrentCollapse}
                />
              </LeftSidePaneContent>
              <MainContent>{renderContent()}</MainContent>
            </>
          )}
        </WrapperContent>
      </DrawerDetail>
      {isOpenLayoutJourney && props.loadingOnStepTwo && (
        <UILoading isLoading={props.loadingOnStepTwo} />
      )}
      <DrawerDetail
        rootStyle={
          props.loadingOnStepTwo ? { boxShadow: 'none', opacity: 0 } : {}
        }
        open={isOpenLayoutJourney}
        fullScreen={
          props.isOpenSubDrawer || props.isOpenModalDestinationTesting
        }
        onClose={() => {
          if (!closableCreateJr) return;

          onCloseDrawer(props.toggleLayoutJourney(false));

          setHeaderState({
            browserTitle: '',
          });

          if (ref.current) {
            ref.current.onReset();
          }
        }}
        closeIconProps={{
          style: !closableCreateJr
            ? { color: '#bebebe', cursor: 'not-allowed' }
            : {},
        }}
        menuProps={{
          items: menuItems,
          selectedKeys: [activeTab],
          onClick: item => {
            onChangeTab(item.key);
          },
        }}
        maskStyle={{ cursor: closableCreateJr ? 'pointer' : 'not-allowed' }}
        anchor="right"
        destroyOnClose
        headerProps={{
          children: <Header />,
          height: 50,
          style: {
            padding: '0',
          },
          showBorderBottom: true,
        }}
        name={DRAWER_NAME_CACHE.JOURNEY_CREATE}
      >
        {isOpenLayoutJourney ? (
          design === 'create' ? (
            <Fragment>
              {/* <UIHelmet title={pageTitle} /> */}
              {isCreateNormalJourney ? (
                <StoriesCreateModule
                  design="create"
                  moduleConfig={MODULE_CONFIG_CREATE}
                  isJourneyV2
                />
              ) : (
                <WrapperContentCreate>
                  <StoriesCreateBlastCampaign
                    design="create"
                    moduleConfig={MODULE_CONFIG_CREATE_BLAST_CAMPAIGN}
                    isJourneyV2
                    channelId={channelId}
                  />
                </WrapperContentCreate>
              )}
            </Fragment>
          ) : (
            <StoriesDetailModuleV2
              activeId={storyId}
              moduleConfig={MODULE_CONFIG_DETAIL}
              design={design}
              tab={activeTab}
              isJourneyV2
              channelId={channelId}
            />
          )
        ) : null}
      </DrawerDetail>
    </>
  );
});

const mapStateToProps = createStructuredSelector({
  isOpenSubDrawer: makeSelectIsOpenSubDrawer(),
  isOpenLayoutJourney: makeSelectIsOpenLayoutJourney(),
  loadingOnStepTwo: makeSelectLoadingOnStepTwo(),
  closableCreateJr: makeSelectClosableDrawerCreateJr(),
});

const mapDispatchToProps = dispatch => {
  return {
    init: params => {
      dispatch(init(`${params.moduleConfig.key}`, params));
    },
    resetDrawer: param => {
      dispatch(reset(`${MODULE_CONFIG.key}@@RESET_DRAWER`));
    },
    resetInitDetailDone: () => {
      dispatch(
        reset(`${MODULE_CONFIG_DETAIL.key}@@RESET_STT_INIT_DETAIL_DONE`),
      );
    },
    updateCreatingJourneyInfo: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CREATING_JOURNEY_INFO`, params),
      );
    },
    resetJourney: params => {
      dispatch(reset(`${params.moduleConfig.key}@@CREATE_JOURNEY`));
    },
    toggleLayoutJourney: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_LAYOUT_JOURNEY`, params),
      );
    },
    updateLoadingOnStepTwo: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@LOADING_ON_STEP_TWO`, params),
      );
    },
    dashboardSetOwnerIdFromData: params => {
      dispatch(dashboardSetOwnerIdFromData(params));
    },
    updateTrigger: params => {
      dispatch(
        updateValue(
          `${
            MODULE_CONFIG_CREATE_BLAST_CAMPAIGN.key
          }@@TRIGGER_DRAWER_JOURNEY@@`,
          params,
        ),
      );
    },
    fetchDataTable: params => {
      dispatch(getList(`${params.moduleConfig.key}`, params));
    },
    onChangeStoryName: value =>
      dispatch(updateValue(`${MODULE_CONFIG_CREATE.key}@@STORY_NAME@@`, value)),
    onChangePageTitle: value =>
      dispatch(updateValue(`${MODULE_CONFIG_CREATE.key}@@PAGE_TITLE@@`, value)),
    onChangeBlastStoryName: value =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG_CREATE_BLAST_CAMPAIGN.key}@@STORY_NAME@@`,
          value,
        ),
      ),
    onChangePageTitleBlast: value =>
      dispatch(
        updateValue(
          `${MODULE_CONFIG_CREATE_BLAST_CAMPAIGN.key}@@PAGE_TITLE@@`,
          value,
        ),
      ),
    resetCreatingJourneyInfo: () => {
      dispatch(reset(`${MODULE_CONFIG.key}@@CREATING_JOURNEY_INFO`));
    },
  };
};

DrawerCreatingJourney.propTypes = {
  isOpen: propsTypes.bool,
  toggle: propsTypes.func,
  channelId: propsTypes.oneOfType([propsTypes.string, propsTypes.number]),
  callback: propsTypes.func,
  drawerJourneyInfo: propsTypes.any,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  { forwardRef: true },
)(DrawerCreatingJourney);
