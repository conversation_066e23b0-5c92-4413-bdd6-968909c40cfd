.tippy-tooltip[data-animation='fade'][data-state='hidden'] {
  opacity: 0;
}
.tippy-iOS {
  cursor: pointer !important;
  -webkit-tap-highlight-color: transparent;
}
.tippy-popper {
  pointer-events: none;
  max-width: calc(100vw - 10px);
  transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
  transition-property: transform;
}
.tippy-tooltip {
  position: relative;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
  background-color: #425b76;
  transition-property: visibility, opacity, transform;
  outline: 0;
}
.tippy-tooltip[data-placement^='top'] {
  & > .tippy-arrow {
    border-width: 8px 8px 0;
    border-top-color: #425b76;
    margin: 0 3px;
    transform-origin: 50% 0;
    bottom: -7px;
  }
}
.tippy-tooltip[data-placement^='bottom'] {
  & > .tippy-arrow {
    border-width: 0 8px 8px;
    border-bottom-color: #425b76;
    margin: 0 3px;
    transform-origin: 50% 7px;
    top: -7px;
  }
}
.tippy-tooltip[data-placement^='left'] {
  & > .tippy-arrow {
    border-width: 8px 0 8px 8px;
    border-left-color: #425b76;
    margin: 3px 0;
    transform-origin: 0 50%;
    right: -7px;
  }
}
.tippy-tooltip[data-placement^='right'] {
  & > .tippy-arrow {
    border-width: 8px 8px 8px 0;
    border-right-color: #425b76;
    margin: 3px 0;
    transform-origin: 7px 50%;
    left: -7px;
  }
}
.tippy-tooltip[data-interactive][data-state='visible'] {
  pointer-events: auto;
}
.tippy-tooltip[data-inertia][data-state='visible'] {
  transition-timing-function: cubic-bezier(0.54, 1.5, 0.38, 1.11);
}
.tippy-arrow {
  position: absolute;
  border-color: transparent;
  border-style: solid;
}
.tippy-content {
  padding: 5px 9px;
  white-space: normal;
  text-align: initial;
  font-weight: 400;
  cursor: text;
  user-select: text;
  // text-transform: unset;
  .antsomi-label-tag {
    background-color: #fff !important;
    border-color: #fff !important;
    color: #0060be !important;
  }
}

.wrapper-tippy-image {
  .tippy-tooltip {
    background-color: #ffffff !important;
    border: 1px solid #cfd4e3 !important;
    border-bottom-color: #cfd4e3 !important;
  }
  .tippy-tooltip[data-placement^='bottom'] > .tippy-arrow {
    border-bottom-color: #cfd4e3 !important;
    top: -8px;
  }
  .tippy-tooltip[data-placement^='top'] > .tippy-arrow {
    border-top-color: #cfd4e3 !important;
    bottom: -8px;
  }
  .tippy-tooltip[data-placement^='left'] > .tippy-arrow {
    border-left-color: #cfd4e3;
    right: -8px;
  }
  .tippy-tooltip[data-placement^='right'] > .tippy-arrow {
    border-right-color: #cfd4e3;
    left: -8px;
  }
}

.content-tippy-image {
  background-color: #ffffff !important;
  border: 1px solid #cfd4e3 !important;
  border-bottom-color: #cfd4e3 !important;
  &.tippy-tooltip[data-placement^='bottom'] > .tippy-arrow {
    border-bottom-color: #cfd4e3 !important;
    top: -8px;
  }
  &.tippy-tooltip[data-placement^='top'] > .tippy-arrow {
    border-top-color: #cfd4e3 !important;
    bottom: -8px;
  }
  &.tippy-tooltip[data-placement^='left'] > .tippy-arrow {
    border-left-color: #cfd4e3;
    right: -8px;
  }
  &.tippy-tooltip[data-placement^='right'] > .tippy-arrow {
    border-right-color: #cfd4e3;
    left: -8px;
  }
}
.dropdown,
.dropup {
  position: relative;
}

.show > .dropdown-menu,
.dropdown-menu.show {
  display: block;
  z-index: 2000;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 120px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  // border: 1px solid #ccc;
  // border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  -webkit-box-shadow: 0px 3px 20px 0px rgba(0, 46, 89, 0.2);
  box-shadow: 0px 3px 20px 0px rgba(0, 46, 89, 0.2);
}

li.uiListItem.private-list__item button {
  white-space: nowrap;
}

/* .popover-dropdown .show {
  z-index: 1111;
} */
/* tooltip*/

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  //   'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
  //   'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;

  &.show {
    opacity: 0.9;
  }

  .arrow {
    position: absolute;
    display: block;
    width: 0.8rem;
    height: 0.4rem;

    &::before {
      position: absolute;
      content: '';
      border-color: transparent;
      border-style: solid;
    }
  }
}

.bs-tooltip-top,
.bs-tooltip-auto[x-placement^='top'] {
  padding: 0.4rem 0;
}

.bs-tooltip-top .arrow,
.bs-tooltip-auto[x-placement^='top'] .arrow {
  bottom: 0;
}

.bs-tooltip-top .arrow::before,
.bs-tooltip-auto[x-placement^='top'] .arrow::before {
  top: 0;
  right: 4px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #425b76;
}

.bs-tooltip-right,
.bs-tooltip-auto[x-placement^='right'] {
  padding: 0 0.4rem;
}

.bs-tooltip-right .arrow,
.bs-tooltip-auto[x-placement^='right'] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-right .arrow::before,
.bs-tooltip-auto[x-placement^='right'] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #425b76;
}

.bs-tooltip-bottom,
.bs-tooltip-auto[x-placement^='bottom'] {
  padding: 0.4rem 0;
}

.bs-tooltip-bottom .arrow,
.bs-tooltip-auto[x-placement^='bottom'] .arrow {
  top: 0;
}

.bs-tooltip-bottom .arrow::before,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #425b76;
}

.bs-tooltip-left,
.bs-tooltip-auto[x-placement^='left'] {
  padding: 0 0.4rem;
}

.bs-tooltip-left .arrow,
.bs-tooltip-auto[x-placement^='left'] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-left .arrow::before,
.bs-tooltip-auto[x-placement^='left'] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #425b76;
}

.tooltip-inner {
  max-width: 232px;
  padding: 10px 16px;
  color: #fff;
  font-size: 13px;
  text-align: left;
  background-color: #425b76;
  border-radius: 0.25rem;
}

/* popever*/

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;

  /* max-width: 276px; */
  /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; */
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;

  /* border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem; */
  border-radius: 3px;
  // box-shadow: 0 1px 24px 0 rgba(0, 0, 0, 0.08);
  // border: 1px solid #cbd6e2;
  border: 1px solid #e6e6e6;
  box-shadow: 0 0 0.5625rem 0 rgba(0, 0, 0, 0.09);
  .arrow {
    position: absolute;
    display: block;
    width: 1rem;
    height: 0.5rem;
    margin: 0 0.3rem;

    &::before,
    &::after {
      position: absolute;
      display: block;
      content: '';
      border-color: transparent;
      border-style: solid;
    }
  }
}

.popover-marign-0 {
  .bs-popover-top,
  .bs-popover-auto[x-placement^='top'] {
    margin-bottom: 0rem !important;
  }
  .bs-popover-right,
  .bs-popover-auto[x-placement^='right'] {
    margin-left: 0rem !important;
  }
  .bs-popover-left,
  .bs-popover-auto[x-placement^='left'] {
    margin-right: 0rem !important;
  }
  .bs-popover-bottom,
  .bs-popover-auto[x-placement^='bottom'] {
    margin-top: 0rem !important;
  }
}

.popover-marign--2rem {
  .bs-popover-top,
  .bs-popover-auto[x-placement^='top'] {
    margin-bottom: -2rem !important;
  }
  .bs-popover-right,
  .bs-popover-auto[x-placement^='right'] {
    margin-left: -2rem !important;
  }
  .bs-popover-left,
  .bs-popover-auto[x-placement^='left'] {
    margin-right: -2rem !important;
  }
  .bs-popover-bottom,
  .bs-popover-auto[x-placement^='bottom'] {
    margin-top: -2rem !important;
  }
}

.bs-popover-top .arrow,
.bs-popover-auto[x-placement^='top'] .arrow {
  bottom: calc((0.5rem + 1px) * -1);
}

.bs-popover-top .arrow::before,
.bs-popover-auto[x-placement^='top'] .arrow::before,
.bs-popover-top .arrow::after,
.bs-popover-auto[x-placement^='top'] .arrow::after {
  border-width: 0.5rem 0.5rem 0;
}

.bs-popover-top .arrow::before,
.bs-popover-auto[x-placement^='top'] .arrow::before {
  bottom: 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-top .arrow::after,
.bs-popover-auto[x-placement^='top'] .arrow::after {
  bottom: 1px;
  border-top-color: #fff;
}

.bs-popover-right,
.bs-popover-auto[x-placement^='right'] {
  margin-left: 0.5rem;
}

.bs-popover-right .arrow,
.bs-popover-auto[x-placement^='right'] .arrow {
  left: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-right .arrow::before,
.bs-popover-auto[x-placement^='right'] .arrow::before,
.bs-popover-right .arrow::after,
.bs-popover-auto[x-placement^='right'] .arrow::after {
  border-width: 0.5rem 0.5rem 0.5rem 0;
}

.bs-popover-right .arrow::before,
.bs-popover-auto[x-placement^='right'] .arrow::before {
  left: 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-right .arrow::after,
.bs-popover-auto[x-placement^='right'] .arrow::after {
  left: 1px;
  border-right-color: #fff;
}

.bs-popover-bottom,
.bs-popover-auto[x-placement^='bottom'] {
  margin-top: 0.5rem;
}

.bs-popover-bottom .arrow,
.bs-popover-auto[x-placement^='bottom'] .arrow {
  top: calc((0.5rem + 1px) * -1);
}

.bs-popover-bottom .arrow::before,
.bs-popover-auto[x-placement^='bottom'] .arrow::before,
.bs-popover-bottom .arrow::after,
.bs-popover-auto[x-placement^='bottom'] .arrow::after {
  border-width: 0 0.5rem 0.5rem 0.5rem;
}

.bs-popover-bottom .arrow::before,
.bs-popover-auto[x-placement^='bottom'] .arrow::before {
  top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-bottom .arrow::after,
.bs-popover-auto[x-placement^='bottom'] .arrow::after {
  top: 1px;
  border-bottom-color: #fff;
}

.bs-popover-bottom .popover-header::before,
.bs-popover-auto[x-placement^='bottom'] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: '';
  border-bottom: 1px solid #f7f7f7;
}

.bs-popover-left,
.bs-popover-auto[x-placement^='left'] {
  margin-right: 0.5rem;
}

.bs-popover-left .arrow,
.bs-popover-auto[x-placement^='left'] .arrow {
  right: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-left .arrow::before,
.bs-popover-auto[x-placement^='left'] .arrow::before,
.bs-popover-left .arrow::after,
.bs-popover-auto[x-placement^='left'] .arrow::after {
  border-width: 0.5rem 0 0.5rem 0.5rem;
}

.bs-popover-left .arrow::before,
.bs-popover-auto[x-placement^='left'] .arrow::before {
  right: 0;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-left .arrow::after,
.bs-popover-auto[x-placement^='left'] .arrow::after {
  right: 1px;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: inherit;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);

  &:empty {
    display: none;
  }
}

.popover-body {
  padding: 0.5rem 0.75rem;
  color: #212529;
}

.private-nav__wrapper {
  // margin-bottom: 20px;
  .private-nav__wrapper {
    margin-bottom: 0;
  }
}

.private-nav {
  padding-left: 0;
  list-style: none;
  &.private-nav--level-0 {
    margin-bottom: 0;
  }
  &.private-nav--level-1 {
    margin-bottom: 4px;
  }
}

.private-nav-item {
  display: flex;
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
  font-smoothing: auto;
  text-shadow: 0 0 1px transparent;
  font-size: 14px;
  // font-family: Avenir Next W02, Helvetica, Arial, sans-serif;
  font-weight: 400;
  color: #000000;
  left: -12px;
  line-height: 16px;
  padding: 0 12px;
  position: relative;
  text-decoration: none;
  width: calc(100% + 12px);
  .private-nav-item__label {
    flex-grow: 1;
  }
  &:hover {
    // color: #33475b;
    cursor: pointer;
    text-decoration: none;
  }
  &:focus {
    z-index: 1;
  }
  &.private-nav-item--has-icon-right .private-nav-item__icon-right {
    margin-left: 8px;
    margin-right: 0;
  }
  &.private-nav-item--has-icon-left .private-nav-item__icon-left {
    margin-right: 4px;
  }
}

.private-nav--level-1 .private-nav-item {
  padding-left: 24px;
}

.private-nav-item {
  // &.private-nav-item--active {
  //   border-radius: 3px;
  //   background-color: rgb(0, 94, 184);
  //   color: rgb(255, 255, 255);
  //   font-weight: 600;
  // }
  &.private-nav-item--select-tree-active {
    color: #33475b;
    font-weight: 600;
  }
  // &.private-nav-item--key-up--key-down--hover {
  //   background-color: rgb(0, 94, 184);
  //   color: rgb(255, 255, 255);
  // }
  &.private-nav-item--disabled {
    background-color: #eaf0f6 !important;
    border-color: #eaf0f6 !important;
    box-shadow: none !important;
    color: #b0c1d4 !important;
    cursor: not-allowed;
  }
  .private-tag--unenclosed {
    line-height: 16px;
  }
}

.private-nav-item__label {
  align-items: center;
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
}

.private-nav-item__count {
  margin-left: auto;
  padding-left: 8px;
}

.private-nav-item__height-transition {
  margin-left: -12px;
  padding-left: 12px;
  transition: height 0.2s ease, opacity 0.2s ease-in-out,
    transform 0.2s ease-in-out;
  &.private-height-transition--closed {
    opacity: 0;
    transform: translateY(-10px);
  }
  &.private-height-transition--animating {
    overflow: hidden;
  }
  &.private-height-transition--closed:not(.private-height-transition--animating) {
    display: none;
  }
}

.private-nav--expandable {
  .private-nav--level-1 .private-nav-item {
    font-size: 12px;
    padding-bottom: 8px;
    padding-top: 8px;
  }
  .private-nav-item__icon-right.private-nav-item__icon-right {
    color: #0091ae;
  }
  .private-nav-item--is-parent {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  // .private-nav-item--hovered,
  // .private-nav-item:hover {
  //   background-color: rgb(0, 94, 184);
  //   color: rgb(255, 255, 255);
  // }
}

.private-folder-nav {
  .private-nav {
    margin: 0;
    .private-nav-item--is-parent {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      padding: 8px 12px !important;
    }
    .private-folder-nav-item {
      left: 0;
      padding: 8px 10px;
      width: 100%;
    }
    .private-nav-item__icon-left {
      color: #0095da;
      margin-right: 4px;
      max-width: 15px;
    }
    // .private-folder-nav-item:hover {
    //   color: rgb(255, 255, 255);
    //   background-color: rgb(0, 94, 184);
    // }
  }
  .private-nav--level-1 .private-folder-nav-item {
    padding-left: 20px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .item-child {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }

  .item-child-2 {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .private-nav--level-1 .override-style-checkbox .MuiButtonBase-root {
    padding: 9px;
  }

  .item-child-2 .MuiButtonBase-root {
    padding: 2px 0 2px 9px;
    margin-right: 0 !important;
  }

  .override-style-checkbox .MuiButtonBase-root {
    padding: 2px 0 2px 9px;
    margin-right: 8px;
  }

  .private-nav--level-2 .private-folder-nav-item {
    padding-left: 40px;
  }
  .private-nav--level-3 .private-folder-nav-item {
    padding-left: 60px;
  }
  .private-nav--level-4 .private-folder-nav-item {
    padding-left: 80px;
  }
  .private-nav--level-5 .private-folder-nav-item {
    padding-left: 100px;
  }
  .private-nav--level-6 .private-folder-nav-item {
    padding-left: 120px;
  }
  .private-nav--level-7 .private-folder-nav-item {
    padding-left: 140px;
  }
  .private-nav--level-8 .private-folder-nav-item {
    padding-left: 160px;
  }
  .private-nav--level-9 .private-folder-nav-item {
    padding-left: 180px;
  }
  .private-nav--level-10 .private-folder-nav-item {
    padding-left: 200px;
  }
}

.private-link .MuiTypography-body1 {
  font-size: 0.875rem;

  max-width: 420px;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // display: -webkit-box;
  // -webkit-line-clamp: 1;
  // -webkit-box-orient: vertical;
}

.private-nav-item--only-parent {
  .MuiButtonBase-root {
    padding: 0 9px !important;
  }
}
