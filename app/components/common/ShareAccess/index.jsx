/* eslint-disable no-else-return */
/* eslint-disable no-shadow */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable no-undef */
/* eslint-disable no-case-declarations */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/button-has-type */
/* eslint-disable prettier/prettier */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
// library
import React, { useEffect, useState } from 'react';
// redux
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
// components
import { Popover } from '@material-ui/core';
// utils
import { cloneDeep } from 'lodash';
import { useImmer } from 'use-immer';
import { getDescriptionGeneralAccess, isValidEmail } from './utils';
import { generateKey } from '../../../utils/common';
// styled
import {
  Logo,
  StyledInputAccount,
  StyledBlock,
  WrapperShareAccess,
  StyledMenuItem,
  Item,
} from './styles';

// hooks
import useDebounce from '../../../hooks/useDebounce';
// selector
import {
  makeSelectDashboardMenuCodeActive,
  makeSelectDashboardModalSelectAccount,
  makeSelectUser,
} from '../../../modules/Dashboard/selector';
// services
import DashboardServices from '../../../services/Dashboard';
// constants
import { optionAccess, typeAccess } from './constants';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
} from '../../../utils/web/permission';
import ModalTransferOwnerAccount from './ModalTransferOwnerAccount';

function ShareAccess(props) {
  const {
    design = 'create',
    userInfo = {},
    accessInfo = {},
    callback = () => {},
    isHiddenTransferOwner = false,
    menuCodeActive,
    modalSelectAccount = {},
  } = props;

  const [state, setState] = useImmer({
    open: false,
    anchorEl: null,
    options: [],
    search: '',
    isInitDone: false,
    accountSuggestion: [],
    isShowSuggestion: false,
    activeId: '',
  });

  const { listAccess, general } = accessInfo;

  const debounceSearch = useDebounce(state.search, 500);
  const [isOpenModalTransfer, setIsOpenModalTransfer] = useState(false);

  useEffect(() => {
    // handle init access when create
    if (
      !listAccess.length &&
      !Object.keys(general).length &&
      design === 'create'
    ) {
      let accessInfo = {
        general: {},
        listAccess: [],
      };
      const hasRoleEditEverything = checkingRoleScope(
        menuCodeActive,
        APP_ACTION.CREATE,
        APP_ROLE_SCOPE.EVERYTHING,
      );
      const { cacheDataConfirm } = modalSelectAccount;
      if (hasRoleEditEverything && cacheDataConfirm) {
        const { email } = cacheDataConfirm;
        console.log({email})
        DashboardServices.data
          .getUserInfo({
            email,
          })
          .then(res => {
            if (res.data.length) {
              const { full_name, email, user_id, avatar } = res.data[0];
              const newAccess = {
                id: generateKey(),
                name: full_name,
                email,
                userId: user_id,
                avatar,
                accessType: { label: 'Owner', value: 'owner' },
                role: 1,
              };
              accessInfo = {
                general: typeAccess[0],
                listAccess: [newAccess],
              };
              updateShareAccess(accessInfo);
            }
          });
      } else {
        const { fullName, email, userId, networkId } = userInfo;
        const { avatar } = APP_CACHE_PARAMS;
        const currentAccess = {
          id: generateKey(),
          name: fullName,
          email,
          userId,
          portalId: networkId,
          avatar,
          accessType: { label: 'Owner', value: 'owner' },
          role: 1,
        };
        accessInfo = {
          general: typeAccess[0],
          listAccess: [currentAccess],
        };
        updateShareAccess(accessInfo);
      }
    }
  }, [listAccess.length, design]);
  useEffect(() => {
    if (isValidEmail(debounceSearch)) {
      DashboardServices.data
        .getUserInfo({
          email: debounceSearch,
        })
        .then(res => {
          if (res.data.length) {
            setState(draft => {
              draft.accountSuggestion = res.data;
              draft.open = true;
              draft.isShowSuggestion = true;
            });
          }
        });
    }
  }, [debounceSearch]);

  const handleClose = () => {
    setState(draft => {
      draft.anchorEl = null;
      draft.open = false;
      draft.isShowSuggestion = false;
      draft.accountSuggestion = [];
      draft.activeId = '';
    });
  };

  const handleOpenPopup = (event, type, id = '') => {
    if (event) {
      switch (type) {
        case 'GENERAL_ACCESS':
          setState(draft => {
            draft.open = true;
            draft.anchorEl = event;
            draft.options = typeAccess;
          });
          break;

        case 'TYPE_ACCESS':
          const { userId } = userInfo;
          const accessOwner = listAccess.find(access => access.role === 1);
          const isOwner = +accessOwner.userId === +userId;

          let options = Object.values(optionAccess).filter(
            option => option.value !== 'owner',
          );
          if (!id) {
            options = options.filter(
              option =>
                option.value !== 'remove' && option.value !== 'transfer',
            );
          } else if (!isOwner || isHiddenTransferOwner) {
            // user là editor thì ẩn option transfer || truyền props ẩn option này
            options = options.filter(option => option.value !== 'transfer');

            const index = options.findIndex(
              option => option.value === 'remove',
            );
            options[index].isDivide = true;
          }

          if (options.some(option => option.value === 'transfer')) {
            const index = options.findIndex(
              option => option.value === 'remove',
            );
            options[index].isDivide = false;
          }

          setState(draft => {
            draft.open = true;
            draft.anchorEl = event;
            draft.options = options;
            draft.activeId = id;
          });
          break;

        default:
          break;
      }
    }
  };

  const updateShareAccess = data => {
    const newAccessInfo = cloneDeep(accessInfo);

    if (Object.keys(data).length) {
      Object.keys(data).forEach(key => {
        newAccessInfo[key] = data[key];
      });
    }
    
    callback('UPDATE_SHARE_ACCESS', { accessInfo: newAccessInfo,type: data?.type || '' });
  };

  const onSelectOption = option => {
    const { type } = option;

    switch (type) {
      case 'GENERAL_ACCESS':
        updateShareAccess({ general: option,type: 'onChange' });
        break;
      case 'TYPE_ACCESS':
        if (state.activeId) {
          const index = listAccess.findIndex(
            access => access.id === state.activeId,
          );
          if (index !== -1) {
            const newListAccess = cloneDeep(listAccess);
            if (option.value === 'remove') {
              newListAccess.splice(index, 1);
            } else if (option.value === 'transfer') {
              setIsOpenModalTransfer(true);
            } else {
              newListAccess[index] = {
                ...listAccess[index],
                accessType: option,
              };
            }
            updateShareAccess({
              listAccess: newListAccess,
              type: 'onChange'
            });
          }
        } else {
          updateShareAccess({
            general: {
              ...general,
              accessType: option,
            },
            type: 'onChange'
          });
        }
        break;
      default:
        break;
    }

    setState(draft => {
      draft.open = false;
    });
  };

  const callbackComponent = (type, data) => {
    switch (type) {
      case 'ON_CONFIRM_TRANSFER_OWNER':
        if (state.activeId) {
          const index = listAccess.findIndex(
            access => access.id === state.activeId,
          );
          if (index !== -1) {
            const newListAccess = cloneDeep(listAccess);
            newListAccess[0] = {
              ...listAccess[0],
              role: 2,
              accessType: optionAccess.EDITOR,
            };
            newListAccess[index] = {
              ...listAccess[index],
              role: 1,
              accessType: optionAccess.OWNER,
            };

            newListAccess.sort((access1, access2) => {
              if (access1.role === 1) {
                return -1;
              } else if (access2.role === 1) {
                return 1;
              } else {
                return 0;
              }
            });
            updateShareAccess({
              listAccess: newListAccess,
              type: 'onChange'
            });
            setIsOpenModalTransfer(false);
          }
        }
        break;

      default:
        break;
    }
  };

  const onChangeSearchAccount = (value, target) => {
    setState(draft => {
      draft.search = value;
      draft.anchorEl = target;
    });
  };

  const addAccountAccess = () => {
    if (state.accountSuggestion.length) {
      const { full_name, email, user_id, avatar } = state.accountSuggestion[0];

      if (listAccess.every(access => access.email !== email)) {
        const newAccess = {
          id: generateKey(),
          name: full_name,
          email,
          userId: user_id,
          avatar,
          accessType: { label: 'Viewer', value: 'viewer' },
        };

        const newListAccess = cloneDeep(listAccess);
        newListAccess.push(newAccess);

        updateShareAccess({
          listAccess: newListAccess,
          type: 'onChange'
        });
      }

      setState(draft => {
        draft.search = '';
      });
      handleClose();
    }
  };
  return (
    <WrapperShareAccess>
      <StyledInputAccount>
        <input
          onChange={e => onChangeSearchAccount(e.target.value, e.target)}
          className="search-account"
          placeholder={props.placeholder || 'Add people'}
          value={state.search}
        />
      </StyledInputAccount>

      <StyledBlock>
        <div className="title-access">People with access</div>
        <div className="body-access">
          {listAccess.map(access => {
            const isOwner = access.accessType.value === 'owner';        
            const email = `${access.email}`.replace(
              /(.remove#1)/g,
              '',
            );   
            return (
              <div className="access-container">
                <div className="logo">
                  <Logo src={access.avatar} alt={access.fullName} />
                </div>
                <div className="info">
                  <span className="name">
                    {access.status && parseInt(access.status) === 80 && (
                      <span className="name-delete">[Deleted account]</span>
                    )}{' '}
                    {access.name}
                  </span>
                  <span className="email">
                    {email}
                  </span>
                </div>
                <div className="role">
                  {isOwner ? (
                    <span className="role-name">{access.accessType.label}</span>
                  ) : (
                    <button
                      onClick={event =>
                        handleOpenPopup(event.target, 'TYPE_ACCESS', access.id)
                      }
                      className="switch-role"
                    >
                      {access.accessType.label}
                      <span className="icon-xlab-arrow-down" />
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </StyledBlock>

      <StyledBlock>
        <div className="title-access">General access</div>
        <div className="body-access">
          <div className="access-container">
            <div className="logo">
              <span className={general.icon} />
            </div>
            <div className="info">
              <button
                onClick={event =>
                  handleOpenPopup(event.target, 'GENERAL_ACCESS')
                }
                className="switch-type-access"
              >
                {general.label}
                <span className="icon-xlab-arrow-down" />
              </button>
              <span className="email">
                {getDescriptionGeneralAccess(general)}
              </span>
            </div>
            {general.accessType && (
              <div className="role">
                <button
                  onClick={event =>
                    handleOpenPopup(event.target, 'TYPE_ACCESS')
                  }
                  className="switch-role"
                >
                  {general.accessType.label}
                  <span className="icon-xlab-arrow-down" />
                </button>
              </div>
            )}
          </div>
        </div>
      </StyledBlock>

      <Popover
        // id={id}
        open={state.open}
        anchorEl={state.anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <StyledMenuItem>
          {state.isShowSuggestion ? (
            <StyledBlock>
              <div
                onClick={addAccountAccess}
                className="body-access suggestion-account"
              >
                <div className="access-container">
                  <div className="logo">
                    <Logo
                      src={state.accountSuggestion[0].avatar}
                      alt={state.accountSuggestion[0].full_name}
                    />
                  </div>
                  <div className="info">
                    <span className="name">
                      {state.accountSuggestion[0].full_name}
                    </span>
                    <span className="email">
                      {state.accountSuggestion[0].email}
                    </span>
                  </div>
                </div>
              </div>
            </StyledBlock>
          ) : (
            state.options.map(option => (
              <Item
                onClick={() => onSelectOption(option)}
                isDivide={option.isDivide || false}
              >
                {option.label}
              </Item>
            ))
          )}
        </StyledMenuItem>
      </Popover>

      <ModalTransferOwnerAccount
        isOpen={isOpenModalTransfer}
        callback={callbackComponent}
        toggle={() => setIsOpenModalTransfer(!isOpenModalTransfer)}
      />
    </WrapperShareAccess>
  );
}
const mapStateToProps = createStructuredSelector({
  dashboard: state => state.get('dashboard'),
  userInfo: state => (state.get('dashboard') ? makeSelectUser(state) : null),
  menuCodeActive: makeSelectDashboardMenuCodeActive(),
  modalSelectAccount: makeSelectDashboardModalSelectAccount(),
});

export default connect(
  mapStateToProps,
  null,
)(ShareAccess);
