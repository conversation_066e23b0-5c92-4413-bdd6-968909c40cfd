/* eslint-disable react/prop-types */
import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import * as Styled from '../styled';
import Grid from '@material-ui/core/Grid';

export const useStyles = makeStyles(() => ({
  root: {
    flexGrow: 1,
    backgroundColor: 'rgb(250, 250, 250)',
    marginBottom: '1.188rem',
    fontSize: '0.75rem',
    position: 'relative',
    maxWidth: '68.75rem',
  },
  container: {
    background: '#fff',
    borderRadius: '10px',
    overflow: 'hidden',
    borderLeft: '4px solid rgb(15 93 182)',
    borderRight: '1px solid #d4d4d4',
    borderTop: '1px solid #d4d4d4',
    borderBottom: '1px solid #d4d4d4',

    position: 'relative',
  },
  padding: {
    padding: '10px 20px',
    // alignItems: 'center',
  },
  paddingLeft: {
    paddingLeft: '20px',
    position: 'relative',
  },
  textRight: {
    textAlign: 'right',
  },
  refineTitle: {
    textAlign: 'right',
    position: 'relative',
    top: '16px',
    transform: 'translateY(-50%)',
  },
  commonDisplayTextRight: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    textAlign: 'right',
  },
  flexDirectionColumn: {
    flexDirection: 'column',
  },
  containerRefine: {
    marginBottom: '19px',
    alignItems: 'start',
  },
  positionRelative: {
    position: 'relative',
  },
  displayFlexWrap: {
    display: 'flex',
    flexWrap: 'inherit',
  },
}));

const ContainerCondition = props => {
  const classes = useStyles();

  return (
    <Styled.ContainerCondition
      className={`${classes.root} container-condition-perform-event`}
    >
      <Grid container className={classes.container}>
        {props.children}
      </Grid>
    </Styled.ContainerCondition>
  );
};

export default ContainerCondition;
