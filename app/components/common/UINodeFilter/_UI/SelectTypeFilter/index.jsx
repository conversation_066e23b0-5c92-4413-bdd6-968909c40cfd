/* eslint-disable prefer-destructuring */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import { Button, Flex, Spin } from '@antscorp/antsomi-ui';
import { DeleteRemoveTrashIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { isEmpty } from 'lodash';
import { Grid } from '@material-ui/core';

// Components
import { FilterItem } from './Item';

// Styled
import { BlockContentWrapper, LabelTitle } from '../../styled';
import { Title, WrapperHeader } from './styled';

// Constants
import {
  DATA_OPTIONS_FILTER,
  DATA_OPTIONS_FILTER_CUSTOMER_NO_EVENT_ATTR,
  DATA_OPTIONS_FILTER_NO_CUSTOMER,
  DATA_OPTIONS_FILTER_NO_EVENT_ATTR,
} from '../../utils';

import { safeParse } from '../../../../../utils/common';

const initTypeDelay = {
  options: DATA_OPTIONS_FILTER,
  value: {},
  isLoading: true,
};

const SelectTypeFilter = props => {
  const {
    initData,
    isActionBase,
    itemTypeId,
    isViewMode,
    channelActive,
    triggerTargetAudienceId,
  } = props;
  const [state, setState] = useImmer(initTypeDelay);

  const onChangeTypeFilter = data => {
    setState(draft => {
      draft.value = data;
    });

    props.onChange('filterType', data.value);
  };

  useEffect(() => {
    if (Object.keys(safeParse(initData, {})).length > 0) {
      setState(draft => {
        draft.value = initData;
      });
    }
    setState(draft => {
      draft.isLoading = false;
    });

    return () => {
      setState(() => initTypeDelay);
    };
  }, [props.componentId, initData]);

  const onRemoveFilter = () => {
    props.onChange('', state.value?.value);
  };

  const listOptions = useMemo(() => {
    let options = [];
    if (isActionBase) {
      options = state.options;
    }

    if (
      isActionBase &&
      Number(channelActive.value) === 2 &&
      Number(triggerTargetAudienceId) === -1007
    ) {
      options = DATA_OPTIONS_FILTER_NO_CUSTOMER;
    }

    if (!isActionBase && +itemTypeId === -1003) {
      options = DATA_OPTIONS_FILTER_CUSTOMER_NO_EVENT_ATTR;
    }

    if (!isActionBase && +itemTypeId === -1007) {
      options = DATA_OPTIONS_FILTER_NO_EVENT_ATTR;
    }

    return options;
  }, [
    isActionBase,
    itemTypeId,
    state.options,
    channelActive.value,
    triggerTargetAudienceId,
  ]);

  if (state.isLoading) {
    return <Spin spinning />;
  }

  return (
    <BlockContentWrapper alignItems="start">
      <LabelTitle>Target to</LabelTitle>

      {isEmpty(state.value) ? (
        <Flex gap={20}>
          {listOptions.map(option => {
            return (
              <FilterItem
                key={option.value}
                item={option}
                onSelect={onChangeTypeFilter}
              />
            );
          })}
        </Flex>
      ) : (
        <Grid container>
          <Grid item sm={12} lg={10}>
            <WrapperHeader>
              <Title> {state.value.label}</Title>
              {!isViewMode && (
                <Button
                  type="text"
                  icon={<DeleteRemoveTrashIcon size={20} />}
                  onClick={onRemoveFilter}
                >
                  Remove
                </Button>
              )}
            </WrapperHeader>
          </Grid>
        </Grid>
      )}
    </BlockContentWrapper>
  );
};

SelectTypeFilter.defaultProps = {
  onChange: () => {},
  initData: {},
};

export default SelectTypeFilter;
