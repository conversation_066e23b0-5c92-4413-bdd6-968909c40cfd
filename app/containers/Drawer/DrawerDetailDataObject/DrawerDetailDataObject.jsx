/* eslint-disable prettier/prettier */
// Libraries
import {
  Button,
  DRAWER_DETAIL_DIMENSION,
  DrawerDetail,
  Icon,
} from '@antscorp/antsomi-ui';
import { useHistory, useParams } from 'react-router-dom';
import React, { useEffect, useMemo, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import propsTypes from 'prop-types';
import { connect } from 'react-redux';
import { isEmpty } from 'lodash';

// Components
import DetailDOModule from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/indexV2';
import DetailAttributeModule from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Detail/Loadable';
import DrawerCreateDOAttribute from '../DrawerCreateDOAttribute';
import DrawerDetailCollection from '../DrawerDetailCollection/DrawerDetailCollection';
import DrawerCreateCollection from '../DrawerCreateCollection';
import EditableContent from '../../../components/common/EditableContent';
import UIHelmet from '../../../components/Templates/LayoutContent/Helmet';

// Hooks
import APP from '../../../appConfig';
import { getPortalId } from '../../../utils/web/cookie';
import { getList, updateValue } from '../../../redux/actions';

// Styled
import {
  BoxHeaderLeft,
  BoxHeaderRight,
  WrapperBody,
  WrapperHeader,
} from './styled';

// Selectors
import {
  makeSelectIsOpenDrawerCreateAttr,
  makeSelectIsOpenDrawerCreateCollection,
  makeSelectIsOpenDrawerDetailCollection,
  makeSelectIsOpenDrawerDetailExplore,
  makeSelectIsOpenSubDrawer,
  makeSelectStatusRenameBO,
  makeSelectStatusRenameBOAttr,
} from '../../../modules/Dashboard/ApiHub/BusinessObject/Main/selectors';
import { makeSelectActiveRow } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/selectors';
import { makeSelectIsOpenDrawerCollectionComputation } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/List/selectors';
import { makeSelectIsOpenDrawerAttributeHistory } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/List/selectors';
import { makeSelectVersionDetail } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Collections/Detail/Detail/selectors';
import {
  makeSelectDetailActiveRow,
  makeSelectDetailDomainMain,
} from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/selectors';
import { makeSelectDomainMain } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Detail/selectors';

// Constants
import { MODULE_CONFIG } from '../../../modules/Dashboard/ApiHub/BusinessObject/Main/config';
import { MODULE_CONFIG as MODULE_CONFIG_BO } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/config';
import { MODULE_CONFIG as MODULE_CONFIG_BO_LIST } from '../../../modules/Dashboard/ApiHub/BusinessObject/List/config';
import { MODULE_CONFIG as MODULE_CONFIG_BO_LIST_ATTR } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/List/config';
import {
  DEFAULT_MENU_ITEMS,
  DETAIL_BO_DRAWER,
  HEADER_BUTTON_SAVE_ID,
  HEADER_RIGHT_ID,
  TAB,
} from './constants';
import { MODULE_CONFIG as MODULE_CONFIG_UPDATE_ATTRIBUTE } from 'modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Detail/config';
import { DRAWER_NAME_CACHE } from '../../../utils/constants';

// Services
import BusinessObject from 'services/BusinessObject';

// Hooks
import { useUpdateObjectName } from '../../../hooks/useUpdateObjectName';
import { makeSelectSourceCommonMain } from '../../../modules/Dashboard/ApiHub/BusinessObject/selectors';
import { hiddenBODetail } from '../../../modules/Dashboard/ApiHub/BusinessObject/Detail/utils';
import { ItemTypeNameSegmentIds } from '../../../modules/Dashboard/ApiHub/BusinessObject/utils';

export const DrawerDetailDataObject = props => {
  const [isOpen, toggleOpenDrawer] = useState(false);

  const history = useHistory();

  const { tab } = useParams();

  const {
    toggleIsOpenDrawerCreateAttr,
    changeDrawerDOInfo,
    mainUpdateAttribute,
    activeRowDetailBO,
    toggleOpenDrawerDetailCollection,
    dispatchUpdateName,
    fetchData,
    dispatchUpdateBOAttrName,
    updateStatusRenameBO,
    isRenameBOSuccess,
    isRenameBOSuccessAttr,
    updateStatusRenameBOAttr,
    fetchListDataAttr,
    main,
    mainBO,
  } = props;

  const { activeRow } = mainUpdateAttribute;
  const { checkPermision } = main;

  const [updatedMenuItems, setUpdatedMenuItems] = useState(DEFAULT_MENU_ITEMS);

  const searchParams = new URLSearchParams(window.location.search);
  const [browserTitle, setBrowserTitle] = useState('');
  const [browserTitleAttr, setBrowserTitleAttr] = useState('');

  const {
    tabSearchParams,
    itemTypeId,
    collectionId,
    ui,
    subTab,
    createCopy,
    itemPropertyName,
  } = useMemo(() => {
    return {
      tabSearchParams: searchParams.get('tab'),
      itemTypeId: searchParams.get('itemTypeId'),
      collectionId: searchParams.get('id'),
      ui: searchParams.get('ui'),
      subTab: searchParams.get('subTab'),
      createCopy: searchParams.get('createCopy'),
      itemPropertyName: searchParams.get('itemPropertyName'),
    };
  }, [searchParams]);

  const tabsHiddenByItemTypeId = {
    [TAB.DATA_TABLE.key]: [-1011, -1010, -1009, -1021],
    [TAB.COLLECTION.key]: [-1003, -100, -1007, -1011, -1010, -1009],
  };

  useEffect(() => {
    if (ui === DETAIL_BO_DRAWER) {
      toggleOpenDrawer(true);
    } else {
      toggleOpenDrawer(false);
    }
  }, [ui]);

  useEffect(() => {
    if (collectionId && subTab) {
      toggleOpenDrawerDetailCollection(true);
    } else {
      toggleOpenDrawerDetailCollection(false);
    }

    if (
      (tabSearchParams === TAB.ATTRIBUTE.key ||
        tabSearchParams === TAB.GROUP.key) &&
      itemPropertyName
    ) {
      // return listing attribute if itemAttribute is segment_ids
      if (itemPropertyName === ItemTypeNameSegmentIds) {
        changeDrawerDOInfo({
          isOpenSubDrawer: false,
        });
        searchParams.delete('itemPropertyName');
        history.push({ search: searchParams.toString() });
      } else {
        changeDrawerDOInfo({
          isOpenSubDrawer: true,
        });
      }
    } else {
      changeDrawerDOInfo({
        isOpenSubDrawer: false,
      });
    }

    if (createCopy) {
      toggleIsOpenDrawerCreateAttr(true);
    }
  }, [collectionId, itemPropertyName, subTab, tabSearchParams]);

  const { isEdit = true, isExist = true } = checkPermision;

  useEffect(() => {
    let newMenuItems = [...DEFAULT_MENU_ITEMS];

    if (
      !tabsHiddenByItemTypeId[TAB.DATA_TABLE.key].includes(Number(itemTypeId))
    ) {
      newMenuItems.splice(1, 0, {
        icon: <Icon type="icon-ants-table-vertical" />,
        label: TAB.DATA_TABLE.label,
        key: TAB.DATA_TABLE.key,
      });
    }

    if (
      !tabsHiddenByItemTypeId[TAB.COLLECTION.key].includes(Number(itemTypeId))
    ) {
      newMenuItems.splice(3, 0, {
        icon: <Icon type="icon-ants-plus-paginate" />,
        label: TAB.COLLECTION.label,
        key: TAB.COLLECTION.key,
      });
    }

    if (!isEdit) {
      newMenuItems.shift();
    }

    if (hiddenBODetail(mainBO.listBO, itemTypeId)) {
      newMenuItems = [];
    }

    // update tab search params
    const existingTab = newMenuItems.find(item => item.key === tabSearchParams);

    if (isOpen && itemTypeId && !existingTab) {
      searchParams.set('tab', 'attributes');
      history.push({ search: searchParams.toString() });
    }

    setUpdatedMenuItems(newMenuItems);
  }, [isOpen, itemTypeId, checkPermision, mainBO.listBO]);

  useEffect(() => {
    setBrowserTitle(activeRowDetailBO.translate_label);
  }, [activeRowDetailBO.translate_label]);

  useEffect(() => {
    setBrowserTitleAttr(activeRow.translate_label);
  }, [activeRow.translate_label]);

  const onCloseDrawer = () => {
    if (isRenameBOSuccess) {
      updateStatusRenameBO(false);
      fetchData();
    }

    history.push(`${APP.PREFIX}/${getPortalId()}/api-hub/objects/${tab}`);

    toggleOpenDrawer(false);
    resetErrorBOName();
  };

  const onCloseSubDrawer = () => {
    if (isRenameBOSuccessAttr) {
      updateStatusRenameBOAttr(false);
      fetchListDataAttr();
    }
    searchParams.delete('itemPropertyName');

    changeDrawerDOInfo({
      isOpenSubDrawer: false,
    });

    history.push({ search: searchParams.toString() });
  };

  const {
    isLoading: isLoadingBOName,
    isError: isErrorBOName,
    error: errorBOName,
    updateName: updateBOName,
    isSuccess: isSuccessBOName,
    reset: resetErrorBOName,
  } = useUpdateObjectName({
    serviceFunc: BusinessObject.businessObject.updateName,
    options: {},
  });

  const {
    isLoading: isLoadingBOAttrName,
    error: errorBOAttrName,
    updateName: updateBOAttrName,
    isSuccess: isSuccessBOAttrName,
  } = useUpdateObjectName({
    serviceFunc: BusinessObject.BOAttribute.updateName,
    options: {},
  });

  useEffect(() => {
    if (isSuccessBOName) {
      updateStatusRenameBO(true);
      setBrowserTitle(activeRowDetailBO.item_type_display);
    }
  }, [isSuccessBOName]);

  useEffect(() => {
    if (isSuccessBOAttrName) {
      updateStatusRenameBOAttr(true);
      setBrowserTitleAttr(activeRow.item_property_display);
    }
  }, [isSuccessBOAttrName]);

  const onChangeBOName = value => {
    dispatchUpdateName({
      name: value,
    });
  };

  const onChangeBOAttrName = value => {
    dispatchUpdateBOAttrName(value);
  };

  const onBlurBOName = () => {
    if (isEmpty(activeRowDetailBO.item_type_display)) return;

    updateBOName({
      objectId: activeRowDetailBO.itemTypeId,
      data: {
        itemTypeDisplay: activeRowDetailBO.item_type_display.trim(),
      },
    });
  };

  const onBlurBOAttrName = () => {
    if (isEmpty(activeRow.item_property_display.trim())) return;

    updateBOAttrName({
      itemPropertyName: activeRow.item_property_name,
      data: {
        itemPropertyDisplay: activeRow.item_property_display.trim(),
        itemTypeId: activeRow.item_type_id,
      },
    });
  };

  const renderContentDrawerSubLayer = () => {
    switch (tabSearchParams) {
      case TAB.GROUP.key:
      case TAB.ATTRIBUTE.key:
        return <DetailAttributeModule design="update" isUIV2 />;

      default:
        return null;
    }
  };

  const onChangeTabDrawerMainLayer = item => {
    if (item.key === tab) return;

    searchParams.set('tab', item.key);
    searchParams.delete('subTab');
    searchParams.delete('segmentId');
    searchParams.delete('versionId');

    history.replace({ search: searchParams.toString() });
  };

  return (
    <>
      <DrawerDetail
        name={DRAWER_NAME_CACHE.DO_DETAIL}
        open={isOpen}
        menuProps={{
          items: isEmpty(main.activeRow) ? [] : updatedMenuItems,
          selectedKeys: [tabSearchParams],
          onClick: item => onChangeTabDrawerMainLayer(item),
        }}
        onClose={onCloseDrawer}
        destroyOnClose
        fullScreen={
          props.isOpenSubDrawer ||
          props.isOpenDrawerCreateAttr ||
          props.isOpenDrawerCollectionComputation ||
          props.isOpenDrawerAttributeHistory ||
          props.isOpenDrawerCreateCollection ||
          props.isOpenDrawerDetailCollection ||
          props.isOpenDrawerExplore
        }
        headerProps={{
          showBorderBottom: true,
          height: '50px',
          style: {
            padding: '0 15px',
          },
          children: (
            <>
              {!isEmpty(activeRowDetailBO) &&
                !hiddenBODetail(mainBO.listBO, itemTypeId) && (
                  <WrapperHeader>
                    <BoxHeaderLeft>
                      <EditableContent
                        defaultValue={activeRowDetailBO.item_type_display}
                        required
                        onChange={name => onChangeBOName(name)}
                        onBlur={onBlurBOName}
                        error={errorBOName}
                        isLoading={isLoadingBOName}
                        isError={isErrorBOName}
                        readonly={!isEdit}
                      />
                    </BoxHeaderLeft>
                    <div id={HEADER_RIGHT_ID} />
                  </WrapperHeader>
                )}
            </>
          ),
        }}
      >
        <UIHelmet title={browserTitle} />
        <WrapperBody>
          <DetailDOModule />
        </WrapperBody>
      </DrawerDetail>
      <DrawerDetail
        open={props.isOpenSubDrawer}
        minWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
        maxWidth={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
        key="layer-2"
        destroyOnClose
        menuProps={{
          show: false,
          // items: isEmpty(activeRow)
          //   ? []
          //   : [
          //       {
          //         icon: <Icon type="icon-ants-pencil" />,
          //         label: TAB.SETTINGS.label,
          //         key: TAB.SETTINGS.key,
          //       },
          //     ],
          // selectedKeys: [TAB.SETTINGS.key],
          showExpandButton: false,
        }}
        onClose={onCloseSubDrawer}
        headerProps={{
          showBorderBottom: true,
          height: '50px',
          style: {
            padding: '0 15px',
          },
          children: (
            <>
              {!isEmpty(activeRow) && (
                <WrapperHeader>
                  <BoxHeaderLeft>
                    <EditableContent
                      className="input-name"
                      defaultValue={activeRow.item_property_display}
                      onChange={value => onChangeBOAttrName(value)}
                      onBlur={onBlurBOAttrName}
                      error={errorBOAttrName}
                      isLoading={isLoadingBOAttrName}
                      required
                      readonly={!isEdit}
                    />
                  </BoxHeaderLeft>
                  <BoxHeaderRight>
                    <Button onClick={onCloseSubDrawer}>Cancel</Button>
                    <div id={HEADER_BUTTON_SAVE_ID} />
                  </BoxHeaderRight>
                </WrapperHeader>
              )}
            </>
          ),
        }}
      >
        <UIHelmet title={browserTitleAttr} />

        <WrapperBody>{renderContentDrawerSubLayer()}</WrapperBody>
      </DrawerDetail>

      <DrawerCreateDOAttribute
        isOpenDrawerCreateAttr={props.isOpenDrawerCreateAttr}
        toggleIsOpenDrawerCreateAttr={props.toggleIsOpenDrawerCreateAttr}
      />
      <DrawerDetailCollection />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  isOpenSubDrawer: makeSelectIsOpenSubDrawer(),
  isOpenDrawerCreateAttr: makeSelectIsOpenDrawerCreateAttr(),
  activeRowCollection: makeSelectActiveRow(),
  versionDetail: makeSelectVersionDetail(),
  isOpenDrawerCollectionComputation: makeSelectIsOpenDrawerCollectionComputation(),
  isOpenDrawerAttributeHistory: makeSelectIsOpenDrawerAttributeHistory(),
  isOpenDrawerCreateCollection: makeSelectIsOpenDrawerCreateCollection(),
  isOpenDrawerDetailCollection: makeSelectIsOpenDrawerDetailCollection(),
  mainUpdateAttribute: makeSelectDomainMain(),
  activeRowDetailBO: makeSelectDetailActiveRow(),
  isRenameBOSuccess: makeSelectStatusRenameBO(),
  isRenameBOSuccessAttr: makeSelectStatusRenameBOAttr(),
  isOpenDrawerExplore: makeSelectIsOpenDrawerDetailExplore(),
  main: makeSelectDetailDomainMain(),
  mainBO: makeSelectSourceCommonMain(),
});

const mapDispatchToProps = dispatch => {
  return {
    changeDrawerDOInfo: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@CHANGE_DRAWER_DO_INFO@@`, params),
      );
    },
    toggleOpenDrawerDetailCollection: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_IS_OPEN_DRAWER_DETAIL_COLLECTION@@`,
          params,
        ),
      );
    },
    toggleIsOpenDrawerCreateAttr: params => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_IS_OPEN_DRAWER_CREATE_ATTR@@`,
          params,
        ),
      );
    },
    dispatchUpdateName: params => {
      dispatch(updateValue(`${MODULE_CONFIG_BO.key}@@UPDATE_NAME`, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG_BO_LIST.key, params));
    },
    fetchListDataAttr: params => {
      dispatch(getList(MODULE_CONFIG_BO_LIST_ATTR.key, params));
    },
    dispatchUpdateBOAttrName: name => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG_UPDATE_ATTRIBUTE.key}@@UPDATE_ATTRIBUTE_NAME`,
          name,
        ),
      );
    },
    updateStatusRenameBO: status => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_STATUS_RENAME_BO@@`, status),
      );
    },
    updateStatusRenameBOAttr: status => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@UPDATE_STATUS_RENAME_BO_ATTR@@`,
          status,
        ),
      );
    },
  };
};

DrawerDetailDataObject.propTypes = {
  isOpenSubDrawer: propsTypes.bool,
  isOpenDrawerCreateAttr: propsTypes.bool,
  isOpenDrawerCollectionComputation: propsTypes.bool,
  isOpenDrawerAttributeHistory: propsTypes.bool,
  isOpenDrawerCreateCollection: propsTypes.bool,
  isOpenDrawerDetailCollection: propsTypes.bool,
  isRenameBOSuccess: propsTypes.bool,
  isRenameBOSuccessAttr: propsTypes.bool,
  isOpenDrawerExplore: propsTypes.bool,
  mainUpdateAttribute: propsTypes.object,
  activeRowDetailBO: propsTypes.object,
  fetchData: propsTypes.func,
  changeDrawerDOInfo: propsTypes.func,
  dispatchUpdateName: propsTypes.func,
  updateStatusRenameBO: propsTypes.func,
  dispatchUpdateBOAttrName: propsTypes.func,
  toggleIsOpenDrawerCreateAttr: propsTypes.func,
  toggleOpenDrawerDetailCollection: propsTypes.func,
  updateStatusRenameBOAttr: propsTypes.func,
  fetchListDataAttr: propsTypes.func,
  main: propsTypes.object,
  mainBO: propsTypes.object,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(DrawerDetailDataObject);
