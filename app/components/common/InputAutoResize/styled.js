import styled from 'styled-components';
import { makeStyles } from '@material-ui/core/styles';
import { InfoOutlined } from '@material-ui/icons';

export const useStyles = makeStyles(() => ({
  fontInput: {
    fontSize: 16,
    color: 'rgb(0, 94, 184)',
    fontWeight: 'bold',
  },
}));

export const WrapperInput = styled.div`
  width: fit-content;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 30px;
  gap: 15px;
  max-width: 500px;

  .MuiFormControl-root {
    ${({ isError }) => (isError ? `min-width: 340px;` : `min-width: 20px;`)}

  }

  label + .MuiInput-formControl {
    margin-top: 12px;
    /* ${({ isError }) => !isError && `margin-top: 12px;`} */
  }

  .MuiInput-underline:before {
    border-bottom: none;
  }

  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: none;
  }

  .MuiInput-underline:after {
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    position: absolute;
    transform: scaleX(0);
    transition: transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
    border-bottom: 1px solid #005EB8;
    pointer-events: none;
  }

  .MuiInput-underline:focus-within:not(.Mui-disabled):before {
    border-bottom: 1px solid rgba(0, 0, 0, 0.87);
  }

  .MuiFormControl-root {
    min-width: unset;
  }

  span {
    font-size: 16px;
    color: #000000;
    cursor: pointer;
  }

  .MuiInputBase-input {
    font-size: 20px;
    color: #000000;
    font-weight: bold;
    padding: 2px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .MuiInputBase-root {
    &.MuiInput-underline {
      padding: 0;
      &:not(.Mui-disabled):before {
        border-bottom: none;
      }
    }
  }

  .edit-control {
    visibility: hidden;
  }

  &:hover, &:focus-within {
    .MuiInput-underline:after {
      transform: scaleX(1)
    }

    .edit-control {
      visibility: visible;
    }
  }
`;

export const Input = styled.input`
  background-color: transparent;
  box-sizing: content-box;
  height: 32px;

  color: inherit;
  cursor: text;

  border-width: 0px;
  border-style: initial;
  border-color: initial;
  border-image: initial;

  outline: 0px;

  max-width: 100%;

  &:focus {
    border-bottom: 1px solid #005eb8;
  }
`;

export const WrapperLoading = styled.div`
  min-width: 30px;
  max-width: 35px;
  height: 30px;
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 5px;
`;

export const StyledInfoOutlined = styled(InfoOutlined)`
  color: red;
`;
