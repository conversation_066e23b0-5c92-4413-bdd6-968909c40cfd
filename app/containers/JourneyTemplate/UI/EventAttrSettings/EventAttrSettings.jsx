/* eslint-disable prefer-destructuring */
import React from 'react';
import { JOURNEY_OBJECT_TYPE, MODE } from '../../constant';
import { get, isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import BOAttributeDetail from '../../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Detail';
import EventAttributePreview from '../../../UIPreview/EventAtributesPreview';
import { useActiveObj } from '../../hooks/useActiveObj';
import { selectMode } from '../../selector';
import { useSelector } from 'react-redux';
import { Grid } from '@material-ui/core';

export const EventAttrSettings = props => {
  const { errors = [] } = props;

  const mode = useSelector(selectMode);

  const { activeObj, info, saveInfo } = useActiveObj();

  const isEventAttrObject =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.eventAttribute;

  if (!isEventAttrObject) return null;

  if (activeObj.itemTypeId) {
    const getComponentProperties = () => {
      const properties = {};

      if (!info.isExist) {
        properties.settings = {
          ...saveInfo.settings,
        };
        properties.newItemTypeId = get(
          info,
          'dataObject.verify.settings.item_type_id',
        );
      }

      if (info.isExist && mode === MODE.Use) {
        properties.itemTypeId = info.settings.item_type_id;
        properties.itemPropertyName = info.settings.item_property_name;
      }

      if (info.isExist && mode === MODE.Save) {
        properties.itemTypeId = info.itemTypeId;
        properties.itemPropertyName = info.itemPropertyName;
      }

      return properties;
    };

    return (
      <React.Fragment>
        {!info.isExist ? (
          <Grid container className="p-all-4">
            <Grid item xs={2}>
              <div>
                Attribute Name<span style={{ color: 'red' }}> *</span>
              </div>
            </Grid>
            <Grid item xs={10} style={{ color: 'red' }}>
              {saveInfo.settings.item_property_display}
            </Grid>
          </Grid>
        ) : (
          <BOAttributeDetail
            isViewMode
            errors={errors}
            {...getComponentProperties()}
          />
        )}
      </React.Fragment>
    );
  }

  const renderProps = {};

  if (mode === MODE.Save && info.isExist) {
    renderProps.settings = info.settings;
  }

  if (mode === MODE.Use) {
    renderProps.settings = info.settings || saveInfo.settings;
  }

  if (isEmpty(renderProps)) return null;

  return <EventAttributePreview {...renderProps} />;
};

EventAttrSettings.propTypes = {
  errors: PropTypes.array,
};
