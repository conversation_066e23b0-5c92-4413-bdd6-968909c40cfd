/* eslint-disable indent */
import styled from 'styled-components';

export const WrapperCollapseList = styled.div`
  ${props => (props.isDivider ? 'border-bottom: 1px solid #E5E5E5' : '')};
  .item-collapse {
    padding-left: 5px;
    padding-right: 5px;
    justify-content: ${props =>
      props.isExpandSidePane ? 'space-between' : 'center'};

    .icon-tab {
      font-size: 22px;
      margin-right: 5px;
      min-width: 30px;
      text-align: center;
    }

    .title-label {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 700;
    }
  }

  .item-report {
    padding-left: 29px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #eff8ff;
      .icon-xlab-more-vertical {
        visibility: visible;
      }
    }

    &.active {
      background-color: #deeffe;
      min-height: 34px;
    }

    .icon-xlab-more-vertical {
      font-size: 16px;
      color: #005fb8;
      visibility: hidden;
      font-weight: bold;
    }
  }

  .sub-side-pane {
    display: none;
    top: 65px;
    left: 55px;
    width: 200px;
    height: calc(100vh - 75px);
    flex-direction: column;
    background-color: #fff;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    overflow: auto;

    &.active {
      display: flex;
      z-index: 999;
      position: fixed;
    }

    .title {
      font-size: 12px;
      padding: 15px 15px 10px;
      font-weight: bold;
      color: #000;
    }
  }
`;
