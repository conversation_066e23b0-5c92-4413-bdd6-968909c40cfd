import { getDataLookupIds } from '../../../../services/map';
import { JOURNEY_OBJECT_TYPE } from '../../constant';

export const getEventAttributes = (eventInfo, eventAttrInfos = [], objects) => {
  const { eventActionId, eventCategoryId } = eventInfo;

  const filteredEventAttrs = objects.filter(
    obj =>
      obj.type === JOURNEY_OBJECT_TYPE.eventAttribute &&
      obj.eventCategoryId === eventCategoryId &&
      obj.eventActionId === eventActionId,
  );

  const result = {
    eventProperties: [],
    itemProperties: [],
  };

  eventAttrInfos.forEach(eventProperty => {
    const { eventPropertyName, itemTypeId } = eventProperty;
    const eventAttr = filteredEventAttrs.find(i => {
      if (itemTypeId) {
        return (
          i.itemPropertyName === eventPropertyName &&
          i.itemTypeId === itemTypeId
        );
      }

      return i.eventPropertyName === eventPropertyName;
    });

    if (!eventAttr) return;

    if (itemTypeId) {
      const dataBO = objects.find(
        obj =>
          obj.itemTypeId === itemTypeId &&
          obj.type === JOURNEY_OBJECT_TYPE.dataObject,
      );

      const newSettings = {
        itemTypeId: eventAttr.settings.item_type_id,
        itemPropertyName: eventAttr.settings.item_property_name,
        itemPropertyDisplay: eventAttr.settings.item_property_display,
        dataType: eventAttr.settings.data_type,
        status: eventAttr.settings.status,
        propertyDisplayMultilang: eventAttr.settings.property_display_multilang,
        type: 1,
        itemSystemDefined: 0,
        translateLabel: `${dataBO.settings.translate_label} >> ${
          eventAttr.settings.translate_label
        }`,
      };

      result.itemProperties.push(newSettings);
      return;
    }

    const newSettings = {
      eventPropertyDisplay: eventAttr.settings.event_property_display,
      property_display_multilang: eventAttr.settings.property_display_multilang,
      status: eventAttr.settings.status,
      type: eventAttr.settings.type,
      dataType: eventAttr.settings.data_type,
      eventPropertyName: eventAttr.settings.event_property_name,
      itemTypeId: eventAttr.settings.item_type_id,
      translate_label: eventAttr.settings.translate_label,
    };

    result.eventProperties.push(newSettings);
  });

  const res = {
    data: {
      code: 200,
      message: 'Success',
      data: {
        meta: {
          total: 0,
        },
        entries: [
          {
            eventProperties: result.eventProperties,
            itemProperties: result.itemProperties,
          },
        ],
      },
    },
    status: 200,
  };

  return getDataLookupIds(res, 'eventProperty', null, true);
};

export const getFullEventTrackingMap = templateObjSettings => {
  const res = {
    data: {
      code: 200,
      data: {
        entries: templateObjSettings
          .filter(i => i.type === JOURNEY_OBJECT_TYPE.event)
          .map(i => i.settings),
      },
    },
  };

  return getDataLookupIds(res, 'eventSchema');
};

export const getFullEventTracking = objects => {
  const list = [];

  objects.forEach(i => {
    if (i.type === JOURNEY_OBJECT_TYPE.event) {
      list.push({
        eventActionId: i.settings.eventActionId,
        eventCategoryId: i.settings.eventCategoryId,
        eventGroup: i.settings.eventGroup,
        eventNameDisplay: i.settings.itemNameDisplay,
        eventNameMultilang: i.settings.eventNameMultilang,
        eventTrackingCode: i.settings.eventTrackingName,
        eventTrackingName: i.settings.eventTrackingName,
        iconUrl: i.settings.iconUrl,
        status: i.settings.status,
        translateLabel: i.settings.translateLabel,
        available: i.settings.status === 3,
      });
    }
  });

  return {
    code: 200,
    data: list,
  };
};
