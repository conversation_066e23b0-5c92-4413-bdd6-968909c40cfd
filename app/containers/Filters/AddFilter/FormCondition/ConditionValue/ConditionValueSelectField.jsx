// Libraries
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { get, has, isFunction, pick } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// Services
import JourneyServices from 'services/Journey';

// Selectors
import { makeSelectPersonalizations } from '../../../../../modules/Dashboard/selector';

// Hooks
import { useDebounce, useUpdateEffect } from '../../../../../hooks';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import { InputSelectAttribute, Spin, TAG_TYPE } from '@antscorp/antsomi-ui';

// Constants
import {
  COMMON_CODE,
  DEFAULT_DATA_TYPES,
} from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/constants';

// Utils
import {
  initPersonalizations,
  startPersonalizations,
} from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/action';
import { dtoPersonalizations } from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/utils.dto';
import { toUIAtributesPersonalization } from '../../../../../components/common/UIEditorPersonalization/utils';
import { ITEM_TYPE_ID } from '../../../../../components/Templates/TargetAudienceFilter/utils';

const { PROMOTION_CODE, CUSTOMER, VISITOR } = TAG_TYPE;

const PERSONALIZE_GROUPS = {
  [PROMOTION_CODE]: {
    value: PROMOTION_CODE,
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE,
      'Process Allocated Code',
    ),
  },
  [CUSTOMER]: {
    value: CUSTOMER,
    label: translate(
      translations._ATTRIBUTE_TYPE_CUSTOMER,
      'Customer Attribute',
    ),
  },
  [VISITOR]: {
    value: VISITOR,
    label: translate(translations._ATTRIBUTE_TYPE_USER, 'Visitor Attribute'),
  },
};

const LIST_SOURCE_OPTIONS = Object.values(PERSONALIZE_GROUPS);

const Wrapper = styled.div`
  .antsomi-spin-nested-loading > div > .antsomi-spin .antsomi-spin-dot {
    margin: -10px;
  }
`;

function ConditionValueSelectField(props) {
  const {
    value,
    onChange,
    sourceTags,
    helperText,
    error,
    triggerTargetAudienceId,
    channelActive,
  } = props;

  // State
  const [valueState, setValueState] = useState(value || '');
  const debounceValue = useDebounce(valueState, 350);
  const [listSourceOptions, setListSourceOptions] = useState(
    LIST_SOURCE_OPTIONS,
  );

  useEffect(() => {
    if (
      triggerTargetAudienceId &&
      String(triggerTargetAudienceId) === ITEM_TYPE_ID.VISITOR &&
      +channelActive.value === 2
    ) {
      setListSourceOptions(
        LIST_SOURCE_OPTIONS.filter(each => each.value !== CUSTOMER),
      );
    } else {
      setListSourceOptions(LIST_SOURCE_OPTIONS);
    }
  }, [triggerTargetAudienceId, channelActive]);

  const dispatch = useDispatch();

  // Selectors
  const { isLoading = false, isInitDone, settings = {} } = useSelector(
    makeSelectPersonalizations(),
  );

  const isErrorTag = useMemo(
    () =>
      (!!value?.code?.value &&
        value?.source === PROMOTION_CODE &&
        !has(sourceTags, [PROMOTION_CODE, value?.code?.value])) ||
      (String(triggerTargetAudienceId) === ITEM_TYPE_ID.VISITOR &&
      +channelActive.value === 2
        ? value?.source === CUSTOMER
        : false),
    [
      sourceTags,
      value?.source,
      value?.code?.value,
      triggerTargetAudienceId,
      channelActive,
    ],
  );

  const inputValue = useMemo(() => {
    if (typeof valueState === 'string') return valueState;

    return {
      code: valueState?.code?.value || '',
      source: valueState?.source || '',
    };
  }, [valueState]);

  const mapCodeOptions = useMemo(() => {
    const sourceListPromotion = get(sourceTags, PROMOTION_CODE, {});
    const { customer, visitor } = pick(
      get(settings, ['personalizationData'], {}),
      [CUSTOMER, VISITOR],
    );

    if (
      String(triggerTargetAudienceId) === ITEM_TYPE_ID.VISITOR &&
      +channelActive.value === 2
    ) {
      return {
        [VISITOR]: visitor?.list || [],
        [PROMOTION_CODE]: Object.values(sourceListPromotion),
      };
    }

    return {
      [CUSTOMER]: customer?.list || [],
      [VISITOR]: visitor?.list || [],
      [PROMOTION_CODE]: Object.values(sourceListPromotion),
    };
  }, [sourceTags, settings, triggerTargetAudienceId, channelActive]);

  const findCodeBySource = useCallback(
    ({ source, code }) => {
      const options = mapCodeOptions[source] || [];
      const found = options.find(item => item?.value === code);

      return found || null;
    },
    [mapCodeOptions],
  );

  const onChangeInput = useCallback(
    (newData = {}) => {
      if (!newData) return;

      const { value: newValue, valueType } = newData;

      if (valueType === 'select') {
        const { source, code } = newValue;
        const foundCode = findCodeBySource({ source, code });

        if (!foundCode) return;
        const isPromotionSrc = source === PROMOTION_CODE;
        const newCode = isPromotionSrc
          ? pick(foundCode, ['label', 'value', 'nodeId', 'type'])
          : foundCode;

        const data = {
          source,
          code: newCode,
        };
        setValueState(data);
      } else if (valueType === 'input') {
        setValueState(newValue);
      }
    },
    [findCodeBySource],
  );

  useEffect(() => {
    const isExistCustomer = has(settings, ['personalizationData', CUSTOMER]);
    const isExistVisitor = has(settings, ['personalizationData', VISITOR]);
    if (isInitDone && isExistCustomer && isExistVisitor) return;

    dispatch(initPersonalizations());
    dispatch(
      startPersonalizations({
        [COMMON_CODE]: {
          savedKey: COMMON_CODE,
          serviceFn: JourneyServices.fetch.personalizationAttrs,
          dtoFn: dtoPersonalizations,
          payload: {
            data: {
              objectType: 'STORIES',
              groupCodes: [CUSTOMER, VISITOR],
              dataTypes: DEFAULT_DATA_TYPES,
              eventCategoryId: null,
              eventActionId: null,
            },
          },
          normalizeData: toUIAtributesPersonalization,
        },
      }),
    );
  }, []);

  useUpdateEffect(() => {
    if (isFunction(onChange)) {
      onChange(debounceValue);
    }
  }, [debounceValue]);

  return (
    <Wrapper>
      <Spin spinning={isLoading} indicatorSize={18}>
        <InputSelectAttribute
          value={inputValue}
          isErrorTag={isErrorTag}
          onChange={onChangeInput}
          sourceOptions={listSourceOptions}
          mapCodeOptions={mapCodeOptions}
          errorMsg={error ? helperText : ''}
        />
      </Spin>
    </Wrapper>
  );
}

ConditionValueSelectField.propTypes = {
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({ source: PropTypes.string, code: PropTypes.object }),
  ]),
  onChange: PropTypes.func,
  sourceTags: PropTypes.object,
  helperText: PropTypes.string,
  error: PropTypes.bool,
  triggerTargetAudienceId: PropTypes.string,
  channelActive: PropTypes.object,
};

ConditionValueSelectField.defaultProps = {};

export default ConditionValueSelectField;
