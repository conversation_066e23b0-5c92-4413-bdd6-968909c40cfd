/* eslint-disable dot-notation */
/* eslint-disable import/no-cycle */
/* eslint-disable react/no-unused-prop-types */
/* eslint-disable react/prop-types */
/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
import React, { useEffect, useMemo, useState } from 'react';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { useImmer } from 'use-immer';
import PropTypes from 'prop-types';
// import { UILoading as Loading } from '@xlab-team/ui-components';
// import { useParams } from 'react-router';
// import JourneyServices from 'services/Journey';
// import TemplatesMediaServices from 'services/TemplatesMedia';
// import ObjectServices from 'services/Object';
// import SelectorServices from 'services/Selector';
// import MetaDataServices from 'services/MetaData';

// Sevices
import PermServices from 'services/Operate';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  acceptablePatternChecking,
  DEFAULT_ACCEPT_TAGS,
  getCachedRegex,
  patternHandlers,
} from '@antscorp/antsomi-ui';
import EditorPersonalization from '../index';
import { InputPersonalizable } from '../../../Organisms';
// import InputPersonalization from '../Input';

// import {
//   toUIAtributesPersonalization,
//   toUIDataContentSource,
//   toUIDataObjectWidgets,
//   toUIDataProductTemplates,
//   toUIDataPromotionCode,
//   toUIDataPromotionCodeAttr,
// } from '../utils';
// import { safeParse } from '../../../../utils/common';
// import { addMessageToQueue } from '../../../../utils/web/queue';
import WrapperEmailEditor from './WrapperEmailEditor';
import WrapperHTMLEditor from './WrapperHTMLEditor';
// import { buildMergeTag } from './utils';
// import { useDeepCompareEffect } from '../../../../hooks';
// import { getObjectPropSafely } from '../utils.3rd';
import { withPersonalizations } from './hocs';
import { makeSelectPersonalizations } from '../../../../modules/Dashboard/selector';
import { excludeOptions, getPersonalizationInfo } from './utils';
import { JOURNEY_CUSTOM_FN_CODE, SHORT_LINK_V2 } from './constants';

// const setEditor = new Set(['htmlEditor', 'editor']);
const setInput = new Set(['input']);
// const SUCCESS_STATUS = 200;

const WrapperPersonalization = props => {
  // const loadDataViewModeRef = useRef();
  // const arr = window.location.pathname.split('/');
  // let _isMounted = true;
  const [state, setState] = useImmer({
    // personalizationType: {
    //   list: [],
    //   map: {},
    // },
    // personalizationData: {},
    // personalizationDataPromotionCodeAttr
    // promotionCodeAttr: {
    //   list: [],
    //   map: {},
    // },
    // mergeTags: [],
    // contentSources: [],
    isLoading: false,
    isLoadDataDone: false,
    css: '',
    js: '',
    isRendering: true,
    showObjectWidget: false,
    isInitDone: false,
  });

  // Props
  const {
    isViewMode = false,
    // isUsePairKeyValue = false,
    // isChannelEmail = false,
    // isChannelSMS = false,
    // isChannelTelegram = false,
    isAppendBOContentSource = false,
    appendPersonalizeType = [],
  } = props;

  // Selectors
  const {
    isLoading = false,
    isLoadingContentSources = false,
    isInitDone = false,
    settings = {},
  } = useSelector(makeSelectPersonalizations());

  const {
    mergeTags = [],
    contentSources = [],
    promotionCodeAttr = {
      list: [],
      map: {},
    },
    personalizationData: persData = {},
    personalizationType: persType = {
      list: [],
      map: {},
    },
  } = settings;

  // Memoizations
  const initInputValue = useMemo(() => {
    const result = props.initData ?? props.initValue;

    // Make sure it's a string
    if (!_.isString(result)) return '';

    return result;
  }, [props.initData, props.initValue]);

  const isLoadingPersonalizations = useMemo(
    () => isLoading || isLoadingContentSources || !isInitDone,
    [isLoading, isLoadingContentSources, isInitDone],
  );

  const personalizeGroupCodes = useMemo(() => {
    const arr = window.location.pathname.split('/');
    const newGroupCodes = _.cloneDeep(props.groupCodes);
    let appendCodeIds = [];

    if (
      isAppendBOContentSource &&
      _.isArray(appendPersonalizeType) &&
      appendPersonalizeType?.length > 0
    ) {
      appendCodeIds = appendPersonalizeType.map(item => item.groupId);
    }

    // add more custom function for journey into groupCodes
    if (arr.includes('journeys')) {
      appendCodeIds.push(JOURNEY_CUSTOM_FN_CODE);
    }

    if (props.enableShortLink) {
      appendCodeIds.push(SHORT_LINK_V2);
    }

    return _.union(newGroupCodes, appendCodeIds);
  }, [props.groupCodes, isAppendBOContentSource, appendPersonalizeType]);

  const {
    personalizationType = { list: [], map: {} },
    personalizationData = {},
  } = useMemo(
    () =>
      getPersonalizationInfo({
        groupCodes: personalizeGroupCodes,
        data: persData,
        type: excludeOptions(persType, [SHORT_LINK_V2]),
      }),
    [persData, persType, personalizeGroupCodes],
  );

  const stateCombineMemoized = useMemo(
    () => ({
      ...state,
      personalizationType,
      personalizationData,
      mergeTags,
      contentSources,
      promotionCodeAttr,
    }),
    [
      state,
      personalizationType,
      personalizationData,
      mergeTags,
      contentSources,
      promotionCodeAttr,
    ],
  );

  const journeyCustomMemoized = useMemo(() => {
    const customFnPath = ['personalizationData', JOURNEY_CUSTOM_FN_CODE];

    if (!_.has(stateCombineMemoized, customFnPath)) {
      return { list: [], map: {} };
    }

    return _.get(stateCombineMemoized, customFnPath);
  }, [stateCombineMemoized?.personalizationData]);

  const variantExtraDataMemoized = useMemo(() => {
    const { formatAttributes = {}, customFunction = {} } =
      props.otherData?.variantExtraData || {};

    return {
      formatAttributes,
      customFunction,
    };
  }, [
    props.otherData?.variantExtraData?.formatAttributes,
    props.otherData?.variantExtraData?.customFunction,
  ]);
  // const setStateCommon = objects => {
  //   if (_isMounted) {
  //     setState(draft => {
  //       Object.keys(objects).forEach(key => {
  //         if (key === 'personalizationType') {
  //           const { list = [], map = {} } = getObjectPropSafely(
  //             () => objects[key] || {},
  //             {},
  //           );
  //           const newList = _.uniqBy(
  //             [...list, ...draft.personalizationType.list],
  //             'value',
  //           );
  //           const newMap = {
  //             ...draft.personalizationType.map,
  //             ...map,
  //           };

  //           draft[key] = {
  //             list: newList,
  //             map: newMap,
  //           };
  //         } else if (key === 'personalizationData') {
  //           const newPersonalizeData = getObjectPropSafely(
  //             () => objects[key] || {},
  //             {},
  //           );

  //           draft[key] = {
  //             ...draft.personalizationData,
  //             ...newPersonalizeData,
  //           };
  //         } else {
  //           draft[key] = objects[key];
  //         }
  //       });
  //     });
  //   }
  // };

  // console.log('state ===>', state);
  // const fetchData = async () => {
  //   setStateCommon({ isLoading: true });
  //   let eventCategoryId = null;
  //   let eventActionId = null;
  //
  //   const eventValue = safeParse(props.eventValue, {});
  //   eventCategoryId = safeParse(eventValue.eventCategoryId, null);
  //   eventActionId = safeParse(eventValue.eventActionId, null);
  //   const params = 'page=0&search=&limit=1000&sort=utime&sd=desc';
  //
  //   try {
  //     const groupsAttrs = [];
  //
  //     let newGroupCodes = props.groupCodes;
  //     if (props.isBlastCampaign) {
  //       const { itemTypeId } = props;
  //       newGroupCodes = [...props.groupCodes, 'visitor', 'customer'].filter(
  //         (value, index, self) => self.indexOf(value) === index,
  //       );
  //       if (itemTypeId === -1003) {
  //         newGroupCodes.splice(newGroupCodes.indexOf('visitor'), 1);
  //       }
  //     }
  //     newGroupCodes.forEach(item => {
  //       if (item === 'event' || item === 'visitor' || item === 'customer') {
  //         groupsAttrs.push(item);
  //       }
  //     });
  //     const dataAttrs = await JourneyServices.fetch.personalizationAttrs({
  //       data: {
  //         objectType: 'STORIES',
  //         groupCodes: groupsAttrs,
  //         dataTypes: props.dataTypes,
  //         eventCategoryId: groupsAttrs.includes('event')
  //           ? eventCategoryId
  //           : null,
  //         eventActionId: groupsAttrs.includes('event') ? eventActionId : null,
  //       },
  //     });
  //
  //     let dataCommon = {
  //       personalizationType: {
  //         list: [],
  //         map: {},
  //       },
  //       personalizationData: {},
  //       promotionCodeAttr: {
  //         list: [],
  //         map: {},
  //       },
  //     };
  //
  //     if (dataAttrs.code === SUCCESS_STATUS) {
  //       dataCommon = toUIAtributesPersonalization(dataAttrs.data);
  //     } else {
  //       setStateCommon({ isLoading: false, isLoadDataDone: true });
  //     }
  //     if (props.groupCodes.includes('promotion_code')) {
  //       const dataPromotionCode = await ObjectServices.suggestionMultilang.getList(
  //         {
  //           data: {
  //             filters: { OR: [{ AND: [{}] }] },
  //             limit: 2000,
  //             page: 0,
  //             search: '',
  //             sort: 'asc',
  //             objectType: 'PROMOTION_POOL',
  //           },
  //         },
  //       );
  //
  //       // if (dataPromotionCode.code === SUCCESS_STATUS) {
  //       const dataPromotionPools = toUIDataPromotionCode(
  //         dataPromotionCode.list,
  //       );
  //
  //       const tempDataGroup = {
  //         value: 'promotion_code',
  //         label: 'Promotion Code',
  //         color: '#6750A7',
  //       };
  //
  //       dataCommon.personalizationType.list.push(tempDataGroup);
  //       dataCommon.personalizationType.map[tempDataGroup.value] = tempDataGroup;
  //       dataCommon.personalizationData[
  //         tempDataGroup.value
  //       ] = dataPromotionPools;
  //
  //       const dataPromotionCodeAttr = await SelectorServices.attribute.getListBOAttribute(
  //         {
  //           data: {
  //             filters: {
  //               OR: [
  //                 {
  //                   AND: [
  //                     {
  //                       column: 'item_type_id',
  //                       data_type: 'number',
  //                       operator: 'equals',
  //                       value: -100,
  //                     },
  //                     {
  //                       type: 1,
  //                       column: 'status',
  //                       data_type: 'number',
  //                       operator: 'matches',
  //                       value: [1, 2],
  //                     },
  //                   ],
  //                 },
  //               ],
  //             },
  //             limit: null,
  //             page: null,
  //           },
  //         },
  //       );
  //       const dataPromotionAttrs = toUIDataPromotionCodeAttr(
  //         dataPromotionCodeAttr.data,
  //       );
  //       dataCommon.promotionCodeAttr = dataPromotionAttrs;
  //
  //       // } else {
  //       //   setStateCommon({ isLoading: false, isLoadDataDone: true });
  //       // }
  //     }
  //
  //     if (props.groupCodes.includes('objectWidget')) {
  //       const dataObjectwidget = await TemplatesMediaServices.objectWidgets.getList(
  //         params,
  //       );
  //
  //       if (dataObjectwidget.code === SUCCESS_STATUS) {
  //         const data = toUIDataObjectWidgets(dataObjectwidget.data);
  //
  //         const tempDataGroup = {
  //           value: 'objectWidget',
  //           label: 'Object Widget',
  //           color: '#24abb9',
  //         };
  //
  //         dataCommon.personalizationType.list.push(tempDataGroup);
  //         dataCommon.personalizationType.map[
  //           tempDataGroup.value
  //         ] = tempDataGroup;
  //         dataCommon.personalizationData[tempDataGroup.value] = data;
  //       } else {
  //         setStateCommon({ isLoading: false, isLoadDataDone: true });
  //       }
  //     }
  //     //  customFunction journey
  //     if (arr.includes('journeys')) {
  //       dataCommon.personalizationType.list.push({
  //         value: 'custom',
  //         label: 'Custom',
  //         color: '#bbefbe',
  //       });
  //       dataCommon.personalizationType.map.custom = {
  //         value: 'custom',
  //         label: 'Custom',
  //         color: '#bbefbe',
  //       };
  //     }
  //
  //     // input not support
  //     if (!setInput.has(props.typeComponent)) {
  //       if (props.groupCodes.includes('productTemplate')) {
  //         const dataProductTemplates = await MetaDataServices.productTemplate.getList(
  //           params,
  //         );
  //
  //         if (dataProductTemplates.code === SUCCESS_STATUS) {
  //           const data = toUIDataProductTemplates(dataProductTemplates.data);
  //
  //           const tempDataGroup = {
  //             value: 'productTemplate',
  //             label: 'Product Template',
  //             color: '#9238b5',
  //           };
  //
  //           dataCommon.personalizationType.list.push(tempDataGroup);
  //           dataCommon.personalizationType.map[
  //             tempDataGroup.value
  //           ] = tempDataGroup;
  //           dataCommon.personalizationData[tempDataGroup.value] = data;
  //         } else {
  //           setStateCommon({ isLoading: false, isLoadDataDone: true });
  //         }
  //       }
  //     }
  //
  //     const mergeTags = [];
  //     Object.keys(dataCommon.personalizationData).forEach(key => {
  //       if (['visitor', 'customer', 'promotion_code'].includes(key)) {
  //         const tmp = buildMergeTag(
  //           key,
  //           dataCommon.personalizationData[key].list,
  //         );
  //         mergeTags.push(tmp);
  //       }
  //     });
  //     // dataCommon.mergeTags = mergeTags;
  //     // console.log({ dataCommon });
  //     setStateCommon({
  //       mergeTags,
  //       personalizationType: dataCommon.personalizationType,
  //       personalizationData: dataCommon.personalizationData,
  //       promotionCodeAttr: dataCommon.promotionCodeAttr,
  //       isLoading: false,
  //       isLoadDataDone: true,
  //       isInitDone: true,
  //     });
  //   } catch (err) {
  //     addMessageToQueue({
  //       path:
  //         'app/components/common/UIEditorPersonalization/WrapperPersonalization/index.jsx',
  //       func: 'fetchData',
  //       data: err.stack,
  //     });
  //     console.log('err ===>', err);
  //     setStateCommon({ isLoading: false, isLoadDataDone: true });
  //   }
  // };

  // useDeepCompareEffect(() => {
  //   if (props.showPersonalization && !props.onlyAddIcon) {
  //     // Not use objectWidget option anymore
  //     setStateCommon({
  //       showObjectWidget: props.groupCodes.includes('objectWidget'),
  //     });
  //     // fetchData();
  //     // fetchDataObjectWidgets();
  //     // fetchDataProductTemplates();
  //   }
  // }, [props.itemTypeId]);

  // useEffect(() => {
  //   if (!props.showPersonalization) {
  //     setState(draft => {
  //       draft.isInitDone = true;
  //     });
  //   }
  // }, [props.showPersonalization]);

  // useDeepCompareEffect(() => {
  //   if (props.onlyAddIcon) {
  //     setStateCommon({
  //       isLoading: false,
  //       isLoadDataDone: true,
  //       isInitDone: true,
  //       isRendering: false,
  //     });
  //   }
  // }, [props.onlyAddIcon]);

  // useDeepCompareEffect(() => {
  //   let ignore = false;
  //
  //   const fetchContentSource = async () => {
  //     try {
  //       setStateCommon({ isLoading: true });
  //
  //       const dataCommon = {
  //         personalizationType: {
  //           list: [],
  //           map: {},
  //         },
  //         personalizationData: {},
  //         promotionCodeAttr: {
  //           list: [],
  //           map: {},
  //         },
  //       };
  //       const matchItemTypeId = new Set();
  //       const boContentSources = [];
  //       const personalizeMap = appendPersonalizeType.reduce((accur, item) => {
  //         accur[item.groupId] = {
  //           label: item.label,
  //           value: item.groupId,
  //           color: item.color,
  //         };
  //
  //         matchItemTypeId.add(Number(item.itemTypeId));
  //         boContentSources.push(item);
  //
  //         return accur;
  //       }, {});
  //
  //       const response = await SelectorServices.attribute.getListBOAttribute({
  //         data: {
  //           filters: {
  //             OR: [
  //               {
  //                 AND: [
  //                   {
  //                     column: 'item_type_id',
  //                     data_type: 'number',
  //                     operator: 'matches',
  //                     value: [...matchItemTypeId],
  //                   },
  //                   {
  //                     type: 1,
  //                     column: 'status',
  //                     data_type: 'number',
  //                     operator: 'matches',
  //                     value: [1, 2],
  //                   },
  //                 ],
  //               },
  //             ],
  //           },
  //           limit: null,
  //           page: null,
  //         },
  //       });
  //       const personalizeDataGroups = toUIDataContentSource({
  //         appendPersonalizeType,
  //         data: response.data,
  //       });
  //
  //       if (Object.keys(personalizeDataGroups || {}).length > 0) {
  //         dataCommon.personalizationType.list.push(
  //           ...Object.values(personalizeMap || {}),
  //         );
  //         dataCommon.personalizationType.map = {
  //           ...dataCommon.personalizationType.map,
  //           ...personalizeMap,
  //         };
  //         dataCommon.personalizationData = {
  //           ...dataCommon.personalizationData,
  //           ...personalizeDataGroups,
  //         };
  //
  //         if (!ignore) {
  //           setStateCommon({
  //             contentSources: boContentSources,
  //             personalizationType: dataCommon.personalizationType,
  //             personalizationData: dataCommon.personalizationData,
  //             promotionCodeAttr: dataCommon.promotionCodeAttr,
  //             isInitDone: true,
  //           });
  //         } else {
  //           setStateCommon({ isLoading: false, isLoadDataDone: true });
  //         }
  //       }
  //     } catch (err) {
  //       addMessageToQueue({
  //         path:
  //           'app/components/common/UIEditorPersonalization/WrapperPersonalization/index.jsx',
  //         func: 'fetchContentSource',
  //         data: err.stack,
  //       });
  //       setStateCommon({ isLoading: false, isLoadDataDone: true });
  //     }
  //   };
  //
  //   // Add more Personalization attribute type in case has BO content source
  //   if (
  //     isAppendBOContentSource &&
  //     Array.isArray(appendPersonalizeType) &&
  //     appendPersonalizeType.length > 0 &&
  //     props.showPersonalization &&
  //     !props.onlyAddIcon
  //   ) {
  //     fetchContentSource();
  //   } else if (appendPersonalizeType.length === 0) {
  //     setState(draft => {
  //       const contentSourceGroupIds = draft.contentSources.map(
  //         cs => cs.groupId,
  //       );
  //
  //       contentSourceGroupIds.forEach(groupId => {
  //         if (_.has(draft.personalizationData, groupId)) {
  //           draft.personalizationData = _.omit(draft.personalizationData, [
  //             groupId,
  //           ]);
  //           draft.personalizationType.list = draft.personalizationType.list.filter(
  //             item => item.value !== groupId,
  //           );
  //           draft.personalizationType.map = _.omit(
  //             draft.personalizationType.map,
  //             [groupId],
  //           );
  //         }
  //       });
  //
  //       draft.contentSources = [];
  //     });
  //   }
  //
  //   return () => {
  //     ignore = true;
  //   };
  // }, [
  //   appendPersonalizeType,
  //   isAppendBOContentSource,
  //   props.showPersonalization,
  // ]);

  // useEffect(
  //   () => () => {
  //     _isMounted = false;
  //   },
  //   [],
  // );

  // useEffect(() => {
  //   if (
  //     (isViewMode &&
  //       props.typeComponent === 'editor' &&
  //       isChannelEmail &&
  //       props.step === 2 &&
  //       state.isLoadDataDone &&
  //       state.isInitDone) ||
  //     (props.typeComponent === 'htmlEditor' &&
  //       isViewMode &&
  //       props.step === 2 &&
  //       state.isLoadDataDone &&
  //       state.isInitDone)
  //   ) {
  //     if (
  //       props.typeComponent === 'htmlEditor' &&
  //       loadDataViewModeRef.current &&
  //       props.value
  //     ) {
  //       loadDataViewModeRef.current.innerHTML = replacePersonalizeTag(
  //         props.value.html,
  //       );
  //     } else if (loadDataViewModeRef.current && props.value) {
  //       loadDataViewModeRef.current.style.width = '87%';
  //       loadDataViewModeRef.current.innerHTML = replacePersonalizeTag(
  //         props.value,
  //       );
  //     }
  //   }
  // }, [
  //   props.initData,
  //   state.isLoadDataDone,
  //   state.isInitDone,
  //   props.step,
  //   props.isViewMode,
  // ]);
  // console.log('props.typeComponent', props.typeComponent, props.value)
  const renderContentPersonalization = () => {
    if (props.typeComponent === 'editor') {
      return (
        <WrapperEmailEditor
          dataState={stateCombineMemoized}
          objectWidgetInput={props.objectWidgetInput}
          variantExtraData={props.variantExtraData}
          type={props.typeComponent}
          initData={props.initData}
          onChange={props.onChange}
          onChangeOthers={props.onChangeOthers}
          showPopupEditorHTML={props.showPopupEditorHTML}
          showPersonalization={props.showPersonalization}
          showObjectWidget={stateCombineMemoized.showObjectWidget}
          componentKey={props.componentKey}
          contentValue={props.value}
          isViewMode={isViewMode}
        />
      );
    }
    if (props.typeComponent === 'htmlEditor') {
      return (
        <WrapperHTMLEditor
          name={props.name}
          dataState={stateCombineMemoized}
          objectWidgetInput={props.objectWidgetInput}
          variantExtraData={props.variantExtraData}
          type={props.typeComponent}
          initData={props.initData}
          onChange={props.onChange}
          onChangeOthers={props.onChangeOthers}
          showPopupEditorHTML={props.showPopupEditorHTML}
          showPersonalization={props.showPersonalization}
          showObjectWidget={stateCombineMemoized.showObjectWidget}
          componentKey={props.componentKey}
          fullWidthPopover={props.fullWidthPopover}
          validateKey={props.validateKey}
          isViewMode={isViewMode}
        />
      );
    } else if (setInput.has(props.typeComponent)) {
      return (
        <div style={{ width: props.width || '100%' }}>
          <InputPersonalizable
            key={props.componentKey}
            moduleConfig={props.moduleConfig}
            useEmoji={props.hasEmoji}
            disabled={isLoadingPersonalizations}
            channelCode={props.channelElement}
            name={props.name}
            initValue={initInputValue} // use only for init
            isViewMode={isViewMode}
            readonlyTag={isLoadingPersonalizations} // disabled click on the tag when it is loading
            errors={props.errors}
            dataState={stateCombineMemoized}
            tagProperties={props.tagProperties}
            mapAttributes={props.mapAttributes}
            mapErrorAttributes={props.mapErrorAttributes}
            listTemplateCustom={journeyCustomMemoized}
            enableShortLink={props.enableShortLink}
            variantExtraData={variantExtraDataMemoized}
            hiddenDynamicOption={props.hiddenDynamicOption}
            onChange={props.onChange}
            // onChangeOthers={props.onChangeOthers}
            onTagRemove={props.onTagRemove}
            onChangeExtraDataTagProperty={props.onChangeExtraDataTagProperty}
            shouldEscapeHtml={props.shouldEscapeHtml}
            isForceHideBtnPersonalization={props.isForceHideBtnPersonalization}
            showShortLinkWithBroadcast={props.showShortLinkWithBroadcast}
            onlyShowGeneralShortlink={props.onlyShowGeneralShortlink}
            placeholder={props.placeholder}
            showMergeTagDisplay={props.showMergeTagDisplay}
            useAllocatedCode={props.useAllocatedCode}
            isNewCustomTag={props.isNewCustomTag}
            catalogCode={props.catalogCode}
          />
        </div>
      );

      // return (
      //     {/* <InputPersonalization */}
      //     {/*   errors={props.errors} */}
      //     {/*   name={props.name} */}
      //     {/*   isLoadingPersonalizations={isLoadingPersonalizations} */}
      //     {/*   customFnPersonalization={journeyCustomMemoized} */}
      //     {/*   dataState={stateCombineMemoized} */}
      //     {/*   objectWidgetInput={props.objectWidgetInput} */}
      //     {/*   initData={props.initData} */}
      //     {/*   componentKey={props.componentKey} */}
      //     {/*   onChange={props.onChange} */}
      //     {/*   onChangeOthers={props.onChangeOthers} */}
      //     {/*   isForceHideBtnPersonalization={props.isForceHideBtnPersonalization} */}
      //     {/*   isChannelEmail={props.isChannelEmail} */}
      //     {/*   isChannelSMS={props.isChannelSMS} */}
      //     {/*   isChannelTelegram={isChannelTelegram} */}
      //     {/*   isChannelLine={props.isChannelLine} */}
      //     {/*   isSmartInbox={props.isSmartInbox} */}
      //     {/*   isViewMode={isViewMode} */}
      //     {/*   isSimplifyUI={props.isSimplifyUI} */}
      //     {/*   canMultipleLine={props.canMultipleLine} */}
      //     {/*   value={props.value} */}
      //     {/*   isUsePairKeyValue={isUsePairKeyValue} */}
      //     {/*   width={props.width} */}
      //     {/*   setCaretPosition={props.setCaretPosition} */}
      //     {/*   hasEmoji={props.hasEmoji} */}
      //     {/*   inputPersonalId={props.inputPersonalId} */}
      //     {/*   isInputHeightSM={props.isInputHeightSM} */}
      //     {/*   styledComponentBlast={props.styledComponentBlast} */}
      //     {/*   isBlastCampaign={props.isBlastCampaign} */}
      //     {/*   isCenterBlast={props.isCenterBlast} */}
      //     {/*   isDestination={props.isDestination} */}
      //     {/*   enableShortLink={props.enableShortLink} */}
      //     {/*   hiddenDynamicOption={props.hiddenDynamicOption} */}
      //     {/*   otherData={props.otherData} */}
      //     {/*   catalogCode={props.catalogCode} */}
      //     {/*   placeHolder={props.placeHolder || props.placeholder} */}
      //     {/* /> */}
      // );
    }
    return <EditorPersonalization />;
  };

  // if (
  //   (state.isLoadDataDone === false && props.showPersonalization) ||
  //   state.isInitDone === false
  // ) {
  //   return (
  //     <Loading
  //       isLoading={state.isLoading || state.isRendering || !state.isInitDone}
  //     />
  //   );
  // }
  return (
    <ErrorBoundary path="app/components/common/UIEditorPersonalization/WrapperPersonalization/index.jsx">
      {/* {!isViewMode || */}
      {/*! ['editor', 'htmlEditor'].includes(props.typeComponent) ? ( */}
      {renderContentPersonalization()}
      {/* ) : ( */}
      {/*  <div */}
      {/*    ref={loadDataViewModeRef} */}
      {/*    style={{ */}
      {/*      display: isViewMode ? 'block' : 'none', */}
      {/*      fontSize: '12px', */}
      {/*      color: '#000000', */}
      {/*    }} */}
      {/*  /> */}
      {/* )} */}
    </ErrorBoundary>
  );
};

WrapperPersonalization.propTypes = {
  showPersonalization: PropTypes.bool,
  typeComponent: PropTypes.string,
  showPopupEditorHTML: PropTypes.bool,
  fullWidthPopover: PropTypes.bool,
  shouldEscapeHtml: PropTypes.bool,
  showMergeTagDisplay: PropTypes.bool,
  useAllocatedCode: PropTypes.bool,
  groupCodes: PropTypes.array,
  dataTypes: PropTypes.array,
};

WrapperPersonalization.defaultProps = {
  showPersonalization: true,
  typeComponent: 'editor',
  showPopupEditorHTML: true,
  fullWidthPopover: true,
  shouldEscapeHtml: true,
  showMergeTagDisplay: true,
  useAllocatedCode: false,
  groupCodes: ['visitor', 'customer', 'event', 'story', 'campaign', 'variant'],
  dataTypes: ['string', 'number', 'datetime', 'array_string'],
};

export default withPersonalizations(WrapperPersonalization);
