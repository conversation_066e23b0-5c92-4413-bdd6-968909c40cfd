// Libraries
import React, { memo, useMemo } from 'react';
import { isEmpty, isEqualWith, get, isFunction, has } from 'lodash';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

import { makeSelectAllocatedEventsInfo } from '../../../../modules/Dashboard/selector';

// Actions
import { fetchAllocatedEvents } from '../../UIEditorPersonalization/WrapperPersonalization/libs/action';

// Components
import { FlexContainer } from './styled';
import {
  DEFAULT_TAGS_LIST,
  Flex,
  SelectAssociatedTag,
  Spin,
  Typography,
  useDeepCompareEffect,
} from '@antscorp/antsomi-ui';
import SelectTree from 'components/form/UISelectCondition';

// Constants
import {
  ARCHIVE_STATUS,
  REMOVE_STATUS,
  TYPE_ATTRIBUTE,
} from '../../../../utils/constants';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// Utils
import { filterDeep } from '../../../../utils/web/utils';
import { buildEventTrackingKey } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/utils.flow';
import { extracAttribute } from '../../../../containers/Segment/Content/Condition/utils';
import { findOption } from '../../../form/UISelectCondition/utils';

const { Text } = Typography;

const labelSelectAttrs = translate(
  translations._USER_GUIDE_SELECT_ATTRIBUTE,
  'Select attribute',
);

const filterOptions = (list, childrenPath = 'options') =>
  filterDeep(
    list || [],
    (_value, _key, option) => {
      const { status, type } = option;

      return (
        ![ARCHIVE_STATUS, REMOVE_STATUS].includes(Number(status)) ||
        type !== TYPE_ATTRIBUTE.COMPUTED
      );
    },
    {
      childrenPath,
    },
  );

const HavingCondition = props => {
  // Props
  const {
    group,
    waitEventInfo,
    triggerEventInfo,
    isViewMode,
    selected,
    mapEventSchema,
    onChange,
  } = props;
  const { waitEvent = {}, triggerEvent = {} } = selected;
  const { list: waitEventAttrOpts = [] } = group;

  // Selectors
  const { isLoading, sourceEventAttributes } = useSelector(
    makeSelectAllocatedEventsInfo(),
  );

  // Dispatcher
  const dispatch = useDispatch();

  const eventKey = useMemo(() => buildEventTrackingKey(triggerEventInfo), [
    triggerEventInfo,
  ]);
  const inTriggerLabel = useMemo(() => mapEventSchema?.[eventKey]?.label, [
    eventKey,
    mapEventSchema,
  ]);
  const triggerEventAttr = useMemo(() => {
    return sourceEventAttributes?.[eventKey] || {};
  }, [triggerEventInfo, eventKey, sourceEventAttributes]);

  const triggerEventStatus = useMemo(() => {
    if (!triggerEventAttr?.map) return undefined;

    if (!has(triggerEventAttr?.map, triggerEvent?.attribute?.value))
      return 'error';

    const eventInMap = get(
      triggerEventAttr?.map,
      triggerEvent?.attribute?.value,
      {},
    );

    const compareEvent = (first, second) => {
      return (
        first?.eventActionId === second?.eventActionId &&
        first?.eventCategoryId === second?.eventCategoryId
      );
    };

    const isValidEventAttr = isEqualWith(
      triggerEvent?.attribute,
      eventInMap,
      compareEvent,
    );

    return !isValidEventAttr ? 'error' : undefined;
  }, [triggerEventAttr?.map, triggerEvent?.attribute]);

  const filteredTriggerEventOptions = useMemo(
    () => filterOptions(triggerEventAttr?.list),
    [triggerEventAttr?.list],
  );

  const filteredWaitEventOptions = useMemo(
    () => filterOptions(waitEventAttrOpts),
    [waitEventAttrOpts],
  );

  const selectedTriggerEventAttr = useMemo(
    () =>
      findOption(filteredTriggerEventOptions, triggerEvent?.attribute?.value),
    [filteredTriggerEventOptions, triggerEvent],
  );

  const selectedWaitingEventAttr = useMemo(
    () => findOption(filteredWaitEventOptions, waitEvent?.attribute?.value),
    [filteredWaitEventOptions, waitEvent],
  );

  function handleChangeEvent(
    eventType,
    newValue,
    key,
    attributeExtractor = v => v,
  ) {
    if (isFunction(onChange)) {
      let hash;
      let attribute;
      const isAttr = key === 'attribute';
      const isHash = key === 'hash';

      if (eventType === 'waitEvent') {
        hash = isHash ? newValue : waitEvent?.hash;
        attribute = isAttr
          ? attributeExtractor(newValue)
          : waitEvent?.attribute;
      } else if (eventType === 'triggerEvent') {
        hash = isHash ? newValue : triggerEvent?.hash;
        attribute = isAttr
          ? attributeExtractor(newValue)
          : triggerEvent?.attribute;
      }

      onChange({
        [eventType]: { hash, attribute },
      });
    }
  }

  const handleChangeTriggerEventHashAttribute = newHashItem => {
    handleChangeEvent('triggerEvent', newHashItem?.value, 'hash');
  };

  const handleChangeWaitEventHashAttribute = newHashItem => {
    handleChangeEvent('waitEvent', newHashItem?.value, 'hash');
  };

  const handleChangeWaitEventAttribute = newSelected => {
    const newEventAttr = {
      ...newSelected,
      ...waitEventInfo,
    };

    handleChangeEvent('waitEvent', newEventAttr, 'attribute', extracAttribute);
  };

  const handleChangeTriggerEventAttribute = newSelected => {
    const newEventAttr = {
      ...newSelected,
      eventActionId: triggerEventInfo?.eventActionId,
      eventCategoryId: triggerEventInfo?.eventCategoryId,
    };

    handleChangeEvent(
      'triggerEvent',
      newEventAttr,
      'attribute',
      extracAttribute,
    );
  };

  useDeepCompareEffect(() => {
    if (!isEmpty(triggerEventInfo)) {
      dispatch(fetchAllocatedEvents(triggerEventInfo));
    }
  }, [triggerEventInfo?.eventCategoryId, triggerEventInfo?.eventActionId]);

  return (
    <FlexContainer $isViewMode={isViewMode} gap={20}>
      <Text style={{ minWidth: 90, lineHeight: '30px', color: '#000000' }}>
        {translate(translations._NODE_WAIT_FOR_EVENT_HVING, 'Having')}
      </Text>
      <Flex gap={15}>
        <SelectAssociatedTag
          style={{ minWidth: 200 }}
          selected={{ tag: waitEvent?.hash }}
          disabled={isViewMode}
          tagConfigs={{
            title: translate(
              translations._INFO_STORY_WAIT_FOR_HASH,
              'Hash Method',
            ),
            disabled: isViewMode,
            options: DEFAULT_TAGS_LIST,
            onSelect: handleChangeWaitEventHashAttribute,
          }}
        >
          <SelectTree
            onlyParent={false}
            isViewMode={isViewMode}
            displayFormat
            use="tree"
            options={filteredWaitEventOptions}
            isParentOpen={false}
            value={selectedWaitingEventAttr}
            onChange={handleChangeWaitEventAttribute}
            placeholder={labelSelectAttrs}
          />
        </SelectAssociatedTag>
        <Text style={{ flexShrink: 0, lineHeight: '30px' }}>
          {translate(translations._INFO_STORY_WAIT_FOR_MATCH_KEY, 'match with')}
        </Text>
        <Flex vertical gap={5}>
          <SelectAssociatedTag
            style={{ minWidth: 200 }}
            selected={{ tag: triggerEvent?.hash }}
            status={triggerEventStatus}
            disabled={isViewMode}
            tagConfigs={{
              title: translate(
                translations._INFO_STORY_WAIT_FOR_HASH,
                'Hash Method',
              ),
              disabled: isViewMode,
              options: DEFAULT_TAGS_LIST,
              onSelect: handleChangeTriggerEventHashAttribute,
            }}
          >
            <Spin spinning={isLoading} indicatorSize={20}>
              <SelectTree
                isViewMode={isViewMode}
                onlyParent={false}
                displayFormat
                use="tree"
                options={filteredTriggerEventOptions}
                isParentOpen={false}
                value={selectedTriggerEventAttr}
                onChange={handleChangeTriggerEventAttribute}
                placeholder={labelSelectAttrs}
              />
            </Spin>
          </SelectAssociatedTag>
          <div>
            {triggerEventStatus === 'error' ? (
              <Text style={{ fontSize: '11px', color: globalToken.colorError }}>
                {translate(
                  translations._INFO_STORY_WAIT_FOR_ERR,
                  'The selected attribute does not exist in trigger event.',
                )}
              </Text>
            ) : (
              <Text style={{ color: '#7F7F7F', fontSize: '11px' }}>
                {translate(
                  translations._INFO_STORY_WAIT_FOR_DESCRIP_TEXT,
                  'In trigger event',
                )}
                &nbsp;
                <Text
                  style={{
                    fontWeight: '700',
                    color: '#7F7F7F',
                    fontSize: '11px',
                  }}
                >
                  {inTriggerLabel}
                </Text>
              </Text>
            )}
          </div>
        </Flex>
      </Flex>
    </FlexContainer>
  );
};

HavingCondition.propTypes = {
  selected: PropTypes.object,
  waitEventInfo: PropTypes.object,
  triggerEventInfo: PropTypes.object,
  group: PropTypes.object,
  mapEventSchema: PropTypes.object,
  isViewMode: PropTypes.bool,
  onChange: PropTypes.func,
};
HavingCondition.defaultProps = {
  selected: {},
  waitEventInfo: {},
  triggerEventInfo: {},
  group: {},
  mapEventSchema: {},
  isViewMode: false,
  onChange: () => {},
};

export default memo(HavingCondition);
