import React, { useMemo } from 'react';
import { get } from 'lodash';

import ConversionEventSetting from 'modules/Dashboard/ApiHub/EventSources/Conversion/Detail';

import { JOURNEY_OBJECT_TYPE, MODE } from '../../constant';
import {
  getEventAttributes,
  getFullEventTracking,
  getFullEventTrackingMap,
} from './utils';
import { useActiveObj } from '../../hooks/useActiveObj';
import { useGetObjects } from '../../hooks/useGetObjects';
import { useSelector } from 'react-redux';
import { selectMode } from '../../selector';
import { ERROR_CODE } from '../../utils/config';

export const JourneyGoalSettings = () => {
  const mode = useSelector(selectMode);

  const { activeObj, info, saveInfo } = useActiveObj();
  const { allObjects } = useGetObjects();

  const getErrors = useMemo(() => {
    const conversionCodeExist = info.errors?.find(
      i => i.code === ERROR_CODE.CONVERSION_CODE_EXIST,
    );

    if (conversionCodeExist) {
      return [
        {
          code: 'conversionInternalName',
          message: 'This code has been taken, please use another one',
        },
      ];
    }

    return [];
  }, [info.errors]);

  const eventPropertyService = async (params, value) => {
    const { dataPost } = params;
    const { eventActionId, eventCategoryId, eventTrackingCode } = value;

    return getEventAttributes(
      {
        eventActionId,
        eventCategoryId,
      },
      dataPost.eventIds,
      allObjects,
      eventTrackingCode,
    );
  };

  const getFullEventTrackingService = async () => {
    return getFullEventTracking(allObjects);
  };

  const getFullEventTrackingMapService = async () => {
    return getFullEventTrackingMap(allObjects);
  };

  const journeyGoalObj =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.journeyGoal;

  if (!journeyGoalObj) return null;

  // console.log({ info });
  // console.log({ activeObj });
  // console.log({ saveInfo });
  // console.log({ mode });

  const getComponentProps = () => {
    const properties = {};

    if (!info.isExist) {
      properties.settings = saveInfo.settings;
      properties.eventPropertyService = eventPropertyService;
      properties.getFullEventTrackingService = getFullEventTrackingService;
      properties.getFullEventTrackingMapService = getFullEventTrackingMapService;
      properties.isUsingJourneyTemplate = true;
      properties.showAllSource = true;
    }

    if (info.isExist && mode === MODE.Use) {
      properties.conversionId = activeObj.verify.settings.conversionId;
    }

    if (info.isExist && mode === MODE.Save) {
      properties.conversionId = activeObj.conversionId;
    }

    return properties;
  };

  return (
    <ConversionEventSetting
      isViewMode
      errors={getErrors}
      {...getComponentProps()}
    />
  );
};
