import { useSelector } from 'react-redux';
import { useCallback, useMemo } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { get, isEmpty } from 'lodash';

import { useHeaderContext } from '../context';
import { KEY_PARAMS } from '../../Main/constants';
import {
  getStoryRoleActions,
  STORY_SETUP_ACTION,
} from '../../Create/utils.story.rules';
import {
  APP_ACTION,
  MENU_CODE,
  validateAction,
} from '../../../../../../utils/web/permission';
import { getCurrentAccessUserId } from '../../../../../../utils/web/cookie';
import { MODULE_CONFIG } from '../../config';

// Constants for data paths
const DATA_PATHS = {
  ACTIVE_ROW: 'main.activeRow',
  DESIGN_MODE: 'main.design',
  IS_BLAST_CAMPAIGN: 'main.isBlastCampaign',
  MAIN: 'main',
  CONFIGURE: 'configure',
  CHANNEL_ACTIVE: 'main.drawerJourneyInfo.channelActive',
  IS_INIT_DONE: 'main.isInitDone',
  IS_SAVE_DONE: 'main.drawerJourneyInfo.isSaveDone',
};

const DEFAULT_MODE = 'create';
const PREVIEW_MODE = 'preview';

export const useHeaderState = () => {
  const { state: stateHeader } = useHeaderContext();
  const location = useLocation();
  const history = useHistory();

  // Extract context state
  const { moduleConfig, activeRow: activeRowDetail } = stateHeader;

  // Parse URL parameters once
  const searchParams = useMemo(() => new URLSearchParams(location.search), [
    location.search,
  ]);

  const currentTab = useMemo(() => searchParams.get(KEY_PARAMS.TAB), [
    searchParams,
  ]);

  const isDoingCopy = useMemo(
    () => Boolean(searchParams.get(KEY_PARAMS.COPY_ID)),
    [searchParams],
  );

  const storyCreateData = useSelector(state => state.get(moduleConfig.key));

  const stateCommon = useSelector(state => state.get(MODULE_CONFIG.key));

  // Memoized derived values
  const activeRow = useMemo(
    () => get(storyCreateData, DATA_PATHS.ACTIVE_ROW, {}),
    [storyCreateData],
  );

  const mode = useMemo(
    () => get(storyCreateData, DATA_PATHS.DESIGN_MODE, DEFAULT_MODE),
    [storyCreateData],
  );

  const isViewMode = useMemo(
    () => get(storyCreateData, DATA_PATHS.DESIGN_MODE) === PREVIEW_MODE,
    [storyCreateData],
  );

  const main = useMemo(() => get(storyCreateData, DATA_PATHS.MAIN, {}), [
    storyCreateData,
  ]);

  const configure = useMemo(
    () => get(storyCreateData, DATA_PATHS.CONFIGURE, {}),
    [storyCreateData],
  );

  const isBlastCampaign = useMemo(
    () => get(storyCreateData, DATA_PATHS.IS_BLAST_CAMPAIGN, false),
    [storyCreateData],
  );

  const channelActive = useMemo(
    () => get(stateCommon, DATA_PATHS.CHANNEL_ACTIVE, {}),
    [stateCommon],
  );

  const isInitDone = useMemo(
    () => get(stateCommon, DATA_PATHS.IS_INIT_DONE, false),
    [stateCommon],
  );

  const isSaveDone = useMemo(
    () => get(stateCommon, DATA_PATHS.IS_SAVE_DONE, false),
    [stateCommon],
  );

  // Role-based permissions
  const roleActions = useMemo(
    () => getStoryRoleActions(activeRow?.accepted_actions),
    [activeRow?.accepted_actions],
  );

  const disableEdit = useMemo(
    () => !roleActions.has(STORY_SETUP_ACTION.EDIT_STORY_NAME),
    [roleActions],
  );

  const hasEditRole = useMemo(() => {
    const userId = activeRow?.c_user_id || getCurrentAccessUserId();
    return validateAction(MENU_CODE.JOURNEY, APP_ACTION.UPDATE, userId);
  }, [activeRow?.c_user_id]);

  // Header visibility logic
  const shouldShowHeader = useMemo(() => {
    if (isEmpty(storyCreateData) || isDoingCopy) {
      return false;
    }

    // Don't show header in preview mode when no active row
    if (mode === PREVIEW_MODE && isEmpty(activeRow)) {
      return false;
    }

    return true;
  }, [storyCreateData, isDoingCopy, mode, activeRow]);

  // Event handlers
  const onChangeSearchParams = useCallback(
    ({ key, value }) => {
      const newSearchParams = new URLSearchParams(location.search);

      if (value === null || value === undefined) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }

      history.push({
        pathname: location.pathname,
        search: newSearchParams.toString(),
      });
    },
    [history, location.pathname, location.search],
  );

  console.log('rerender');

  return useMemo(
    () => ({
      // Display state
      isShowHeader: shouldShowHeader,
      mode,
      isViewMode,

      // Data
      main,
      configure,
      activeRow,
      activeRowDetail,
      channelActive,

      // Flags
      isBlastCampaign,
      isInitDone,
      isSaveDone,

      // Permissions
      disableEdit,
      hasEditRole,

      // Navigation
      moduleConfig,
      currentTab,
      onChangeSearchParams,
    }),
    [
      shouldShowHeader,
      mode,
      isViewMode,
      main,
      configure,
      activeRow,
      activeRowDetail,
      channelActive,
      isBlastCampaign,
      isInitDone,
      isSaveDone,
      disableEdit,
      hasEditRole,
      moduleConfig,
      currentTab,
      onChangeSearchParams,
    ],
  );
};
