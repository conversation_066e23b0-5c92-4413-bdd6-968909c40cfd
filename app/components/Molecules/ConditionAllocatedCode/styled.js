// Libraries
import styled, { css } from 'styled-components';

// Components
import { Collapse, Row, Typography } from '@antscorp/antsomi-ui';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

const { Text } = Typography;
const { blue1, blue1_1: blue11, borderRadius, blue } = globalToken;

export const ConditionItem = styled(Row)`
  position: relative;
  border-radius: ${borderRadius}px;
  border: 1px solid ${blue1};
  margin-left: 0px !important;
  margin-right: 0px !important;
  padding: 10px 40px 10px 2.5px;
  height: 50px;

  .search-popover-overlay.antsomi-popover .antsomi-popover-inner {
    min-width: unset;
    padding: 0px;
  }

  .antsomi-spin-nested-loading
    > div
    > .antsomi-spin.custom-spin
    .antsomi-spin-dot {
    margin: -10px;
  }
`;

export const CollapseStyled = styled(Collapse)`
  padding: 0px 0px 10px !important;

  .antsomi-collapse-item {
    .antsomi-collapse-header {
      padding: 10px;
    }

    .antsomi-collapse-content {
      .antsomi-collapse-content-box {
        padding: 0px;
      }
    }
  }
`;

export const ContentItem = styled(Text)`
  display: block;
  padding: 0px 10px;
  height: 24px;
  line-height: 24px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: ${blue};
  }

  ${({ $isActive }) =>
    $isActive
      ? css`
          background-color: ${blue11} !important;
        `
      : css``};
`;

export const SelectInputStyled = styled.div`
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0px 5px;
  border-bottom: 1px solid ${blue1};
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover {
    background-color: ${blue} !important;
  }
`;
