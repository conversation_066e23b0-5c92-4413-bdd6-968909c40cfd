import styled from 'styled-components';
import { makeStyles } from '@material-ui/core/styles';

export const ContainerNodeContent = styled.div`
  .MuiFormControl-marginNormal {
    margin-top: 0 !important;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
  /* .MuiFormHelperText-filled { */
  /*   font-size: 0.785rem; */
  /*   color: #f44336; */
  /* } */
`;

export const Title = styled.div`
  color: #595959;
  font-size: 11px;
  min-width: ${props => `${props.minWidth}px`};
  /* height: 32px;
  display: flex;
  justify-content: center;
  align-items: center; */
`;

export const TitleTime = styled.div`
  color: ${props => (props.isViewMode ? '#000000' : '#595959')};
  font-size: ${props => (props.isViewMode ? '12px' : '11px')};
  text-align: left;
`;
export const WrapperLabelOption = styled.span`
  font-size: 0.85rem;
  color: rgb(41, 41, 41);
`;
export const WrapperDays = styled.div`
  /* .MuiTypography-body1 {
    font-size: 0.75rem !important;
  } */
  font-size: 1rem;
  display: flex;
  margin-top: 10px;
  & > div {
    margin-right: 20px;
  }

  & > div:last-child {
    margin-right: 0px;
  }
`;

export const useStyles = makeStyles(() => ({
  root: {
    fontSize: '0.75rem !important',
    // marginTop: '4px',
    marginBottom: '20px',

    '& .no-margin-bottom': {
      marginBottom: '0px !important',
    },
    '& .-margin-top-10': {
      marginTop: '-10px !important',
    },
    '& .style-btn-dropdown': {
      maxWidth: '263px',
    },
    '& .private-form__input-wrapper .private-form__control': {
      width: '100%',
    },
  },
  rootNoBottom: {
    marginBottom: '0',
  },
  rootTime: {
    '& .mb-0': {
      marginBottom: '0',
    },
    '& .mr-30': {
      marginRight: '30px',
    },
    '& .hidden-at': {
      visibility: 'hidden',
    },
    '& .hidden-close-date': {
      display: 'none',
    },
  },
  padding: {
    padding: '10px 20px',
    // alignItems: 'center',
    alignItems: 'start',
  },
  paddingX: {
    padding: '0px 20px',
    alignItems: 'start',
  },
  noPaddingBottom: {
    paddingBottom: '0px !important',
  },
  noPaddingTop: {
    padding: '0px 20px 10px',
    alignItems: 'start',
  },
  unsetPaddingTop: {
    padding: '0px 20px 15px',
    alignItems: 'start',
  },
  flexNoWrap: {
    flexWrap: 'nowrap !important',
  },
  customView: {
    maxWidth: '150px',
    // textAlign: 'end',
    marginRight: '20px',
    color: '#000000',
    whiteSpace: 'nowrap',
  },
  smallText: {
    fontSize: '11px !important',
  },
  mdText: {
    fontSize: '12px !important',
    lineHeight: '13px',
  },
  paddingLeft: {
    paddingLeft: '20px',
    position: 'relative',
  },
  colorGray: {
    color: '#666666 !important',
  },
  marginTriggerText: {
    margin: '8px 0',
  },
  marginBottom0: {
    marginBottom: '0px',
  },
  marginBottom10: {
    marginBottom: '10px',
  },
  marginBottom15: {
    marginBottom: '15px',
  },
  textTime: {
    display: 'flex',
    alignItems: 'flex-end',
    marginBottom: '8px',
    paddingLeft: '20px !important',
    padding: '6px 0 7px',
  },
  textSmall: {
    fontSize: '11px',
  },
  marginBottom: {
    marginBottom: '10px',
  },
  wrapperSelectDaysOfWeeks: {
    flexDirection: 'column',
    // paddingLeft: '20px',
    position: 'relative',

    fontSize: '0.813rem !important',
    marginBottom: '20px',

    '& .mb-0 .MuiFormControl-marginNormal': {
      marginBottom: '0px',
    },
  },
  wrapperDays: {
    flexDirection: 'column',
    marginTop: '0px !important',
    '& .MuiInput-formControl': {
      marginTop: '0px !important',
    },
    '& .m-bottom-2': {
      marginBottom: 0,
    },
    '& .padding-left-10': {
      paddingLeft: '10px',
    },
    '& .MuiInput-formControl.Mui-disabled': {
      backgroundColor: '#F5F5F5',
    },
    '& .MuiInput-underline.Mui-disabled:before': {
      borderBottom: 'unset',
      boxShadow: '0 1px 0 0 #e0e0e0',
    },
  },
}));

export const LabelError = styled.div`
  font-size: 0.785rem;
  color: rgb(244, 67, 54);
`;

export const WrapperToggle = styled.div`
  width: 100%;
  display: ${({ visibilityWrapper }) =>
    visibilityWrapper ? 'contents;' : 'none;'};
`;

export const WrapperEndDate = styled.div`
  display: ${({ hidden }) => (hidden ? 'none;' : 'block;')};
`;
export const WrapperLayout = styled.div`
  width: 100%;

  legend,
  .MuiTypography-root {
    font-size: 0.813rem !important;
  }

  span.MuiTypography-root.MuiTypography-body1 {
    font-size: 12px !important;
  }

  .MuiFormGroup-row {
    margin-left: 4px;
  }
`;

export const ErrorText = styled.span`
  color: red;
`;
