import React, { useEffect, useState } from 'react';

// components
import { EditableName } from '@antscorp/antsomi-ui';

// hooks
import { useDebounce } from '../../../hooks';

// utils
import PropTypes from 'prop-types';
import { isFunction } from 'lodash';

const EditableContent = props => {
  const {
    defaultValue,
    onChange,
    isLoading,
    error,
    required = false,
    onBlur,
    readonly = false,
  } = props;
  const [contentInput, setContentInput] = useState(defaultValue);

  useEffect(() => {
    setContentInput(defaultValue);
  }, [defaultValue]);

  const debounceInput = useDebounce(contentInput, 500);

  useEffect(() => {
    if (isFunction(onChange)) {
      onChange(debounceInput);
    }
  }, [debounceInput]);

  return (
    <EditableName
      value={contentInput}
      onChange={value => setContentInput(value)}
      isLoading={isLoading}
      error={error}
      required={required}
      onBlur={onBlur}
      readonly={readonly}
    />
  );
};

EditableContent.propTypes = {
  defaultValue: PropTypes.string,
  onChange: PropTypes.func,
  isLoading: PropTypes.bool,
  error: PropTypes.string,
  required: PropTypes.bool,
  onBlur: PropTypes.func,
  readonly: PropTypes.bool,
};

export default EditableContent;
