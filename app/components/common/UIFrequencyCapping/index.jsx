/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect, useRef } from 'react';
import { useImmer } from 'use-immer';
import PropTypes from 'prop-types';
import Grid from '@material-ui/core/Grid';
import {
  UIButton,
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import FormControl from '@material-ui/core/FormControl';
import RadioGroup from '../../Molecules/RadioGroup';
import UISelect from '../../form/UISelectCondition';
import {
  AlertStyled,
  ContainerNodeContent,
  StrongText,
  // useStyles,
  Title,
  UINumberStyled,
  // WrapperCenter,
  // WrapperGrid,
  WrapperFlex,
  WrapperOr,
} from './styled';
import { useStyles } from '../UISchedulerTrigger/styled';
import {
  PERIOD_LIST,
  PERIOD_MAP,
  MAP_TITLE,
  EVENT_LIST,
  PERIOD_MAP_LIFETIME,
  PERIOD_LIST_LIFETIME,
  LIST_PERSON,
  MAP_PERSON,
  EVENT_MAP,
  PERIOD_LIST_WEB_PERSONALIZE,
  optionFrequencyV2,
} from './utils';
import { safeParse } from '../../../utils/common';
import TRANSLATE_KEY from '../../../messages/constant';
import { getTranslateMessage } from '../../../containers/Translate/util';
import { Checkbox, Icon, Typography } from '@antscorp/antsomi-ui';
import { getPortalId } from '../../../utils/web/cookie';
import {
  TextButton,
  WrapperIcon,
} from '../UIPerformEvent/HeaderCondition/styled';
import produce from 'immer';
import { mapLabelStoryStatus } from '../../../utils/web/processStatus';

const LIST_PORTAL = {
  SANDBOX: 33167,
  ZUS: 564890159,
};
const labelSDK = getTranslateMessage(
  TRANSLATE_KEY._STORY_SETTING_LIMIT_FREQ_SUBMIT_HINT,
  'This feature is supported with the latest SDK version. View the user guide',
);
const labelHere = getTranslateMessage(
  TRANSLATE_KEY._ZALO_TEMP_SELECT_3,
  'here',
);
const initState = () => ({
  cappingSel: 'unlimited',
  value: 1,
  timeValue: 1,
  timeUnit: PERIOD_MAP.daily,
  event: EVENT_MAP.impression,
  object: MAP_PERSON.person,
  isRecordEvent: false,
});

const UIFrequencyCapping = props => {
  const classes = useStyles();
  const [state, setState] = useImmer(initState());
  const [stateV2, setStateV2] = useImmer([]);
  const isWebChannel = Number(props.channelId) === 2;
  const isLifeTime = props.channelId && !isWebChannel;
  const linkSDK =
    'https://docs.antsomi.com/developers-guide/android/getting-started';
  const portalId = getPortalId();
  const optionLimited = isLifeTime
    ? PERIOD_LIST_LIFETIME
    : isWebChannel
    ? PERIOD_LIST_WEB_PERSONALIZE
    : PERIOD_LIST;
  const { activeRow } = props;
  // init Data
  useEffect(() => {
    const parsedV1 = safeParse(props.initData, {});
    const parsedV2 = safeParse(props.initDataV2, []);
    setState(() =>
      Object.keys(parsedV1).length === 0 ? initState() : parsedV1,
    );
    setStateV2(() => (parsedV2.length === 0 ? [] : parsedV2));
  }, [props.componentId]);
  const onChange = event => {
    let { isRecordEvent } = state;
    if (
      !props.isHiddenRecordEvent &&
      event.target.value === 'limited' &&
      [LIST_PORTAL.ZUS].includes(+portalId)
    ) {
      isRecordEvent = true;
    } else {
      isRecordEvent = false;
    }

    setState(draft => {
      draft.cappingSel = event.target.value;
      draft.isRecordEvent = isRecordEvent;
    });

    const data = {
      timeUnit: state.timeUnit,
      value: state.value,
      timeValue: state.timeValue,
      cappingSel: event.target.value,
      event: state.event,
      object: state.object,
      isRecordEvent,
    };
    onOutput(data);
  };
  const onChangeTimes = value => {
    setState(draft => {
      draft.value = value;
    });
    const data = {
      cappingSel: state.cappingSel,
      timeUnit: state.timeUnit,
      timeValue: state.timeValue,
      object: state.object,
      value,
      event: state.event,
      isRecordEvent: state.isRecordEvent,
    };
    onOutput(data);
  };
  const onChangeTimesValue = value => {
    setState(draft => {
      draft.timeValue = value;
    });
    const data = {
      cappingSel: state.cappingSel,
      timeUnit: state.timeUnit,
      timeValue: value,
      value: state.value,
      event: state.event,
      object: state.object,
    };
    onOutput(data);
  };
  const onChangePeriods = value => {
    setState(draft => {
      draft.timeUnit = value;
    });
    const data = {
      cappingSel: state.cappingSel,
      timeUnit: value,
      timeValue: state.timeValue,
      object: state.object,
      value: state.value,
      event: state.event,
      isRecordEvent: state.isRecordEvent,
    };
    onOutput(data);
  };
  const onChangeEventCapping = value => {
    setState(draft => {
      draft.event = value;
    });
    const data = {
      cappingSel: state.cappingSel,
      timeUnit: state.timeUnit,
      value: state.value,
      timeValue: state.timeValue,
      event: value,
      object: state.object,
      isRecordEvent: state.isRecordEvent,
    };
    onOutput(data);
  };
  const onOutput = newState => {
    const output =
      newState.cappingSel === 'unlimited'
        ? {}
        : {
            cappingSel: newState.cappingSel,
            timeUnit: newState.timeUnit,
            value: newState.value,
            timeValue: newState.timeValue,
            event: newState.event,
            object: newState.object,
            isRecordEvent: newState.isRecordEvent,
          };
    props.onChange(output);
    // case Blast để lưu reducer
    if (isWebChannel) {
      triggerOnChangeV2(stateV2);
    }
  };
  const onChangePerson = value => {
    if (value.value === 'ip') {
      // Tạo ra newState từ state cũ
      const newState = produce(state, draft => {
        draft.object = value;
        draft.event = EVENT_MAP.impression;
      });
      setState(() => newState); // cập nhật state chính
      setStateV2(() => []); // cập nhật Frequency2
    } else {
      // Trường hợp không phải 'ip', chỉ update state bình thường
      setState(draft => {
        draft.object = value;
      });
    }
    const data = {
      cappingSel: state.cappingSel,
      timeUnit: state.timeUnit,
      timeValue: state.timeValue,
      object: value,
      value: state.value,
      event: value.value === 'ip' ? EVENT_MAP.impression : state.event,
      isRecordEvent: state.isRecordEvent,
    };
    onOutput(data);
  };
  const onChangeIsRecordEvent = event => {
    const isChecked = event.target.checked;
    setState(draft => {
      draft.isRecordEvent = isChecked;
    });

    const data = {
      cappingSel: state.cappingSel,
      timeUnit: state.timeUnit,
      value: state.value,
      timeValue: state.timeValue,
      event: state.value,
      object: state.object,
      isRecordEvent: isChecked,
    };
    onOutput(data);
  };

  const onOr = () => {
    const newState = produce(stateV2, draft => {
      draft.push({
        value: 1,
        timeValue: 1,
        timeUnit: PERIOD_MAP.daily,
        event: Object.values(EVENT_MAP).find(
          ({ value }) =>
            !stateV2.some(b => b.event.value === value) &&
            state.event.value !== value,
        ),
        object: MAP_PERSON.person,
        isRecordEvent: false,
      });
    });
    setStateV2(() => newState);
    triggerOnChangeV2(newState);
  };

  const onRemove = index => {
    const newState = produce(stateV2, draft => {
      draft.splice(index, 1);
    });
    setStateV2(() => newState);
    triggerOnChangeV2(newState);
  };

  const onChangeV2 = ({ name, value, index }) => {
    setStateV2(draft => {
      draft[index][name] = value;
    });
    const newState = produce(stateV2, draft => {
      draft[index][name] = value;
    });
    triggerOnChangeV2(newState);
  };

  const triggerOnChangeV2 = newState => {
    const result = newState.map(item => {
      return {
        timeUnit: item.timeUnit,
        value: item.value,
        timeValue: item.timeValue,
        event: item.event,
        object: item.object,
        isRecordEvent: item.isRecordEvent,
      };
    });
    props.onChangeV2(result);
  };
  const renderFrequencyV2 = () => {
    const isMaxlengthFrequencyV2 = stateV2.length === EVENT_LIST.length - 1;
    const isIP = state.object.value === 'ip';

    return (
      <>
        {stateV2.map((each, index) => {
          if (!isIP) {
            return props.isViewMode ? (
              <React.Fragment>
                <WrapperOr>
                  {getTranslateMessage(TRANSLATE_KEY._ACT_OR, 'OR')}
                </WrapperOr>
                <WrapperFlex className="p-left-6 p-bottom-1">
                  <StrongText>{`${each.value} ${
                    isWebChannel ? each.event.label : ''
                  } times per ${each.timeUnit.label} ${
                    each.timeUnit.value === 'every' ? each.timeValue : ''
                  }  ${
                    each.timeUnit.value === 'every'
                      ? getTranslateMessage(TRANSLATE_KEY._, 'day(s) per ') +
                        each.object.label
                      : getTranslateMessage(TRANSLATE_KEY._, 'per ') +
                        each.object.label
                  }`}</StrongText>
                </WrapperFlex>
              </React.Fragment>
            ) : (
              // eslint-disable-next-line react/no-array-index-key
              <React.Fragment key={index}>
                <WrapperOr>
                  {getTranslateMessage(TRANSLATE_KEY._ACT_OR, 'OR')}
                </WrapperOr>
                <WrapperFlex>
                  <WrapperFlex
                    style={{ width: '100%' }}
                    className="p-left-6 p-bottom-1"
                  >
                    <>
                      <UINumberStyled
                        name="times"
                        onChange={value =>
                          onChangeV2({ name: 'value', value, index })
                        }
                        value={each.value}
                        min={1}
                        max={1000}
                        width={65}
                      />
                      <div className="m-x-2">
                        <UISelect
                          use="tree"
                          isSearchable={false}
                          options={optionFrequencyV2(
                            EVENT_LIST,
                            each.event.value,
                            stateV2,
                            state,
                          )}
                          value={each.event}
                          onChange={value =>
                            onChangeV2({ name: 'event', value, index })
                          }
                          fullWidthPopover={false}
                          labelWidth="auto"
                        />
                      </div>

                      <Title className="m-x-2">{MAP_TITLE.infoTimePer}</Title>
                      <UISelect
                        use="tree"
                        isSearchable={false}
                        options={optionLimited}
                        value={each.timeUnit}
                        onChange={value =>
                          onChangeV2({ name: 'timeUnit', value, index })
                        }
                        fullWidthPopover={false}
                        labelWidth="auto"
                      />
                      {each.timeUnit.value === 'every' ? (
                        <React.Fragment>
                          <Title className="m-x-2">
                            <UINumberStyled
                              name="timesValue"
                              onChange={value =>
                                onChangeV2({ name: 'timesValue', value, index })
                              }
                              value={each.timeValue}
                              min={1}
                              max={999}
                              defaultValue={1}
                              width={60}
                            />
                          </Title>

                          <Title className="m-x-2">
                            {getTranslateMessage(TRANSLATE_KEY._, 'day(s) per')}
                          </Title>
                          <UISelect
                            use="tree"
                            isSearchable={false}
                            options={[LIST_PERSON[0]]}
                            value={each.object}
                            fullWidthPopover={false}
                            labelWidth="auto"
                          />
                        </React.Fragment>
                      ) : (
                        <>
                          <Title className="m-x-2">
                            {getTranslateMessage(TRANSLATE_KEY._, 'per')}
                          </Title>
                          <UISelect
                            use="tree"
                            isSearchable={false}
                            options={[LIST_PERSON[0]]}
                            value={each.object}
                            fullWidthPopover={false}
                            labelWidth="auto"
                          />
                        </>
                      )}
                    </>
                  </WrapperFlex>
                  <WrapperIcon
                    onClick={() => onRemove(index)}
                    style={{ cursor: 'pointer', marginLeft: '30px' }}
                  >
                    <Icon
                      type="icon-ants-remove-light"
                      size={16}
                      color="#005eb8"
                    />
                  </WrapperIcon>
                </WrapperFlex>
                {each.event.value === EVENT_MAP.submit_optin.value && (
                  <WrapperFlex style={{ margin: '5px 0px 5px 24px' }}>
                    <Typography.Text>
                      {labelSDK}
                      &nbsp;
                      <Typography.Link href={linkSDK} target="_blank">
                        {labelHere}
                      </Typography.Link>
                    </Typography.Text>
                  </WrapperFlex>
                )}
              </React.Fragment>
            );
          }
          return null;
        })}

        {!isMaxlengthFrequencyV2 && !isIP && !props.isViewMode && (
          <UIButton
            theme="outline"
            borderRadius="1rem"
            iconName="add"
            onClick={onOr}
            reverse
            style={{
              color: '#005eb8',
              height: '27px',
              minWidth: '65px',
              margin: '5px 0px 5px 24px',
            }}
          >
            <TextButton>
              {getTranslateMessage(TRANSLATE_KEY._ACT_OR, 'OR')}
            </TextButton>
          </UIButton>
        )}
      </>
    );
  };
  return (
    <ContainerNodeContent
      className={`${classes.root} ${classes.noPaddingBottom} ${
        classes.marginBottom10
      }`}
      data-test={props['data-test'] || 'frequency-capping'}
    >
      <Grid container className={props.isNoPadding ? '' : classes.paddingX}>
        {props.isShowLabel && (
          <Grid
            item
            sm={2}
            className={`${classes.customView} titl-general-setting`}
            style={props.styledLabel || {}}
          >
            {props.label || MAP_TITLE.generalSettings}:
          </Grid>
        )}
        <Grid container item sm={props.isCollapsed ? 9 : 8}>
          <WrapperDisable disabled={props.disabled}>
            <div component="fieldset">
              <RadioGroup
                label={MAP_TITLE.infoStorySetting}
                value={state.cappingSel}
                onChange={onChange}
                name="capping1"
                isViewMode={props.isViewMode}
                options={[
                  {
                    value: 'unlimited',
                    disabled: props.disabled,
                    label: getTranslateMessage(
                      TRANSLATE_KEY._STORY_SETTING_NO_FREQ,
                      'Unlimited frequency',
                    ),
                  },
                  {
                    value: 'limited',
                    disabled: props.disabled,
                    label: getTranslateMessage(
                      TRANSLATE_KEY._STORY_SETTING_LIMIT_FREQ,
                      'Limited frequency',
                    ),
                  },
                ]}
              />

              {state.cappingSel === 'limited' && (
                <>
                  {/* warning journey status active with setting frequency with web personalization */}
                  {activeRow?.status === mapLabelStoryStatus[1].id &&
                    !props.isViewMode &&
                    isWebChannel && (
                      <AlertStyled
                        message={MAP_TITLE.alterMessage}
                        showIcon
                        type="warning"
                        variant="outline"
                      />
                    )}
                  <WrapperFlex className="p-left-6 p-bottom-1">
                    {props.isViewMode ? (
                      <StrongText>{`${state.value} ${
                        isWebChannel ? state.event.label : ''
                      } times per ${state.timeUnit.label} ${
                        state.timeUnit.value === 'every' ? state.timeValue : ''
                      }  ${
                        state.timeUnit.value === 'every'
                          ? getTranslateMessage(
                              TRANSLATE_KEY._,
                              'day(s) per ',
                            ) + state.object.label
                          : getTranslateMessage(TRANSLATE_KEY._, 'per ') +
                            state.object.label
                      }`}</StrongText>
                    ) : (
                      <>
                        <UINumberStyled
                          name="times"
                          onChange={onChangeTimes}
                          value={state.value}
                          min={1}
                          max={1000}
                          width={65}
                        />
                        {isWebChannel && (
                          <div className="m-x-2">
                            <UISelect
                              use="tree"
                              isSearchable={false}
                              options={optionFrequencyV2(
                                EVENT_LIST,
                                state.event.value,
                                stateV2,
                              )}
                              disabled={state.object.value === 'ip'}
                              value={state.event}
                              onChange={onChangeEventCapping}
                              fullWidthPopover={false}
                              labelWidth="auto"
                            />
                          </div>
                        )}

                        <Title className="m-x-2">{MAP_TITLE.infoTimePer}</Title>
                        <UISelect
                          use="tree"
                          isSearchable={false}
                          options={optionLimited}
                          value={state.timeUnit}
                          onChange={onChangePeriods}
                          fullWidthPopover={false}
                          labelWidth="auto"
                          // width={100}
                          // placeholderTranslateCode={props.placeholderTranslateCode}
                        />

                        {state.timeUnit.value === 'every' && props.channelId ? (
                          <React.Fragment>
                            <Title className="m-x-2">
                              <UINumberStyled
                                name="timesValue"
                                onChange={onChangeTimesValue}
                                value={state.timeValue}
                                min={1}
                                max={999}
                                defaultValue={1}
                                width={60}
                              />
                            </Title>

                            <Title className="m-x-2">
                              {getTranslateMessage(
                                TRANSLATE_KEY._,
                                'day(s) per',
                              )}
                            </Title>
                            <UISelect
                              use="tree"
                              isSearchable={false}
                              options={
                                state.event.value === EVENT_MAP.impression.value
                                  ? LIST_PERSON
                                  : [LIST_PERSON[0]]
                              }
                              value={state.object}
                              onChange={onChangePerson}
                              fullWidthPopover={false}
                              labelWidth="auto"
                              // width={100}
                              // placeholderTranslateCode={props.placeholderTranslateCode}
                            />
                          </React.Fragment>
                        ) : (
                          <>
                            <Title className="m-x-2">
                              {getTranslateMessage(TRANSLATE_KEY._, 'per')}
                            </Title>
                            <UISelect
                              use="tree"
                              isSearchable={false}
                              options={
                                state.event.value === EVENT_MAP.impression.value
                                  ? LIST_PERSON
                                  : [LIST_PERSON[0]]
                              }
                              value={state.object}
                              onChange={onChangePerson}
                              fullWidthPopover={false}
                              labelWidth="auto"
                              // width={100}
                              // placeholderTranslateCode={props.placeholderTranslateCode}
                            />
                          </>
                        )}
                      </>
                    )}
                  </WrapperFlex>
                  {state.event.value === EVENT_MAP.submit_optin.value &&
                    isWebChannel && (
                      <WrapperFlex style={{ margin: '5px 0px 5px 24px' }}>
                        <Typography.Text>
                          {labelSDK}
                          &nbsp;
                          <Typography.Link href={linkSDK} target="_blank">
                            {labelHere}
                          </Typography.Link>
                        </Typography.Text>
                      </WrapperFlex>
                    )}
                  {isWebChannel && renderFrequencyV2()}

                  {!props.isHiddenRecordEvent && (
                    <div className="m-x-7 m-y-1">
                      <Checkbox
                        name="onCheckRecordEvent"
                        checked={state.isRecordEvent}
                        disabled={props.isViewMode}
                        onChange={onChangeIsRecordEvent}
                      >
                        <div className="d-flex" style={{ gap: 10 }}>
                          <span>
                            {getTranslateMessage(
                              TRANSLATE_KEY._,
                              'Record events when surpassing this limit',
                            )}
                          </span>
                          <UITippy
                            content={getTranslateMessage(
                              TRANSLATE_KEY._,
                              'When a message could not be sent due to this limit, Journey Frequency Capping event will be recorded',
                            )}
                          >
                            <Icon
                              type="icon-ants-info-outline"
                              size={20}
                              color="#595959"
                            />
                          </UITippy>
                        </div>
                      </Checkbox>
                    </div>
                  )}
                </>
              )}
            </div>
          </WrapperDisable>
        </Grid>
      </Grid>
    </ContainerNodeContent>
  );
};
UIFrequencyCapping.propTypes = {
  onChange: PropTypes.func,
  name: PropTypes.string,
  initData: PropTypes.object,
  isShowLabel: PropTypes.bool,
  isNoPadding: PropTypes.bool,
};
UIFrequencyCapping.defaultProps = {
  onChange: () => {},
  name: '',
  initData: {},
  disabled: false,
  isShowLabel: true,
  isNoPadding: false,
};

export default UIFrequencyCapping;
