/* eslint-disable no-prototype-builtins */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
// Libraries
import React, { useMemo, useRef, useCallback, memo } from 'react';
import { connect, useDispatch } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { cloneDeep, isFunction, debounce, find, partial } from 'lodash';
import PropTypes from 'prop-types';
import produce from 'immer';

// Services
import JourneyServices from 'services/Journey';

// Actions
import { startPersonalizations } from '../../common/UIEditorPersonalization/WrapperPersonalization/libs/action';

// Selectors
import { makeSelectActiveIdNodeData } from '../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';

// Translations
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Components
import SelectTree from 'components/form/UISelectCondition';
import ConditionAllocatedCode from '../ConditionAllocatedCode';
import { ViewDetailsInformationIcon } from '@antscorp/antsomi-ui/es/components/icons';
import WrapperDisable from '../../common/WrapperDisable';
import ModalConfigFormat from '../../common/Modal/ModalConfigFormat';
import {
  Checkbox,
  Flex,
  Input,
  Tooltip,
  TAG_TYPE,
  Select,
  ALLOCATED_CODE,
} from '@antscorp/antsomi-ui';
import { Label, StyledAutoComplete, StyledModal, WrapperBody } from './styled';
import { UITippy, UIWrapperDisable } from '@xlab-team/ui-components';
import { FormHelperText } from '@material-ui/core';
import AceEditor from 'react-ace';
import { translate, translations } from '@antscorp/antsomi-locales';

// Utils
import {
  getAllocatedIncludeTypeTooltip,
  isValidateAllocatedCode,
} from './utils';
import { SET_OBJECT_WIDGET_AND_PRODUCT_TEMPLATE } from 'components/common/UIEditorPersonalization/utils';
import {
  DATA_ATTR_TYPE,
  DATA_ATTR_WITH_TYPE,
  checkErrorFunction,
  getPersonalizeCodeContentSource,
  toEntryAPICreate,
  toEntryAPIList,
  getTagDisplayName,
  initAllocatedCode,
} from 'components/common/UIEditorPersonalization/components/PopupPersonalization/utils';
import { getObjectPropSafely } from '../../../utils/common';
import { getClassNameColor } from '../../common/UIEditorPersonalization/utils.extends';
import { MAP_DATA_TYPE } from '../../../services/Abstract.data';
import { initDisplayFormat } from '../../common/Modal/ModalConfigFormat/utils';
import { serializeLabelToCodeAttr } from '../../../utils/web/utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { dtoCustomFunction } from '../../common/UIEditorPersonalization/WrapperPersonalization/libs/utils.dto';

// Ace editor
import 'ace-builds/webpack-resolver';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-css';
import 'ace-builds/src-noconflict/theme-tomorrow';
import 'ace-builds/src-noconflict/ext-language_tools';

// Constants
import {
  DELIMITER_OPTS,
  INCLUDE_TYPE,
  INCLUDE_TYPE_OPTS,
  initCustomFunction,
} from './constants';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import {
  DATA_GROUPS,
  JOURNEY_CUSTOM_FN_CODE,
} from '../../common/UIEditorPersonalization/WrapperPersonalization/constants';

const { CUSTOM_FN, PROMOTION_CODE, OBJECT_WIDGET, EVENT } = TAG_TYPE;

const MAP_TITLE = {
  boxTitle: getTranslateMessage(
    TRANSLATE_KEY._BOX_TITL_ADD_PERSONALIZE,
    'Add Personalization',
  ),
  personalizationType: translate(
    translations._TITL_PERSONALIZATION_TYPE,
    'Content Source',
  ),
  personalizationAttr: translate(
    translations._TITL_PERSONALIZE_ATTR,
    'Attribute',
  ),
  personalizationName: getTranslateMessage(TRANSLATE_KEY._, 'Name'),
  defaultValue: getTranslateMessage(
    TRANSLATE_KEY._TITL_PERSONALIZE_DEFAULT,
    'Default Value',
  ),
  personalizationCode: translate(
    translations._TITL_PERSONALIZATION_CODE,
    'Merge Code',
  ),
  personalizationDisplayName: translate(
    translations._PERSONALIZE_TAG_NAME,
    'Tag Display Name',
  ),
  personalizationTag: translate(
    translations._TITL_PERSONALIZATION_TAG,
    'Merge Tag',
  ),
  promotionPool: translate(
    translations._TITL_PERSONALIZATION_TYPE_POOL,
    'Promotion Pools',
  ),
  promotionCodeAttr: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Promotion Code Attribute',
  ),
  outPutDataType: getTranslateMessage(TRANSLATE_KEY._, 'Output Data Type'),
  displayFormat: translate(translations._RENDER_FORMAT, 'Display format'),
  outPutFormat: getTranslateMessage(TRANSLATE_KEY._, 'Output Format'),
  saveTemplate: getTranslateMessage(TRANSLATE_KEY._, 'Save as Template'),
  function: getTranslateMessage(TRANSLATE_KEY._, 'Function'),
  include: translate(translations._PERSONALIZE_PROCESS_CODE_INCLUDE, 'Include'),
  delimiter: translate(
    translations._PERSONALIZE_PROCESS_CODE_DELIMETER,
    'Separate codes by',
  ),
  replaceSpecChars: translate(
    translations._,
    'Replace special characters with system-defined escape sequences to ensure valid JSON format',
  ),
};

const stylePopover = { position: 'absolute', top: 45, left: 50 };

const PATH =
  'app/components/Molecules/Personalizationable/ModalPersonalization.jsx';

const ModalPersonalization = props => {
  // Props
  const {
    design,
    dataState,
    disabled,
    open,
    onCancel,
    listTemplateCustomFn,
    customFunction,
    showMergeTagDisplay,
    useAllocatedCode,
    onAddTag,
    activeNode,
    selectedProperties,
    showReplaceSpecialChars,
    onChangeReplaceSpecialChars,
    onChangeDisplayName,
    onChangeAllocatedCode,
    onChangeSelectedProperties,
    onChangeCustomFunction,
  } = props;
  const {
    personalType,
    isReplaceSpecialChars,
    displayName,
    attribute,
    allocatedCode,
  } = selectedProperties || {};

  // Hooks
  const ref = useRef(null);
  const dispatch = useDispatch();

  const {
    personalizationType,
    personalizationData,
    promotionCodeAttr,
  } = dataState;

  const contentSourceList = useMemo(() => {
    if (useAllocatedCode) {
      const newList = cloneDeep(personalizationType.list);
      newList.push(DATA_GROUPS[ALLOCATED_CODE]);
      return newList;
    }

    return personalizationType.list;
  }, [useAllocatedCode, personalizationType.list]);

  const selectedPersonalType = useMemo(
    () => find(contentSourceList, ['value', personalType?.value]),
    [personalType?.value, contentSourceList],
  );

  const {
    isCustomFn,
    isPromotionCode,
    isObjectWidget,
    isProductTemplate,
    isAllocatedCode,
    isExistPersType,
    persTypeValue: personalTypeValue,
  } = useMemo(() => {
    const { value: persTypeValue } = personalType || {};

    return {
      persTypeValue,
      isExistPersType: !!persTypeValue,
      isAllocatedCode: persTypeValue === ALLOCATED_CODE,
      isCustomFn: persTypeValue === CUSTOM_FN,
      isPromotionCode: persTypeValue === PROMOTION_CODE,
      isObjectWidget: persTypeValue === OBJECT_WIDGET,
      isProductTemplate: persTypeValue === 'productTemplate',
    };
  }, [personalType?.value]);

  const colHeaderAtt = useMemo(() => {
    if (isPromotionCode) return MAP_TITLE.promotionPool;
    if (isCustomFn) return MAP_TITLE.personalizationDisplayName;
    if (isAllocatedCode) return MAP_TITLE.include;

    return MAP_TITLE.personalizationAttr;
  }, [isPromotionCode, isCustomFn, isAllocatedCode]);

  const isShowDisplayFormat = useMemo(() => {
    if (isAllocatedCode) return false;

    return DATA_ATTR_WITH_TYPE.includes(
      selectedProperties?.displayFormat?.dataType,
    );
  }, [isAllocatedCode, selectedProperties?.displayFormat?.dataType]);

  const onDebounceCallback = useCallback(
    debounce((callback, event = {}) => {
      callback(event);
    }, 350),
    [],
  );

  const dispatchFetchingListCustomFn = useCallback(() => {
    dispatch(
      startPersonalizations({
        [JOURNEY_CUSTOM_FN_CODE]: {
          savedKey: JOURNEY_CUSTOM_FN_CODE,
          serviceFn: JourneyServices.personalization.getListCustom,
          dtoFn: dtoCustomFunction,
          payload: null,
          normalizeData: toEntryAPIList,
          isForceUpdateNew: true,
        },
      }),
    );
  }, [dispatch]);

  const handleInsertText = () => {
    let isValid = true;

    try {
      // validate Function
      if (ref && ref.current) {
        isValid = checkErrorFunction(
          ref.current.editor.session.getAnnotations(),
        );
        if (!isValid) {
          onChangeCustomFunction(draft => {
            draft.errorsFunction = 'Invalid Function';
          });
        }
      }

      // TH create new template
      if (isValid) {
        if (isCustomFn && customFunction.saveAsTemplate) {
          const params = {
            data: toEntryAPICreate(customFunction),
          };
          JourneyServices.personalization
            .create(params)
            .then(res => {
              if (res.code === 200 && res.data) {
                onAddTag({
                  nodeId: activeNode?.nodeId,
                  selectedProperties,
                  customFunction,
                  design,
                });

                dispatchFetchingListCustomFn();
              } else if (
                res.code === 500 &&
                res.codeMessage === '_NOTIFICATION_NAMESAKE'
              ) {
                onChangeCustomFunction(draft => {
                  draft.errors = getTranslateMessage(
                    res.codeMessage,
                    'This name already existed',
                  );
                });
              }
            })
            .catch(err => {
              if (!err.isCanceled) {
                addMessageToQueue({
                  path: PATH,
                  func: 'handleInsertText',
                  data: err.stack,
                });

                // eslint-disable-next-line no-console
                console.warn('err', err);
              }
            });
        }
        // TH update template
        else if (
          isCustomFn &&
          customFunction.templateId &&
          !customFunction.saveAsTemplate
        ) {
          const params = {
            data: toEntryAPICreate(customFunction),
            templateId: customFunction.templateId,
          };
          JourneyServices.personalization
            .update(params)
            .then(res => {
              if (res.code === 200 && res.data) {
                onAddTag({
                  selectedProperties,
                  nodeId: activeNode?.nodeId,
                  customFunction,
                  design,
                });

                dispatchFetchingListCustomFn();
              } else if (
                res.code === 500 &&
                res.codeMessage === '_NOTIFICATION_NAMESAKE'
              ) {
                onChangeCustomFunction(draft => {
                  draft.errors = getTranslateMessage(
                    res.codeMessage,
                    'This name already existed',
                  );
                });
              }
            })
            .catch(err => {
              if (!err.isCanceled) {
                addMessageToQueue({
                  path: PATH,
                  func: 'handleInsertText',
                  data: err.stack,
                });

                // eslint-disable-next-line no-console
                console.warn('err', err);
              }
            });
        } else {
          onAddTag({
            nodeId: activeNode?.nodeId,
            selectedProperties,
            customFunction,
            design,
          });
        }
      }
    } catch (error) {
      addMessageToQueue({ path: PATH, func: 'handleInsertText', data: error });
    }
  };

  const handleDebounceInsertText = useCallback(
    partial(onDebounceCallback, handleInsertText),
    [onDebounceCallback, handleInsertText],
  );

  const isShowContentSourceIndex = useMemo(() => {
    if (Array.isArray(selectedProperties.contentSources) && personalTypeValue) {
      return (
        selectedProperties.contentSources.findIndex(
          cs => cs.groupId === personalTypeValue,
        ) !== -1
      );
    }
    return false;
  }, [selectedProperties.contentSources, personalTypeValue]);

  const { sourceAttribute, classNameTag = '' } = useMemo(() => {
    let result = [];

    result = getObjectPropSafely(() => {
      if (
        isExistPersType &&
        personalizationData &&
        personalizationData.hasOwnProperty(personalTypeValue)
      ) {
        const dataGroupAttrs = personalizationData[personalTypeValue];
        return dataGroupAttrs && dataGroupAttrs.list;
      }
      return [];
    }, []);

    const className = getClassNameColor(selectedProperties);

    return { sourceAttribute: result, classNameTag: className };
  }, [
    isExistPersType,
    personalTypeValue,
    selectedProperties.status,
    JSON.stringify(personalizationData),
  ]);

  const personalizationCode = useMemo(() => {
    const attBracketCode = getObjectPropSafely(() => attribute?.bracketed_code);

    if (isPromotionCode) {
      return `#{${personalTypeValue}.${attBracketCode}.${getObjectPropSafely(
        () => selectedProperties.promotionCodeAttr.value,
      )}}`;
    }

    if (isObjectWidget || isProductTemplate) {
      return `#{${personalTypeValue}.${attBracketCode}}`;
    }

    if (isCustomFn) {
      return `#{${personalTypeValue}.${serializeLabelToCodeAttr(
        customFunction.personalizationName || '',
      )}}`;
    }

    if (
      Array.isArray(selectedProperties.contentSources) &&
      selectedProperties.contentSources.some(
        contentSource => contentSource.groupId === personalTypeValue,
      )
    ) {
      const tag = getPersonalizeCodeContentSource({
        personalizeType: personalTypeValue,
        attributeBracket: attBracketCode,
        csIndexAttribute: selectedProperties.csIndexAttribute,
      });

      return tag;
    }

    return `#{${personalTypeValue}.${attBracketCode}||"${getObjectPropSafely(
      () => selectedProperties.value,
    )}"}`;
  }, [
    isCustomFn,
    isPromotionCode,
    isObjectWidget,
    isProductTemplate,
    personalTypeValue,
    attribute,
    selectedProperties,
    customFunction.personalizationName,
    selectedProperties.csIndexAttribute,
  ]);

  const mergeCode = useMemo(
    () => personalizationCode?.replace(/&nbsp;/gm, ' '),
    [personalizationCode],
  );

  const checkAttributeMatch = (attr, targetValue) => {
    if (attr?.value === targetValue) return true; // Nếu value trùng thì return true ngay
    return (
      attr?.options?.some(option => checkAttributeMatch(option, targetValue)) ||
      false
    );
  };

  const isValidateSelectedAttribute = useMemo(() => {
    return (sourceAttribute || []).some(attr =>
      checkAttributeMatch(attr, attribute?.value),
    );
  }, [sourceAttribute, attribute]);

  const canApplyTag = useMemo(() => {
    if (
      !displayName ||
      !selectedPersonalType?.value ||
      (!isValidateSelectedAttribute && !isCustomFn && !isAllocatedCode)
    ) {
      return false;
    }

    if (isAllocatedCode) {
      return isValidateAllocatedCode(allocatedCode);
    }

    return true;
  }, [
    displayName,
    selectedPersonalType,
    isCustomFn,
    isAllocatedCode,
    allocatedCode,
    isValidateSelectedAttribute,
  ]);

  const onChangePersonalType = option => {
    try {
      const newPersonalType = {
        value: option.value,
        label: option.label,
      };

      if (option.value === CUSTOM_FN) {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.displayFormat.dataType = '';
            draft.displayFormat.format = {};
            draft.displayName = 'Custom';
            draft.personalType = newPersonalType;
            draft.allocatedCode = initAllocatedCode();
          }),
        );
      } else if (option.value === ALLOCATED_CODE) {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.displayFormat.dataType = '';
            draft.displayFormat.format = {};
            draft.displayName = getTagDisplayName(attribute, newPersonalType);
            draft.personalType = newPersonalType;
            draft.allocatedCode = initAllocatedCode();
          }),
        );
      } else {
        const foundItem = getObjectPropSafely(() => {
          if (
            option &&
            typeof option.value !== 'undefined' &&
            personalizationData &&
            personalizationData.hasOwnProperty(option.value)
          ) {
            return personalizationData[option.value].list;
          }

          return [];
        }, []);

        if (option && foundItem) {
          onChangeSelectedProperties(
            produce(selectedProperties, draft => {
              draft.personalType = newPersonalType;
              draft.allocatedCode = initAllocatedCode();

              if (foundItem && foundItem.length) {
                const tempAttrs = {
                  value: foundItem[0].value,
                  label: foundItem[0].label,
                  bracketed_code: foundItem[0].bracketed_code,
                  ...foundItem[0],
                };

                if (option.value === EVENT) {
                  tempAttrs.name = foundItem[0].name;
                }

                draft.attribute = tempAttrs;
                draft.displayName = getTagDisplayName(
                  tempAttrs,
                  newPersonalType,
                );
                draft.displayFormat.dataType = tempAttrs.itemDataType;
                draft.displayFormat.format = initDisplayFormat(
                  tempAttrs.itemDataType,
                );
                if (option.value === PROMOTION_CODE) {
                  draft.displayFormat.dataType =
                    selectedProperties.promotionCodeAttr.dataType;
                  draft.displayFormat.format = initDisplayFormat(
                    selectedProperties.promotionCodeAttr.dataType,
                  );
                }
              } else {
                draft.attribute = {
                  value: '',
                  label: '',
                  bracketed_code: '',
                };
                draft.displayName = getTagDisplayName(
                  attribute,
                  newPersonalType,
                );
              }
            }),
          );
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangePersonalType',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeAttribute = option => {
    try {
      if (option) {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.attribute = option;
            draft.status = '';
            draft.statusMsg = '';
            draft.displayName = option?.label || '';

            if (!isPromotionCode) {
              draft.displayFormat.dataType = option.itemDataType;
              draft.displayFormat.format = initDisplayFormat(
                option.itemDataType,
              );
            }
          }),
        );
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangeAttribute',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangePromotionCode = option => {
    try {
      if (option) {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.promotionCodeAttr = option;
            draft.displayFormat.dataType = option.dataType;
            draft.displayFormat.format = initDisplayFormat(option.dataType);
          }),
        );
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangePromotionCode',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeDefaultValue = useCallback(
    event => {
      try {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.value = event?.target?.value;
          }),
        );
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'onChangeInputValue',
          data: error.stack,
        });

        // eslint-disable-next-line no-console
        console.log(error);
      }
    },
    [selectedProperties, onChangeSelectedProperties],
  );

  const onDebounceChangeDefaultValue = useCallback(
    partial(onDebounceCallback, onChangeDefaultValue),
    [onDebounceCallback, onChangeDefaultValue],
  );

  const setRawStyleEditorOutput = value => {
    onChangeCustomFunction(draft => {
      draft.formular = value;
      draft.errorsFunction = '';
    });
  };

  const onChangedataType = option => {
    onChangeCustomFunction(draft => {
      draft.dataType = option.value;
      draft.displayFormat = initDisplayFormat(option.value);
    });
  };
  const onChangeModalConfig = value => {
    onChangeCustomFunction(draft => {
      draft.displayFormat = value;
    });
  };

  const onChangeDisplayFormat = value => {
    try {
      onChangeSelectedProperties(
        produce(selectedProperties, draft => {
          draft.displayFormat.format = value;
        }),
      );
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangeDisplayFormat',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeNamePersonal = value => {
    if (value?.length >= 255) {
      onChangeCustomFunction(draft => {
        draft.errors = getTranslateMessage(
          TRANSLATE_KEY._NAME_RULE_INVALID_MAX_LENGTH,
          'Invalid name! Name contains no more than 255 characters',
        );
      });
    } else {
      onChangeCustomFunction(draft => {
        draft.personalizationName = value;
        draft.errors = '';
      });
      onChangeDisplayName(value);
    }
  };

  const onChangeSelectName = (_event, value) => {
    if (value) {
      onChangeCustomFunction(draft => {
        draft.personalizationName = value.personalizationName;
        draft.displayFormat = value.displayFormat;
        draft.dataType = value.dataType;
        draft.formular = value.formular;
        draft.templateId = value.template_id;
        draft.templateCode = value.template_code;
        draft.errors = '';
      });
      onChangeDisplayName(value.personalizationName);
    }
  };

  const onSaveAsTemplate = () => {
    onChangeCustomFunction(draft => {
      draft.saveAsTemplate = !customFunction.saveAsTemplate;
      draft.errors = '';
    });
  };

  const handleChangeTagDisplayName = useCallback(
    event => {
      if (event.target && isFunction(onChangeDisplayName)) {
        onChangeDisplayName(event.target.value);
      }
    },
    [onChangeDisplayName],
  );
  const handleDebounceChangeTagDisplayName = useCallback(
    partial(onDebounceCallback, handleChangeTagDisplayName),
    [onDebounceCallback, handleChangeTagDisplayName],
  );

  const handleClickReplaceSpecChars = () => {
    if (isFunction(onChangeReplaceSpecialChars)) {
      onChangeReplaceSpecialChars(!isReplaceSpecialChars);
    }
  };

  const renderTagDisplayName = () => {
    return (
      <td colSpan={2} style={{ verticalAlign: 'top' }}>
        <Flex vertical gap={8}>
          <Label>
            {MAP_TITLE.personalizationDisplayName}
            <span style={{ color: globalToken.colorError }}>&nbsp;*</span>
          </Label>

          <Input
            value={displayName}
            maxLength={255}
            onChange={handleDebounceChangeTagDisplayName}
          />
        </Flex>
      </td>
    );
  };

  const renderMergeTag = () => {
    return (
      <td style={{ verticalAlign: 'top' }}>
        <Flex vertical gap={8}>
          <Label>{MAP_TITLE.personalizationTag}</Label>

          <div style={{ wordBreak: 'break-all' }} data-merge-code={mergeCode}>
            <UITippy content={displayName}>
              <button
                type="button"
                className={`insert-word ${
                  isCustomFn ? 'custom' : classNameTag
                }`}
                style={{
                  maxWidth: '100%',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {displayName}
              </button>
            </UITippy>
          </div>
        </Flex>
      </td>
    );
  };

  const renderMergeTagDisplay = () => {
    if (!showMergeTagDisplay) return null;

    return (
      <tr>
        {renderTagDisplayName()}
        {renderMergeTag()}
      </tr>
    );
  };

  const renderColumnSecondHead = () => {
    // Custom function [column customFunction name]
    if (isCustomFn) {
      return (
        <Flex vertical>
          <StyledAutoComplete
            showSearch
            value={displayName}
            options={listTemplateCustomFn.map(i => ({
              ...i,
              value: i.template_code,
              label: i.template_name,
            }))}
            onSelect={onChangeSelectName}
            onSearch={onChangeNamePersonal}
          />

          <FormHelperText
            id="component-helper-text"
            error={!!customFunction.errors}
            style={{ width: '350px' }}
          >
            {customFunction.errors}
          </FormHelperText>
        </Flex>
      );
    }

    // For case allocated code [column "Include"]
    if (isAllocatedCode) {
      return (
        <Select
          value={allocatedCode.includeType}
          options={INCLUDE_TYPE_OPTS}
          optionRender={option => (
            <Tooltip
              destroyTooltipOnHide
              mouseEnterDelay={0.5}
              placement="top"
              title={getAllocatedIncludeTypeTooltip(option.value)}
              zIndex={9999}
            >
              {option.label}
            </Tooltip>
          )}
          onChange={newInclude =>
            onChangeAllocatedCode('CHANGE_INCLUDE_TYPE', newInclude)
          }
        />
      );
    }

    // [column attribute name]
    return (
      <SelectTree
        use="tree"
        isSearchable
        options={sourceAttribute}
        value={attribute}
        onChange={onChangeAttribute}
        placeholder={getTranslateMessage(
          TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
          'Select an item',
        )}
        fullWidthPopover
        errors={!isValidateSelectedAttribute ? ['Invalid Selected'] : []}
      />
    );
  };

  const renderColumnThirdHead = () => {
    // Promotion tag
    if (isPromotionCode) {
      return (
        <td>
          <Flex vertical gap={8}>
            <Label>{MAP_TITLE.promotionCodeAttr}</Label>

            <SelectTree
              use="tree"
              isSearchable
              options={promotionCodeAttr.list}
              value={selectedProperties.promotionCodeAttr}
              onChange={onChangePromotionCode}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
            />
          </Flex>
        </td>
      );
    }

    // For case Process allocated code tag
    if (isAllocatedCode) {
      if (allocatedCode?.includeType !== INCLUDE_TYPE.ALL_UNIQUE_CODES)
        return null;

      return (
        <td>
          <Flex vertical gap={8}>
            <Label>
              {MAP_TITLE.delimiter}
              <span style={{ color: globalToken.colorError }}>&nbsp;*</span>
            </Label>

            <Select
              value={allocatedCode.delimiter}
              options={DELIMITER_OPTS}
              onChange={newDelimiter =>
                onChangeAllocatedCode('CHANGE_DELIMITER', newDelimiter)
              }
            />
          </Flex>
        </td>
      );
    }

    // Personal tag type
    if (
      !SET_OBJECT_WIDGET_AND_PRODUCT_TEMPLATE.has(
        selectedProperties.personalType.value,
      )
    ) {
      return (
        <td>
          <Flex vertical gap={8}>
            <Flex align="center" gap={4}>
              <Label>{MAP_TITLE.defaultValue}</Label>

              <Tooltip title="Value to be displayed if there's no dynamic content">
                <ViewDetailsInformationIcon size={14} />
              </Tooltip>
            </Flex>

            <UIWrapperDisable disabled={isShowContentSourceIndex}>
              <Input
                placeholder="Enter value"
                value={getObjectPropSafely(() =>
                  selectedProperties.value.replace(/&nbsp;/gm, ' '),
                )}
                onChange={onDebounceChangeDefaultValue}
              />
            </UIWrapperDisable>
          </Flex>
        </td>
      );
    }

    return null;
  };

  return (
    <div id="antsomi-popover-customer" style={stylePopover}>
      <StyledModal
        centered
        title={MAP_TITLE.boxTitle}
        open={open}
        width="650px"
        onCancel={onCancel}
        destroyOnClose
        okText={translate(translations._ACT_APPLY)}
        cancelText={translate(translations._ACT_CANCEL)}
        onOk={handleDebounceInsertText}
        closeIcon={false}
        okButtonProps={{
          disabled: !canApplyTag,
        }}
      >
        <WrapperBody className="antsomi-wrapper-personalization">
          <div className="body">
            <table>
              <tbody>
                <tr>
                  <td>
                    <Flex vertical gap={8}>
                      <Label>
                        {MAP_TITLE.personalizationType}

                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>

                      <SelectTree
                        use="tree"
                        isSearchable
                        options={contentSourceList}
                        value={selectedPersonalType}
                        onChange={onChangePersonalType}
                        placeholder={getTranslateMessage(
                          TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                          'Select an item',
                        )}
                        fullWidthPopover
                      />
                    </Flex>
                  </td>

                  <td>
                    <Flex vertical gap={8}>
                      <Label>
                        {colHeaderAtt}

                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>

                      {renderColumnSecondHead()}
                    </Flex>
                  </td>

                  {isCustomFn && renderMergeTag()}

                  {renderColumnThirdHead()}
                </tr>

                {isAllocatedCode ? (
                  <tr>
                    <td colSpan={3}>
                      <ConditionAllocatedCode
                        moduleConfig={props.moduleConfig}
                        allocatedCode={allocatedCode}
                        disabledAdd={!selectedPersonalType?.value}
                        onChangeAllocatedCode={onChangeAllocatedCode}
                      />
                    </td>
                  </tr>
                ) : null}

                {isShowContentSourceIndex && (
                  <tr>
                    <td>
                      <Label>
                        Index
                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>
                    </td>
                    <td>
                      <SelectTree
                        use="tree"
                        isSearchable
                        options={Array.from({ length: 90 }).map((_, index) => ({
                          label: index + 1,
                          value: index + 1,
                        }))}
                        value={selectedProperties.csIndexAttribute}
                        onChange={valueOut => {
                          onChangeSelectedProperties(
                            produce(selectedProperties, draft => {
                              // eslint-disable-next-line no-param-reassign
                              draft.csIndexAttribute = valueOut;
                            }),
                          );
                        }}
                        placeholder={getTranslateMessage(
                          TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                          'Select an item',
                        )}
                        fullWidthPopover
                      />
                    </td>
                  </tr>
                )}

                {isCustomFn ? (
                  <>
                    <tr>
                      <td colSpan={3} style={{ paddingRight: 0 }}>
                        <Flex
                          vertical
                          gap={8}
                          style={{ margin: '5px 0px 5px 0px' }}
                        >
                          <Label>
                            {MAP_TITLE.function}
                            <span style={{ color: globalToken.colorError }}>
                              &nbsp;*
                            </span>
                          </Label>

                          <WrapperDisable disabled={disabled}>
                            <AceEditor
                              ref={ref}
                              setOptions={{
                                enableBasicAutocompletion: true,
                                enableLiveAutocompletion: true,
                                enableSnippets: true,
                                showLineNumbers: true,
                                tabSize: 2,
                              }}
                              mode="javascript"
                              fontSize={14}
                              showPrintMargin
                              showGutter
                              highlightActiveLine
                              value={customFunction.formular}
                              wrapEnabled
                              onChange={(value, event, tmp) =>
                                setRawStyleEditorOutput(value, event, tmp)
                              }
                              style={{ width: '100%', height: '200px' }}
                            />
                            <FormHelperText
                              id="component-helper-text"
                              error={!!customFunction.errorsFunction}
                              style={{ width: '350px' }}
                            >
                              {customFunction.errorsFunction}
                            </FormHelperText>
                          </WrapperDisable>
                        </Flex>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <Flex vertical gap={8} style={{ height: '100%' }}>
                          <Label>
                            {MAP_TITLE.outPutDataType}

                            <span style={{ color: globalToken.colorError }}>
                              &nbsp;*
                            </span>
                          </Label>

                          <SelectTree
                            onlyParent
                            use="tree"
                            placeholder={getTranslateMessage(
                              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                              'Select an item',
                            )}
                            fullWidthPopover
                            mapOptions={MAP_DATA_TYPE}
                            options={DATA_ATTR_TYPE}
                            value={MAP_DATA_TYPE[customFunction.dataType]}
                            // disabled={state.displayAs.disabled}
                            onChange={onChangedataType}
                          />
                        </Flex>
                      </td>
                      <td colSpan={2}>
                        <Flex vertical gap={8} style={{ width: 400 }}>
                          <Label>
                            {MAP_TITLE.displayFormat}

                            <span style={{ color: globalToken.colorError }}>
                              &nbsp;*
                            </span>
                          </Label>

                          <ModalConfigFormat
                            usePortal={[]}
                            dataType={customFunction.dataType}
                            onChange={onChangeModalConfig}
                            mode="edit"
                            initData={customFunction.displayFormat}
                          />
                        </Flex>
                      </td>
                    </tr>
                  </>
                ) : (
                  <></>
                )}

                {isShowDisplayFormat ? (
                  <tr>
                    <td style={{ verticalAlign: 'top' }}>
                      <Label style={{ lineHeight: '30px' }}>
                        {MAP_TITLE.displayFormat}
                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>
                    </td>

                    <td colSpan={2}>
                      <div style={{ wordBreak: 'break-all', width: '48%' }}>
                        <ModalConfigFormat
                          usePortal={[]}
                          dataType={selectedProperties?.displayFormat?.dataType}
                          onChange={onChangeDisplayFormat}
                          mode="edit"
                          initData={selectedProperties?.displayFormat?.format}
                        />
                      </div>
                    </td>
                  </tr>
                ) : (
                  <></>
                )}

                {isCustomFn ? (
                  <tr>
                    <td colSpan={3}>
                      <div
                        style={{
                          wordBreak: 'break-all',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <Checkbox
                          checked={customFunction.saveAsTemplate}
                          onClick={onSaveAsTemplate}
                        >
                          {MAP_TITLE.saveTemplate}
                        </Checkbox>
                      </div>
                    </td>
                  </tr>
                ) : (
                  renderMergeTagDisplay()
                )}

                {/* Section Replace chars */}
                {showReplaceSpecialChars ? (
                  <tr>
                    <td colSpan={3} style={{ padding: '20px 0px 5px 0px' }}>
                      <Checkbox
                        checked={isReplaceSpecialChars}
                        onClick={handleClickReplaceSpecChars}
                      >
                        {MAP_TITLE.replaceSpecChars}
                      </Checkbox>
                    </td>
                  </tr>
                ) : null}
              </tbody>
            </table>
          </div>
        </WrapperBody>
      </StyledModal>
    </div>
  );
};

ModalPersonalization.propTypes = {
  open: PropTypes.bool,
  design: PropTypes.string,
  dataState: PropTypes.object,
  activeNode: PropTypes.object,
  disabled: PropTypes.bool,
  listTemplateCustomFn: PropTypes.array,
  customFunction: PropTypes.object,
  selectedProperties: PropTypes.object,
  moduleConfig: PropTypes.object,
  showMergeTagDisplay: PropTypes.bool,
  useAllocatedCode: PropTypes.bool,
  showReplaceSpecialChars: PropTypes.bool,
  onChangeReplaceSpecialChars: PropTypes.func,
  onCancel: PropTypes.func,
  onChangeSelectedProperties: PropTypes.func,
  onChangeCustomFunction: PropTypes.func,
  onChangeDisplayName: PropTypes.func,
  onChangeAllocatedCode: PropTypes.func,
  onAddTag: PropTypes.func,
};
ModalPersonalization.defaultProps = {
  open: false,
  disabled: false,
  design: 'create',
  listTemplateCustomFn: [],
  selectedProperties: {},
  dataState: {
    personalizationType: {},
    personalizationData: {},
    promotionCodeAttr: {},
  },
  activeNode: {},
  moduleConfig: {},
  showMergeTagDisplay: true,
  useAllocatedCode: false,
  customFunction: initCustomFunction,
  onChangeSelectedProperties: () => {},
  onChangeCustomFunction: () => {},
  onChangeDisplayName: () => {},
  onChangeAllocatedCode: () => {},
  onCancel: () => {},
  onAddTag: () => {},
};

const mapStateToProps = createStructuredSelector({
  activeNode: makeSelectActiveIdNodeData(),
});

export default connect(
  mapStateToProps,
  null,
)(memo(ModalPersonalization));
