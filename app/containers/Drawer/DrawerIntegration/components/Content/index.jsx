/* eslint-disable react/prop-types */
/* eslint-disable indent */
// Libraries
import React, { useEffect, memo, useCallback, useMemo, useState } from 'react';
import _ from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Locales
import { getTranslateMessage } from '../../../../Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { MAP_TRANSLATE } from '../../constants.translate';

// Services
import DestinationServices from 'services/Destination';

// MODULE_CONFIGS
import { MODULE_CONFIG } from 'modules/Dashboard/MarketingHub/Destination/Create/config';

// Selectors
import {
  makeSelectCreateDesDataConfig,
  makeSelectCreateDesMain,
} from 'modules/Dashboard/MarketingHub/Destination/Create/selectors';
import { makeSelectChannelIntegration } from '../../selectors';
import {
  makeSelectIsOwnerDestDetail,
  makeSelectIsHasPermissionDestDetail,
} from 'modules/Dashboard/MarketingHub/Destination/Detail/selectors';

// Actions
import { updateValue, getListDone } from 'redux/actions';

// Components
import { Grid } from '@material-ui/core';
import {
  Alert,
  Checkbox,
  Radio as AntsomiRadio,
  Flex,
} from '@antscorp/antsomi-ui';
import {
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import UISelect from 'components/form/UISelectCondition';
import UIFrequencyCapping from 'components/common/UIFrequencyCappingV2';
import FormHelperText from '@material-ui/core/FormHelperText';
import {
  useStyles,
  StyleContentLoading,
  LabelText,
} from 'modules/Dashboard/MarketingHub/Destination/Create/Content/styles';
import {
  WrapperFixWidth,
  WrapperTitleItem,
} from 'containers/Segment/Content/SegmentMember/styled';
import { UINumberStyled } from 'modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/styles';
import {
  BlockLeft,
  BlockRight,
  BlockTitle,
  Heading,
  Title,
  WrapperContent,
  WrapperSendMultiple,
} from './styled';
import ErrorBoundary from 'components/common/ErrorBoundary';

// Utils
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { generateKey, getObjectPropSafely, safeParseInt } from 'utils/common';
import {
  CHANNEL_FILE_TRANSFER,
  MAX_TO_SEND_BROADCAST,
  MINUTE_OPTIONS,
  TYPE_DELIVERY_INTERVAL,
  mapLabelDeliveryInterval,
  checkIsHideField,
} from 'modules/Dashboard/MarketingHub/Destination/Create/utils';
import { checkHideInfoFields, isBroadcastSize } from '../../utils';
import { CATALOG_WITH_RESPONSE } from '../../constants';
import { CircleInfoIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { DELIVERY_SEND_OPTIONS, DELIVERY_SEND_VALUE } from './constants';
import { DEFAULT_INTERVAL_SETTING } from '../../../../../modules/Dashboard/MarketingHub/Destination/Create/constants';
import { translate, translations } from '@antscorp/antsomi-locales';

const CHANNEL_ID_TYPE = {
  WEB_PUSH: 3,
  APP_PUSH: 4,
};
export const CATALOG_CODE_CHANNEL_LIMIT = {
  [CHANNEL_ID_TYPE.WEB_PUSH]: 'antsomi_web_push',
  [CHANNEL_ID_TYPE.APP_PUSH]: 'antsomi_app_push',
};

const PATH =
  'app/containers/Drawer/DrawerIntegration/components/Content/index.jsx';

const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._,
    'Editing the settings will affect the other features which is using this destination',
  ),
  notiNotCreateDest: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_CREATE_DESTINATION,
    'Do not exist any catalog, to create a destination required have must a catalog',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titlConfigFields: getTranslateMessage(
    TRANSLATE_KEY._TITL_CONFIGURE_FIELD,
    'Configure fields',
  ),
  titlGeneralSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_SETTING,
    'General Setting',
  ),
  titlFrequencyCapping: getTranslateMessage(
    TRANSLATE_KEY._TITL_FREQUENCY_CAPPING,
    'Frequency Capping',
  ),
  titleSendRate: translate(translations._LABEL_RATE_LIMIT, 'Send Rate'),
  titleDeliveredMethod: translate(
    translations._DESTINATION_DELIVERY_METHOD,
    'Delivery Method',
  ),
  possible: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_AS_FAST_AS_POSSIBLE,
    'As fast as possible',
  ),
  maximumOf: translate(translations._LABEL_MAX, 'Maximum of'),
  timePerson: translate(
    translations._LABEL_REQ_PER_SEC,
    'request(s) per second',
  ),
  broadcastSize: translate(translations._BROADCAST_SIZE, 'Broadcast size'),
};

const InputComponent = memo(props => (
  <Grid
    container
    className={props.classes.item}
    style={props.isHideField ? { padding: 0 } : {}}
  >
    {props.componentEl(props)}
  </Grid>
));

function Content(props) {
  const classes = useStyles();

  // Selectors
  const { main, dataConfig } = props;
  const { isLoading, isInitDone, design, channelId } = main;

  // States
  const [isValidBroadCast, setIsValidBroadCast] = useState(true);

  const onMemoInitdata = useMemo(() => dataConfig.frequencyCapping.value, [
    isInitDone,
  ]);
  const showBroadcastSize = useMemo(
    () =>
      isBroadcastSize(
        dataConfig?.destinationCatalog?.value?.catalogCode,
        dataConfig?.deliveryBroadcast?.isBroadcast,
      ),
    [dataConfig],
  );

  const onMemoComponenId = useMemo(() => generateKey(), [isInitDone]);

  const onChangeData = useCallback(
    name => (value, extraValue) => {
      const isAntsomiWebPush = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 3 &&
          value.catalogCode === 'antsomi_web_push',
      );
      const isAntsomiEmail = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 1 &&
          value.catalogCode === 'antsomi_email',
      );

      const isAntsomiAppPush = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 4 &&
          value.catalogCode === 'antsomi_app_push',
      );

      const isZaloOA = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 10 &&
          value.catalogCode === 'zalo_official_account',
      );

      const isNewUI =
        isAntsomiWebPush || isAntsomiEmail || isAntsomiAppPush || isZaloOA;

      if (isNewUI) {
        props.goToCreateV2({ channelId });
      } else if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value, extraValue });
      }
    },
    [dataConfig],
  );

  const catalogCodeSelected = useMemo(
    () => _.get(props.main, 'activeDestCatalog.catalogCode'),
    [props.main && props.main.activeDestCatalog],
  );
  const methodList = useMemo(() => _.get(dataConfig, 'method.options', []), [
    dataConfig?.method?.options,
  ]);
  const isHideMethod = useMemo(
    () =>
      catalogCodeSelected === 'infobip_whatsapp_template' ||
      methodList?.length === 1,
    [catalogCodeSelected, methodList],
  );

  const methodSelected = useMemo(() => _.get(dataConfig, 'method.value'), [
    dataConfig?.method?.value,
  ]);

  const isHideGeneralSettings = useMemo(
    () =>
      ['sms_fpt', 'sms_fpt_new'].includes(catalogCodeSelected) &&
      methodSelected?.value === 'sendCSKH',
    [catalogCodeSelected, methodSelected],
  );

  const canSendBroadcast = useMemo(
    () =>
      getObjectPropSafely(
        () => props.main.activeDestCatalog.catalogInput.canSendBroadcast,
        false,
      ),
    [props.main && props.main.activeDestCatalog],
  );
  const canSendMultiple = useMemo(
    () =>
      getObjectPropSafely(
        () => props.main.activeDestCatalog.catalogInput.canSendMultiple,
        false,
      ),
    [props.main && props.main.activeDestCatalog],
  );

  const intervalBroadcastSetting = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          props.main.activeDestCatalog.catalogSetting.intervalBroadcastSetting,
        {},
      ),
    [props.main && props.main.activeDestCatalog],
  );
  const { isSendBroadcast, fromNumber, toNumber } =
    intervalBroadcastSetting || {};

  const isForceHideBroadCast = useMemo(
    () => _.get(dataConfig, 'method.value.forceHide', false),
    [dataConfig && dataConfig.method],
  );

  useEffect(() => {
    if (!_.isEmpty(intervalBroadcastSetting) && design === 'create') {
      props.onChangeDataConfigForce({
        name: 'deliveryBroadcast',
        value: {
          from: fromNumber,
          to: toNumber,
          isBroadcast: isSendBroadcast,
        },
      });
    }
  }, [intervalBroadcastSetting]);

  useEffect(() => {
    if (canSendBroadcast && design === 'create') {
      onChangeData('deliveryBroadcast')(canSendBroadcast);
    }
  }, [canSendBroadcast]);
  useEffect(() => {
    // init send multiple to stored
    if (canSendMultiple && design === 'create') {
      onChangeData('deliveryBroadcast')('onCheckingMultiple', canSendMultiple);
    }
  }, [canSendMultiple]);

  useEffect(() => {
    if (
      design === 'create' &&
      ['sms_fpt', 'sms_fpt_new'].includes(catalogCodeSelected)
    ) {
      const currentMethod = _.get(dataConfig, 'method.value', '');

      if (_.isEmpty(currentMethod)) {
        const initMethod = _.get(dataConfig, 'method.mapOptions.sendOTP');

        if (initMethod) {
          props.onChangeDataConfig({ name: 'method', value: initMethod });
        }
      }
    }
  }, [dataConfig?.method, design, catalogCodeSelected]);

  const handleValidateDestinationCount = async catalogId => {
    try {
      const res = await DestinationServices.data.getCountDestination({
        catalogId,
      });

      const newOptions = main.cacheOptionsDataDes.filter(
        option =>
          option.catalogCode !==
          CATALOG_CODE_CHANNEL_LIMIT[safeParseInt(channelId)],
      );
      if (res.code === 200 && safeParseInt(res.data.destinationCount) > 0) {
        props.onChangeDesCataLog(newOptions);
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleValidateDestinationCount',
        data: err.stack,
      });
      console.warn('err', err);
    }
  };

  useEffect(() => {
    if (
      design === 'create' &&
      Object.values(CHANNEL_ID_TYPE).includes(safeParseInt(channelId))
    ) {
      const catalogTemp = dataConfig.destinationCatalog.options.find(
        option =>
          option.catalogCode ===
          CATALOG_CODE_CHANNEL_LIMIT[safeParseInt(channelId)],
      );
      const catalogId = getObjectPropSafely(() => catalogTemp.catalogId, '');

      if (catalogId) {
        handleValidateDestinationCount(catalogId);
      }
    }
  }, [dataConfig.destinationCatalog.options]);

  const renderDeliveredFields = channelIdDes => {
    if (+channelIdDes === CHANNEL_FILE_TRANSFER) {
      const { value = '' } = getObjectPropSafely(
        () => dataConfig.deliveryInterval.mapOptions,
        {},
      );
      const valueTemp = getObjectPropSafely(
        () => dataConfig.deliveryInterval.value,
        1,
      );
      const label = mapLabelDeliveryInterval[value] || '';
      const mapValueMinute =
        MINUTE_OPTIONS.find(minuteOption => minuteOption.value === valueTemp) ||
        MINUTE_OPTIONS[0];

      return (
        <Grid container>
          <BlockLeft
            item
            xs={3}
            style={{ display: 'flex', alignItems: 'center' }}
          >
            <Title>{MAP_TITLE.titleDeliveredMethod}</Title>
          </BlockLeft>
          <BlockRight item xs={9}>
            <WrapperDisable disabled={dataConfig.deliveryInterval.disabled}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <WrapperFixWidth width="250px">
                  <UISelect
                    onlyParent
                    use="tree"
                    isSearchable={false}
                    options={dataConfig.deliveryInterval.options}
                    value={dataConfig.deliveryInterval.mapOptions}
                    onChange={onChangeData('deliveryInterval')}
                    placeholder={getTranslateMessage(
                      TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                      'Select an item',
                    )}
                    fullWidthPopover
                    disabled={false}
                  />
                </WrapperFixWidth>
                {value !== TYPE_DELIVERY_INTERVAL.AFTER_COMPLETED && (
                  <>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._EVERY, 'every')}
                    </LabelText>
                    <WrapperFixWidth width="100px">
                      {value === TYPE_DELIVERY_INTERVAL.MINUTE ? (
                        <UISelect
                          onlyParent
                          use="tree"
                          isSearchable={false}
                          options={MINUTE_OPTIONS}
                          value={mapValueMinute}
                          onChange={onChangeData('deliveryInterval')}
                          placeholder={getTranslateMessage(
                            TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                            'Select an item',
                          )}
                          fullWidthPopover
                          disabled={false}
                        />
                      ) : (
                        <UINumberStyled
                          name="times"
                          onChange={onChangeData('deliveryInterval')}
                          value={valueTemp}
                          min={1}
                          // max={100}
                          defaultValue={1}
                          width={100}
                        />
                      )}
                    </WrapperFixWidth>
                    <LabelText>{label}</LabelText>
                  </>
                )}
              </div>
            </WrapperDisable>
          </BlockRight>
        </Grid>
      );
    }

    return (
      <>
        {canSendBroadcast && !isForceHideBroadCast && (
          <Grid container style={{ marginBottom: 15 }}>
            <BlockLeft
              xs={3}
              style={{
                display: 'flex',
                alignItems: 'flex-start',
              }}
            >
              <Title>
                {showBroadcastSize
                  ? MAP_TITLE.broadcastSize
                  : MAP_TITLE.titleDeliveredMethod}
              </Title>
            </BlockLeft>
            <BlockRight item xs={9}>
              <WrapperDisable disabled={dataConfig.deliveryBroadcast.disabled}>
                {!showBroadcastSize ? (
                  <AntsomiRadio.Group
                    name="onCheckingBroadCast"
                    value={
                      dataConfig.deliveryBroadcast.isBroadcast
                        ? DELIVERY_SEND_VALUE.BROADCAST
                        : DELIVERY_SEND_VALUE.PERSONALIZED
                    }
                    options={DELIVERY_SEND_OPTIONS}
                    disabled={dataConfig.deliveryBroadcast.disabled}
                    onChange={event => {
                      if (event.target) {
                        const {
                          value = '',
                          name: checkName = '',
                        } = event.target;
                        const isEnabled =
                          value === DELIVERY_SEND_VALUE.BROADCAST;

                        onChangeData('deliveryBroadcast')(checkName, isEnabled);
                      }
                    }}
                  />
                ) : null}

                <WrapperSendMultiple
                  isShow={
                    canSendMultiple &&
                    canSendBroadcast &&
                    dataConfig.deliveryBroadcast.isBroadcast
                  }
                >
                  <Checkbox
                    name="onCheckingMultiple"
                    checked={dataConfig.deliveryBroadcast?.isSendMultiple}
                    disabled={dataConfig.deliveryBroadcast.disabled}
                    onChange={event => {
                      if (event.target) {
                        const {
                          checked = false,
                          name: checkName = '',
                        } = event.target;

                        onChangeData('deliveryBroadcast')(checkName, checked);
                      }
                    }}
                  >
                    {getTranslateMessage(
                      TRANSLATE_KEY._,
                      'Send multiple messages',
                    )}
                  </Checkbox>
                </WrapperSendMultiple>
                {dataConfig?.deliveryBroadcast?.isBroadcast && (
                  <Flex vertical gap={10}>
                    <Flex
                      align="center"
                      gap={12}
                      style={{
                        marginTop: showBroadcastSize ? -5 : 8,
                      }}
                    >
                      <LabelText>
                        {translate(translations._LABEL_FROM, 'From')}
                      </LabelText>
                      <WrapperFixWidth width="70px">
                        <UINumberStyled
                          name="times"
                          onChange={value => {
                            setIsValidBroadCast(
                              getObjectPropSafely(
                                () => value <= dataConfig.deliveryBroadcast.to,
                              ),
                            );

                            onChangeData('deliveryBroadcast')({
                              from: value,
                              isFrom: true,
                            });
                          }}
                          value={dataConfig.deliveryBroadcast.from}
                          min={
                            !_.isEmpty(intervalBroadcastSetting) &&
                            typeof fromNumber === 'number'
                              ? fromNumber
                              : 0
                          }
                          max={
                            !_.isEmpty(intervalBroadcastSetting) &&
                            typeof toNumber === 'number'
                              ? toNumber
                              : MAX_TO_SEND_BROADCAST
                          }
                          defaultValue={dataConfig.deliveryBroadcast.from}
                          defaultEmptyValue={
                            !_.isEmpty(intervalBroadcastSetting) &&
                            typeof fromNumber === 'number'
                              ? fromNumber
                              : 1
                          }
                          width={70}
                        />
                      </WrapperFixWidth>
                      <LabelText>
                        {getTranslateMessage(TRANSLATE_KEY._LABEL_TO, 'to')}
                      </LabelText>
                      <WrapperFixWidth width="70px">
                        <UINumberStyled
                          name="times"
                          onChange={value => {
                            setIsValidBroadCast(
                              getObjectPropSafely(
                                () =>
                                  value >= dataConfig.deliveryBroadcast.from,
                              ),
                            );

                            onChangeData('deliveryBroadcast')({
                              to: value,
                              isFrom: false,
                            });
                          }}
                          value={dataConfig.deliveryBroadcast.to}
                          min={Math.max(
                            !_.isEmpty(intervalBroadcastSetting) &&
                              typeof fromNumber === 'number'
                              ? fromNumber
                              : 1,
                            dataConfig.deliveryBroadcast.from,
                          )}
                          max={
                            !_.isEmpty(intervalBroadcastSetting) &&
                            typeof toNumber === 'number'
                              ? toNumber
                              : MAX_TO_SEND_BROADCAST
                          }
                          defaultValue={dataConfig.deliveryBroadcast.to}
                          defaultEmptyValue={
                            !_.isEmpty(intervalBroadcastSetting) &&
                            typeof toNumber === 'number'
                              ? toNumber
                              : 1000
                          }
                          width={70}
                        />
                      </WrapperFixWidth>
                      <LabelText>
                        {translate(
                          translations._LABEL_PPL_PER_ONCE,
                          'people per once',
                        )}
                      </LabelText>
                    </Flex>
                    <Flex align="center" gap={12}>
                      <LabelText>
                        {translate(
                          translations._LABEL_ALL_PPL,
                          'or all available people every',
                        )}
                      </LabelText>
                      <WrapperFixWidth width="70px">
                        <UINumberStyled
                          name="times"
                          onChange={value => {
                            console.log({ value });
                            onChangeData('deliveryBroadcast')({
                              repeatInterval: {
                                ...DEFAULT_INTERVAL_SETTING,
                                value,
                              },
                            });
                          }}
                          value={
                            dataConfig.deliveryBroadcast.repeatInterval.value
                          }
                          min={1}
                          max={60}
                          defaultValue={
                            dataConfig.deliveryBroadcast.repeatInterval.value
                          }
                          defaultEmptyValue={1}
                          width={70}
                        />
                      </WrapperFixWidth>
                      <LabelText>
                        {translate(translations._LABEL_MINUTE, 'minute(s)')}
                      </LabelText>
                    </Flex>
                  </Flex>
                )}
                {!isValidBroadCast && dataConfig.deliveryBroadcast.isBroadcast && (
                  <div
                    style={{
                      marginTop: 6,
                      fontSize: '11px',
                      color: '#f44336',
                    }}
                  >
                    {translate(
                      translations._ERR_VALIDATE_INPUT_NUMBER,
                      'Min must be less than or equal to Max',
                    )}
                  </div>
                )}
              </WrapperDisable>
            </BlockRight>
          </Grid>
        )}
        {!dataConfig.deliveryBroadcast.isBroadcast && (
          <Grid container>
            <BlockLeft item xs={3}>
              <Title>{MAP_TITLE.titleSendRate}</Title>
            </BlockLeft>
            <BlockRight item xs={9}>
              <WrapperDisable disabled={dataConfig.deliveryRate.disabled}>
                <AntsomiRadio.Group
                  key={catalogCodeSelected}
                  aria-label="segmentMember"
                  name="segmentMember"
                  defaultValue={dataConfig.deliveryRate.type}
                  value={dataConfig.deliveryRate.type}
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 10,
                  }}
                  onChange={onChangeData('deliveryRate')}
                >
                  <AntsomiRadio value="normal">
                    {MAP_TITLE.possible}
                  </AntsomiRadio>

                  <Flex align="center" gap={10}>
                    <AntsomiRadio value="limit">
                      {MAP_TITLE.maximumOf}
                    </AntsomiRadio>

                    <WrapperFixWidth
                      style={{
                        marginLeft: '-30px',
                      }}
                    >
                      <UINumberStyled
                        name="times"
                        onChange={onChangeData('deliveryRate')}
                        value={dataConfig.deliveryRate.limit || 1}
                        min={1}
                        defaultValue={1}
                        width={60}
                      />
                    </WrapperFixWidth>

                    <WrapperTitleItem>{MAP_TITLE.timePerson}</WrapperTitleItem>
                  </Flex>
                </AntsomiRadio.Group>
              </WrapperDisable>
            </BlockRight>
          </Grid>
        )}
      </>
    );
  };

  const renderContentSettings = () => (
    <StyleContentLoading>
      <Loading isLoading={isLoading} />

      {design === 'update' && (
        <>
          <Alert
            showIcon
            type="warning"
            variant="outline"
            message={MAP_TRANSLATE.notiAlertEdit}
            style={{ marginBottom: 15, width: '100%' }}
          />

          {CATALOG_WITH_RESPONSE.includes(catalogCodeSelected) && (
            <Alert
              showIcon
              closable
              type="info"
              variant="outline"
              message={MAP_TRANSLATE.notiInfoRetry}
              style={{ marginBottom: 15, width: '100%' }}
              icon={<CircleInfoIcon size={24} />}
            />
          )}
        </>
      )}
      <Grid container>
        <Heading item xs={12}>
          <BlockTitle style={{ marginTop: 0 }}>
            {MAP_TITLE.titlGeneralInfomation}
          </BlockTitle>
        </Heading>
        <Grid item xs={12}>
          {dataConfig.infoFields.map(
            each =>
              !checkHideInfoFields(each) && (
                <InputComponent
                  {...dataConfig[each]}
                  onChange={onChangeData}
                  key={each}
                  classes={classes}
                  isOwner={props.isOwner}
                  isHasPermissionEdit={props.isHasPermissionEdit}
                />
              ),
          )}
          {dataConfig.extraInfoFields.map(each => {
            if (each === 'method' && isHideMethod) return null;

            return (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                classes={classes}
                isOwner={props.isOwner}
                isHasPermissionEdit={props.isHasPermissionEdit}
              />
            );
          })}
          {dataConfig.destinationCatalog.options.length === 0 && (
            <FormHelperText error>{MAP_TITLE.notiNotCreateDest}</FormHelperText>
          )}
        </Grid>
      </Grid>
      {dataConfig.configFields.length > 0 && (
        <Grid container>
          <Heading item xs={12}>
            <BlockTitle>{MAP_TITLE.titlConfigFields}</BlockTitle>
          </Heading>
          <Grid item xs={12}>
            {dataConfig.configFields.map(each => (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                isHideField={checkIsHideField({
                  channelId,
                  extraEmailBtn: +channelId === 1 ? main.extraEmailBtn : [],
                  name: each,
                })}
                extraEmailBtn={main.extraEmailBtn}
                onChangeExtraEmailBtn={props.onChangeExtraEmailBtn}
                classes={classes}
                channelId={channelId}
                isOwner={props.isOwner}
                isHasPermissionEdit={props.isHasPermissionEdit}
              />
            ))}
          </Grid>
        </Grid>
      )}
      {!isHideGeneralSettings && (
        <Grid container>
          <Heading item xs={12}>
            <BlockTitle> {MAP_TITLE.titlGeneralSetting}</BlockTitle>
          </Heading>
          <Grid container item xs={12}>
            <BlockLeft item xs={3}>
              <Title>{MAP_TITLE.titlFrequencyCapping}</Title>
            </BlockLeft>
            <BlockRight item xs={9}>
              <UIFrequencyCapping
                key={catalogCodeSelected}
                isNoPadding
                channelId={channelId}
                isShowLabel={false}
                onChange={onChangeData('frequencyCapping')}
                initData={onMemoInitdata}
                componentId={onMemoComponenId}
                disabled={dataConfig.frequencyCapping.disabled}
              />
            </BlockRight>
          </Grid>
          <Grid container>{renderDeliveredFields(channelId)}</Grid>
        </Grid>
      )}
    </StyleContentLoading>
  );

  return (
    <ErrorBoundary path={PATH}>
      <WrapperContent>{renderContentSettings()}</WrapperContent>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  workspace: makeSelectChannelIntegration(),
  dataConfig: makeSelectCreateDesDataConfig(),
  main: makeSelectCreateDesMain(),
  isOwner: makeSelectIsOwnerDestDetail(),
  isHasPermissionEdit: makeSelectIsHasPermissionDestDetail(),
});

function mapDispatchToProps(dispatch, _ownProps) {
  return {
    onChangeExtraEmailBtn: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_EXTRA_EMAIL_BTN@@`, data),
      );
    },
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    onChangeDataConfigForce: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG_FORCE@@`, value)),
    onChangeDesCataLog: value =>
      dispatch(getListDone(`${MODULE_CONFIG.key}@@DEST_CATALOG@@`, value)),
    onChangeInitCataLog: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@INIT_CATALOG@@`, value)),
    goToCreateV2: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_CREATE_V2`, data)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Content);
