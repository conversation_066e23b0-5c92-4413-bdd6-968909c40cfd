/* eslint-disable no-else-return */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { Fragment, useEffect, memo, useMemo } from 'react';
import { useImmer } from 'use-immer';
import {
  UIButton,
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import CloseIcon from '@material-ui/icons/Close';

import {
  WrapperPreviewSource,
  DivLoading,
  DataSourceBlockContent,
} from '../../styled';
import { useFetchDataByEvent } from './useFetchDataByEvent';
import ItemPreviewSourceSelected from '../../../../Molecules/ItemPreviewSource';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ConditionError from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import ModalConfirmDeleteSource from './ModalConfirmDeleteSource';
import { convertConditionRefineToArrayString } from './utils';
import { validatePropertiesRefines } from '../../utils.validate';
import { FormHelperText } from '@material-ui/core';
import { STATUS_SOURCE_CODE } from '../../../../../utils/constants';
import isEqual from 'react-fast-compare';
import classnames from 'classnames';
import { Button, PopoverSelect } from '@antscorp/antsomi-ui';
import { addMessageToQueue } from '../../../../../utils/web/queue';

const initState = {
  open: false,
  tempDataSources: [],
  sourceNames: [],
  arrayRefineWillDeleted: [],
  sourceIds: [],
  errors: [],
  isRFM: false,
};

const labelAnySource = getTranslateMessage(
  TRANSLATE_KEY._TITL_IN_ANY_SOURCE,
  'In any source of',
);

const labelAddSource = getTranslateMessage(
  TRANSLATE_KEY._ACT_ADD_SOURCE,
  'Add source',
);

const PATH =
  'app/components/common/UIPerformEventV2/BlockConditionPerformEvent/DataSources/index.jsx';

const SelectDataSources = props => {
  let isMounted = true;
  const {
    eventValue,
    sourcesSelected: sourceSelectedProp,
    isInitDataSources,
    refineWithProperties,
    isRFM,
    label,
  } = props;

  let isDisable = true;

  if (eventValue !== null && typeof eventValue === 'object') {
    if (Object.keys(eventValue).length > 0) {
      isDisable = false;
    }
  }

  const [state, setState] = useImmer(initState);

  const { group, isLoading } = useFetchDataByEvent({
    isInitDataSources,
    eventValue,
    sourcesSelected: sourceSelectedProp,
    paramsFetchEvent: props.paramsFetchEvent,
  });

  useEffect(
    () => () => {
      isMounted = false;
    },
    [],
  );

  const toggleModal = () => {
    setState(draft => {
      draft.open = false;
    });
  };

  useEffect(() => {
    // console.log('-------', isLoading, group, isInitDataSources);
    if (!isLoading) {
      const arr = [];

      group.list.forEach(tmp => {
        arr.push(tmp.value);
      });

      if (isInitDataSources !== true) {
        props.onChange({
          dataSourceSelected: arr,
          temp: false,
          isDeleteRefine: false,
        });
      }

      if (props.onChangeListDatasources) {
        props.onChangeListDatasources(group.list);
      }
    }
  }, [group, isLoading]);

  useEffect(() => {
    if (group.list.length > 0) {
      let errors = [];
      sourceSelectedProp.forEach(each => {
        const item = group.map[each];

        if (
          item &&
          item.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT
        ) {
          errors = [
            'The source colored with red no more exists in any of model-source',
          ];
        }
      });

      setState(draft => {
        draft.errors = errors;
      });
    }
  }, [sourceSelectedProp, group]);

  const callback = (type, data) => {
    if (type === 'DELETE_SOURCE_ITEM') {
      const array = sourceSelectedProp;
      const { inValid, refineBelongtoSourceId } = validatePropertiesRefines(
        refineWithProperties,
        array[data],
      );

      if (
        refineWithProperties.size === 0 ||
        inValid ||
        !refineBelongtoSourceId
      ) {
        props.onChange({
          dataSourceSelected: [
            ...array.slice(0, data),
            ...array.slice(data + 1),
          ],
          isDeleteRefine: false,
        });
      } else {
        let sourceName = '--';
        const sourceId = array[data];
        const tempData = [...array.slice(0, data), ...array.slice(data + 1)];

        if (group.map[sourceId]) {
          sourceName = group.map[sourceId].label;
        }

        setState(draft => {
          draft.open = true;
          draft.sourceNames = [sourceName];
          draft.sourceIds = [sourceId];
          draft.arrayRefineWillDeleted = convertConditionRefineToArrayString(
            refineWithProperties,
            sourceId,
            tempData,
          );
          draft.tempDataSources = tempData;
        });
      }
    } else if (type === 'CONFIRM_DELETE_SOURCE') {
      if (data === true) {
        props.onChange({
          dataSourceSelected: state.tempDataSources,
          isDeleteRefine: true,
          sourceIdWillDeleted: state.sourceIds,
        });
      }
    }
  };

  const sourceSelected = useMemo(() => {
    if (props.showAllSource && props.isViewMode) {
      return group.list.map(item => item.id);
    }

    return sourceSelectedProp;
  }, [props.isViewMode, sourceSelectedProp, props.showAllSource, group.list]);

  const handleApplySelectSources = newSelectedKeyList => {
    try {
      if (!Array.isArray(sourceSelectedProp)) {
        throw new Error('sourceSelectedProp must be an array');
      }

      // No need to validate with refine properties
      if (refineWithProperties?.size === 0) {
        props.onChange({
          dataSourceSelected: newSelectedKeyList,
          isDeleteRefine: false,
        });

        return;
      }

      const removedSourceKeys = sourceSelectedProp.filter(
        sourceKey => !newSelectedKeyList.includes(sourceKey),
      );
      const isValidAllSources = removedSourceKeys.every(sourceId => {
        const { inValid, refineBelongtoSourceId } = validatePropertiesRefines(
          refineWithProperties,
          sourceId,
        );

        return inValid || !refineBelongtoSourceId;
      });

      // If not removed any source or all sources are valid -> just update selected source data
      if (removedSourceKeys.length === 0 || isValidAllSources) {
        props.onChange({
          dataSourceSelected: newSelectedKeyList,
          isDeleteRefine: false,
        });

        return;
      }

      const {
        arrayRefineWillDeleted,
        removedSourceNameList,
      } = removedSourceKeys.reduce(
        (acc, sourceId) => {
          // Update list removed source name
          if (group.map[sourceId]) {
            acc.removedSourceNameList.push(group.map[sourceId].label);
          } else {
            acc.removedSourceNameList.push('--');
          }

          // Update list refine will be deleted
          const refineList = convertConditionRefineToArrayString(
            refineWithProperties,
            sourceId,
            newSelectedKeyList,
          );
          acc.arrayRefineWillDeleted.push(...refineList);

          return acc;
        },
        { removedSourceNameList: [], arrayRefineWillDeleted: [] },
      );

      // Process with modal confirm delete source
      setState(draft => {
        draft.open = true;
        draft.sourceNames = removedSourceNameList;
        draft.sourceIds = removedSourceKeys;
        draft.arrayRefineWillDeleted = arrayRefineWillDeleted;
        draft.tempDataSources = newSelectedKeyList;
      });
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleApplySelectSources',
        data: {
          error: error.stack,
          args: { newSelectedKeyList, sourceSelectedProp },
        },
      });
    }
  };

  if (!isMounted) {
    return null;
  }

  const renderContentSelectDataSources = () => {
    if (!isDisable && isLoading && isMounted) {
      return (
        <DivLoading>
          <Loading isLoading={isLoading} size={20} />
        </DivLoading>
      );
    }

    console.log({ sourceSelected });

    return (
      <WrapperDisable disabled={isDisable}>
        {sourceSelected.map((source, index) => {
          const tmp = group.map[source] || {};
          const isError =
            tmp.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT;
          if (tmp !== undefined) {
            return (
              <ItemPreviewSourceSelected
                rightIcon={
                  <Button
                    shape="circle"
                    type="text"
                    style={{
                      width: 16,
                      height: 16,
                      minWidth: 16,
                      backgroundColor: '#ffffff',
                    }}
                    onClick={() => callback('DELETE_SOURCE_ITEM', index)}
                    icon={<CloseIcon />}
                  />
                }
                index={Math.random()}
                label={tmp.label}
                value={tmp.value}
                readOnly={props.isViewMode}
                item={tmp}
                key={source}
                isError={isError}
              />
            );
          }
          return null;
        })}

        {!props.isViewMode && (
          <PopoverSelect
            onApply={handleApplySelectSources}
            options={group.list}
            selected={sourceSelected}
          >
            <UIButton
              variant="contained"
              reverse
              iconName="add"
              theme="outline"
              borderRadius="1rem"
              fs="0.75rem"
              style={{
                border: 'solid 1px',
                height: 24,
              }}
            >
              {labelAddSource}
            </UIButton>
          </PopoverSelect>
        )}
      </WrapperDisable>
    );
  };

  if (props.hidden) {
    return null;
  }

  return (
    <DataSourceBlockContent isRFM={isRFM}>
      {label && isRFM ? (
        <Fragment>{label}</Fragment>
      ) : (
        <div className="title select-source">
          <div className="select-source-txt">{labelAnySource}</div>
        </div>
      )}
      <div
        className={classnames('content', {
          'flex-column': props.isViewMode,
        })}
      >
        <WrapperDisable disabled={props.disabledEventConditions}>
          <WrapperPreviewSource>
            {renderContentSelectDataSources()}
          </WrapperPreviewSource>
        </WrapperDisable>

        <ConditionError keyError={3} error={props.error} />
        {!!state.errors[0] && (
          <FormHelperText id="component-helper-text" error={!!state.errors[0]}>
            {state.errors}
          </FormHelperText>
        )}
      </div>
      <ModalConfirmDeleteSource
        isOpen={state.open}
        toggle={toggleModal}
        callback={callback}
        sourceName={state.sourceNames.join(', ')}
        conditionRefine={state.arrayRefineWillDeleted}
      />
    </DataSourceBlockContent>
  );
};

SelectDataSources.defaultProps = {
  sourcesSelected: [],
  disabledEventConditions: false,
  isViewMode: false,
  showAllSource: false,
};
export default memo(SelectDataSources, isEqual);
