/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-param-reassign */
// import _isEmpty from 'lodash/isEmpty';
import { OrderedMap } from 'immutable';
import { useImmer } from 'use-immer';

import BOAttributeServices from 'services/BusinessObject';
import SegmentServices from 'services/Segment';

import MetaDataServices from 'services/MetaData';
import { useDeepCompareEffect, useIsMounted } from '../../../../../hooks';
import { safeParse } from '../../../../../utils/common';
import {
  getLookupRefineWithProperties,
  toEntryFE,
  toRefinePropertiesUI,
} from './utils';

// import { initState, toEntryFE, NO_DATA } from '../ItemRule/Preview/utils';
// const NO_DATA = 'No data.';

const initState = {
  isLoading: true,
  group: {
    list: [],
    map: {},
  },
  conditions: OrderedMap(),
};

export const useFetchDataByEvent = (
  eventValue = null,
  isInit,
  backup = {},
  refineWithProperties,
  moduleConfig = {},
  sourcesSelected = [],
  itemTypeId = null,
  eventPropertyService,
  isUsingJourneyTemplate,
) => {
  const isMounted = useIsMounted();

  const [state, setState] = useImmer(initState);

  const setStateCommon = object => {
    setState(draft => {
      Object.keys(object).forEach(key => {
        draft[key] = object[key];
      });
    });
  };

  const fetchDataAPI = async (type, value, dataSources) => {
    setState(draft => {
      draft.isLoading = true;
    });

    try {
      let response = null;
      let data = {
        list: [],
        map: {},
      };

      if (!isUsingJourneyTemplate) {
        response = await SegmentServices.fetch[type](
          value,
          moduleConfig.objectType,
          dataSources,
        );
      }

      if (response && response.code === 200) {
        data = toEntryFE(response.data, type);
      }

      if (isInit) {
        const refine = safeParse(backup.OR[0], {});
        const eventIds = getLookupRefineWithProperties(refine);
        const dataSourceID = '-1';
        const params = {
          inputUrl: `${dataSourceID}/${eventValue.eventCategoryId}/${
            eventValue.eventActionId
          }`,
          dataPost: {
            eventIds,
          },
        };

        const res2 = await eventPropertyService(params, value);

        const conditions = toRefinePropertiesUI(backup, data.map, res2.map);

        if (isMounted()) {
          setStateCommon({
            group: data,
            isLoading: false,
            conditions,
          });
        }
      } else if (isMounted()) {
        setStateCommon({
          group: data,
          isLoading: false,
          conditions: refineWithProperties,
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  // eslint-disable-next-line no-shadow
  const fetchDataAttributeAPI = itemTypeId => {
    const columns = [
      'status',
      'item_property_display',
      'type',
      'storage_type',
      'data_type',
      'process_status',
      'is_required',
      'last_updated_date',
      'last_updated_user',
      'compute_schedule_start',
      'compute_schedule_end',
      'description',
      'last_processed_date',
      'created_date',
      'created_user',
      'auto_suggestion',
      'item_type_name',
    ];
    const paramsBO = {
      data: {
        page: 1,
        limit: 1000,
        search: '',
        sort: 'created_date',
        sd: 'asc',
        columns,
        perf_columns: [],
        filters: {
          OR: [
            {
              AND: [],
            },
          ],
        },
        item_type_id: parseInt(itemTypeId),
      },
    };

    setState(draft => {
      draft.isLoading = true;
    });

    BOAttributeServices.BOAttribute.getList(paramsBO).then(response => {
      let data = {
        list: [],
        map: {},
      };
      if (response.code === 200) {
        // eslint-disable-next-line prefer-destructuring
        data = toEntryFE(response.data, 'BOAttribute');
      }
      // console.log('phong----------------', data);
      // const isInit = false;
      if (isInit) {
        const refine = safeParse(backup.OR[0], {});
        const eventIds = getLookupRefineWithProperties(refine);
        // tạm thời để  là -1 do chưa có phát sinh gì
        const dataSourceID = '-1';
        const params = {
          inputUrl: `${dataSourceID}/${eventValue.eventCategoryId}/${
            eventValue.eventActionId
          }`,
          dataPost: {
            eventIds,
          },
        };
        MetaDataServices.eventProperty.lookupByIds(params).then(res2 => {
          // console.log('res2', res2);
          const conditions = toRefinePropertiesUI(backup, data.map, res2.map);

          if (isMounted()) {
            setStateCommon({
              group: data,
              isLoading: false,
              conditions,
            });
          }
        });
      } else if (isMounted()) {
        setStateCommon({
          group: data,
          isLoading: false,
          conditions: refineWithProperties,
        });
      }
    });
  };

  useDeepCompareEffect(() => {
    if (eventValue !== null) {
      if (itemTypeId) {
        fetchDataAttributeAPI(itemTypeId);
      } else {
        fetchDataAPI('eventProperties', eventValue, sourcesSelected);
      }
    } else {
      setState(draft => {
        draft.isLoading = false;
      });
    }
  }, [eventValue, sourcesSelected]); // no more warning

  return { ...state };
};
