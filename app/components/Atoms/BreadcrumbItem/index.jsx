/* eslint-disable react/prop-types */
/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/intractive-supports-focuse */
/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import PropTypes from 'prop-types';
import React from 'react';
import { Link } from 'react-router-dom';
import { makeUrlPermisison } from '../../../utils/web/permission';
import { getLink } from '../../Organisms/LayoutHeader/MenuGrid/utils';
import StyledBreadcrumbItem, {
  A,
  StyledInputRename,
} from './StyledBreadcrumbItem';
import { STORY_SETUP_ACTION } from '../../../modules/Dashboard/MarketingHub/Journey/Create/utils.story.rules';
import InputAutoResize from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/InputAutoResize';
import { get } from 'lodash';
import EditableContent from '../../common/EditableContent';

function BreadcrumbItem({
  item,
  portalId,
  userId,
  maxWidth,
  children,
  nameEditConfig,
  isJourneyV2 = false,
}) {
  const linkTo = getLink(item, portalId, userId);

  const renderCaret = () =>
    item.isShowArrow ? (
      <span className="icon-xlab-arrow-down icon-arrow" />
    ) : null;

  if (typeof item.onClick === 'function') {
    const onConfirm = () => item.onClick(linkTo);
    return (
      <StyledBreadcrumbItem maxWidth={maxWidth}>
        <A onClick={onConfirm}>
          <UITippy content={children}>
            <span>
              {children}
              {renderCaret()}
            </span>
          </UITippy>
        </A>
      </StyledBreadcrumbItem>
    );
  }
  if (linkTo !== null) {
    return (
      <StyledBreadcrumbItem maxWidth={maxWidth}>
        <Link to={makeUrlPermisison(linkTo)}>
          <UITippy content={children}>
            <span>
              {children}
              {renderCaret()}
            </span>
          </UITippy>
        </Link>
      </StyledBreadcrumbItem>
    );
  }

  // Flow edit name general
  if (item.nameEditConfig) {
    const { name } = item.nameEditConfig;

    return (
      <StyledBreadcrumbItem maxWidth={maxWidth}>
        <WrapperDisable disabled={item.disabled}>
          <StyledInputRename
            hiddenHelperText
            {...item.nameEditConfig}
            value={name}
          />
        </WrapperDisable>
      </StyledBreadcrumbItem>
    );
  }

  if (isJourneyV2) {
    return (
      <EditableContent
        defaultValue={nameEditConfig.configure.main.name}
        onChange={nameEditConfig.onChange}
        isLoading={nameEditConfig.configure.isDoing}
        error={get(
          nameEditConfig.configure,
          'main.mainErrors.story_name[0]',
          '',
        )}
        required
      />
    );
  }

  // Flow edit name journey
  return nameEditConfig && nameEditConfig.configure ? (
    <StyledBreadcrumbItem>
      <WrapperDisable
        disabled={
          !nameEditConfig.roleActions ||
          typeof nameEditConfig.roleActions.has !== 'function' ||
          !nameEditConfig.roleActions.has(STORY_SETUP_ACTION.EDIT_STORY_NAME)
        }
      >
        <InputAutoResize
          value={
            typeof children === 'string' && nameEditConfig.design !== 'create'
              ? children
              : nameEditConfig.configure.main.name
          }
          onChange={nameEditConfig.onChange}
          className="form-field-name"
          serviceUpdateName={nameEditConfig.service}
          paramsUpdate={nameEditConfig.paramsUpdate}
          nameKey="story_name"
          design={nameEditConfig.design}
          errors={nameEditConfig.configure.main.mainErrors}
          isHidden={nameEditConfig.isHidden}
        />
      </WrapperDisable>
    </StyledBreadcrumbItem>
  ) : (
    <StyledBreadcrumbItem maxWidth={maxWidth}>
      <UITippy content={children}>
        <a>
          {children}
          {renderCaret()}
        </a>
      </UITippy>
    </StyledBreadcrumbItem>
  );
}

BreadcrumbItem.propTypes = {
  children: PropTypes.any,
  maxWidth: PropTypes.any,
  // href: PropTypes.string,
};

BreadcrumbItem.defaultProps = {
  maxWidth: 'unset',
};

export default BreadcrumbItem;
