import { isEmpty } from 'lodash';

export function safeParseValue(dataType, value = '') {
  if (dataType === 'datetime' && value === '') {
    // return new Date().getTime();
    return '';
  }
  if (dataType === 'boolean' && value === '') {
    return 'true';
  }
  return value;
}
export const displayFormatDefault = {
  type: 'NUMBER',
  config: {
    group: ',',
    decimal: '.',
    decimalPlace: 0,
  },
};

export const getOptionBySource = (source, sourceTags) => {
  const result = [];

  if (isEmpty(Object.keys(sourceTags)) || sourceTags[source] === undefined) {
    return [];
  }

  Object.values(sourceTags[source]).forEach(option => {
    result.push(option);
  });

  return result;
};
