/* eslint-disable consistent-return */
/* eslint-disable import/order */
/* eslint-disable no-else-return */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import CloseIcon from '@material-ui/icons/Close';
import {
  UIButton,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import ModalConfirmDeleteSource from './ModalConfirmDeleteSource';

import {
  WrapperPreviewSource,
  DivLoading,
  DataSourceBlockContent,
} from '../../styled';
import { useFetchDataByEvent } from './useFetchDataByEvent';

import ItemPreviewSourceSelected from '../../../../Molecules/ItemPreviewSource';

import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ConditionError from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import { convertConditionRefineToArrayString } from './utils';
import { validatePropertiesRefines } from '../../utils.validate';
import { FormHelperText } from '@material-ui/core';
import { STATUS_SOURCE_CODE } from '../../../../../utils/constants';
import { Button, PopoverSelect } from '@antscorp/antsomi-ui';
import { addMessageToQueue } from '../../../../../utils/web/queue';

const PATH =
  'app/components/common/UIPerformEvent/BlockConditionPerformEvent/DataSources/index.jsx';

const initState = {
  open: false,
  tempDataSources: [],
  sourceNames: [],
  arrayRefineWillDeleted: [],
  sourceIds: [],
  errors: [],
};

const labelAnySource = getTranslateMessage(
  TRANSLATE_KEY._TITL_IN_ANY_SOURCE,
  'In any source of',
);

const labelAddSource = getTranslateMessage(
  TRANSLATE_KEY._ACT_ADD_SOURCE,
  'Add source',
);

const SelectDataSources = props => {
  let isMounted = true;

  const {
    eventValue,
    sourcesSelected,
    isInitDataSources,
    refineWithProperties,
  } = props;
  let isDisable = true;

  if (eventValue !== null && typeof eventValue === 'object') {
    if (Object.keys(eventValue).length > 0) {
      isDisable = false;
    }
  }

  const [state, setState] = useImmer(initState);

  const { group, isLoading } = useFetchDataByEvent(
    isInitDataSources,
    eventValue,
    sourcesSelected,
    props.paramsFetchEvent,
  );

  useEffect(
    () => () => {
      isMounted = false;
    },
    [],
  );

  useEffect(() => {
    // console.log('-------', isLoading, group, isInitDataSources);
    if (!isLoading) {
      const arr = [];
      group.list.forEach(tmp => {
        arr.push(tmp.value);
      });
      if (isInitDataSources !== true) {
        props.onChange({
          dataSourceSelected: arr,
          temp: false,
          isDeleteRefine: false,
        });
      }
    }
  }, [group, isLoading]);

  useEffect(() => {
    if (group.list.length > 0) {
      let errors = [];
      sourcesSelected.forEach(each => {
        const item = group.map[each];
        if (
          item &&
          item.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT
        ) {
          errors = [
            'The source colored with red no more exists in any of model-source',
          ];
        }
      });
      setState(draft => {
        draft.errors = errors;
      });
    }
  }, [sourcesSelected, group]);

  const toggleModal = () => {
    setState(draft => {
      draft.open = false;
    });
  };

  const callback = (type, data) => {
    if (type === 'DELETE_SOURCE_ITEM') {
      const array = sourcesSelected;
      const { inValid, refineBelongtoSourceId } = validatePropertiesRefines(
        refineWithProperties,
        array[data],
      );

      if (
        refineWithProperties.size === 0 ||
        inValid ||
        !refineBelongtoSourceId
      ) {
        props.onChange({
          dataSourceSelected: [
            ...array.slice(0, data),
            ...array.slice(data + 1),
          ],
          isDeleteRefine: false,
        });
      } else {
        let sourceName = '--';
        const sourceId = array[data];
        const tempData = [...array.slice(0, data), ...array.slice(data + 1)];
        if (group.map[sourceId]) {
          sourceName = group.map[sourceId].label;
        }
        setState(draft => {
          draft.open = true;
          draft.sourceNames = [sourceName];
          draft.sourceIds = [sourceId];
          draft.arrayRefineWillDeleted = convertConditionRefineToArrayString(
            refineWithProperties,
            sourceId,
            tempData,
            array,
          );
          draft.tempDataSources = tempData;
        });
      }
    } else if (type === 'CONFIRM_DELETE_SOURCE') {
      if (data === true) {
        props.onChange({
          dataSourceSelected: state.tempDataSources,
          isDeleteRefine: true,
          sourceIdWillDeleted: state.sourceIds,
          array: sourcesSelected,
        });
      }
    }
  };

  const handleApplySelectSources = newSelectedKeyList => {
    try {
      if (!Array.isArray(sourcesSelected)) {
        throw new Error('sourcesSelected must be an array');
      }

      // No need to validate with refine properties
      if (refineWithProperties?.size === 0) {
        props.onChange({
          dataSourceSelected: newSelectedKeyList,
          isDeleteRefine: false,
        });

        return;
      }

      const removedSourceKeys = sourcesSelected.filter(
        sourceKey => !newSelectedKeyList.includes(sourceKey),
      );
      const isValidAllSources = removedSourceKeys.every(sourceId => {
        const { inValid, refineBelongtoSourceId } = validatePropertiesRefines(
          refineWithProperties,
          sourceId,
        );

        return inValid || !refineBelongtoSourceId;
      });

      // If not removed any source or all sources are valid -> just update selected source data
      if (removedSourceKeys.length === 0 || isValidAllSources) {
        props.onChange({
          dataSourceSelected: newSelectedKeyList,
          isDeleteRefine: false,
        });

        return;
      }

      const {
        arrayRefineWillDeleted,
        removedSourceNameList,
      } = removedSourceKeys.reduce(
        (acc, sourceId) => {
          // Update list removed source name
          if (group.map[sourceId]) {
            acc.removedSourceNameList.push(group.map[sourceId].label);
          } else {
            acc.removedSourceNameList.push('--');
          }

          // Update list refine will be deleted
          const refineList = convertConditionRefineToArrayString(
            refineWithProperties,
            sourceId,
            newSelectedKeyList,
            sourcesSelected,
          );
          acc.arrayRefineWillDeleted.push(...refineList);

          return acc;
        },
        { removedSourceNameList: [], arrayRefineWillDeleted: [] },
      );

      // Process with modal confirm delete source
      setState(draft => {
        draft.open = true;
        draft.sourceNames = removedSourceNameList;
        draft.sourceIds = removedSourceKeys;
        draft.arrayRefineWillDeleted = arrayRefineWillDeleted;
        draft.tempDataSources = newSelectedKeyList;
      });
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleApplySelectSources',
        data: {
          error: error.stack,
          args: { newSelectedKeyList, sourcesSelected },
        },
      });
    }
  };

  if (!isMounted) {
    return null;
  }

  const renderContentSelectDataSources = () => {
    if (!isDisable && isLoading && isMounted) {
      return (
        <DivLoading>
          <Loading isLoading={isLoading} size={20} />
        </DivLoading>
      );
    }

    return (
      <WrapperDisable disabled={isDisable}>
        {sourcesSelected.map((source, index) => {
          const tmp = group.map[source] || {};
          const isError =
            tmp.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT;
          if (tmp !== undefined) {
            return (
              <ItemPreviewSourceSelected
                index={index}
                rightIcon={
                  !props.isViewMode && (
                    <Button
                      shape="circle"
                      type="text"
                      style={{
                        width: 16,
                        height: 16,
                        minWidth: 16,
                        backgroundColor: '#ffffff',
                      }}
                      onClick={() => callback('DELETE_SOURCE_ITEM', index)}
                      icon={<CloseIcon />}
                    />
                  )
                }
                label={tmp.label}
                value={tmp.value}
                item={tmp}
                key={source}
                isError={isError}
                isViewMode={props.isViewMode}
                isBlastCampaign={props.isBlastCampaign}
              />
            );
          }

          return null;
        })}

        {!props.isViewMode && (
          <PopoverSelect
            onApply={handleApplySelectSources}
            options={group.list}
            selected={sourcesSelected}
          >
            <UIButton
              variant="contained"
              reverse
              iconName="add"
              theme="outline"
              borderRadius="1rem"
              fs="0.75rem"
              style={{
                border: 'solid 1px',
                height: 24,
              }}
            >
              {labelAddSource}
            </UIButton>
          </PopoverSelect>
        )}
      </WrapperDisable>
    );
  };

  return (
    <DataSourceBlockContent>
      <div className="title select-source">
        <div className="select-source-txt">{labelAnySource}</div>
      </div>
      <div className="content">
        <WrapperDisable
          disabled={
            props.isBlastCampaign && props.isViewMode
              ? false
              : props.disabledEventConditions
          }
        >
          <WrapperPreviewSource>
            {renderContentSelectDataSources()}
          </WrapperPreviewSource>
        </WrapperDisable>

        <ConditionError keyError={3} error={props.error} />
        <FormHelperText id="component-helper-text" error={!!state.errors[0]}>
          {state.errors}
        </FormHelperText>
      </div>
      <ModalConfirmDeleteSource
        isOpen={state.open}
        toggle={toggleModal}
        callback={callback}
        sourceName={state.sourceNames.join(', ')}
        conditionRefine={state.arrayRefineWillDeleted}
      />
    </DataSourceBlockContent>
  );
};

SelectDataSources.defaultProps = {
  sourcesSelected: [],
  disabledEventConditions: false,
};
export default SelectDataSources;
