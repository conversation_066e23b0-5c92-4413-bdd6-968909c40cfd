// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Utils
import { initDisplayFormat } from '../../common/Modal/ModalConfigFormat/utils';

export const initCustomFunction = {
  dataType: 'number',
  personalizationName: '',
  formular: '',
  saveAsTemplate: false,
  displayFormat: initDisplayFormat('number'),
  errors: '',
  templateId: null,
  templateCode: '',
  errorsFunction: '',
};

export const EXTRA_ATTR_TYPE = Object.freeze({
  CUSTOM_FN: 'customFunction',
  FORMAT: 'formatAttributes',
});

export const INCLUDE_TYPE = Object.freeze({
  COUNT_ALL_CODES: 'count_all_codes',
  COUNT_UNIQUE_CODE: 'count_unique_code',
  ALL_UNIQUE_CODES: 'all_unique_codes',
});

export const INCLUDE_TYPE_MAP = Object.freeze({
  [INCLUDE_TYPE.COUNT_ALL_CODES]: {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_COUNT,
      'Count all codes',
    ),
    value: INCLUDE_TYPE.COUNT_ALL_CODES,
    title: null,
  },
  [INCLUDE_TYPE.COUNT_UNIQUE_CODE]: {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_COUNT_DISTINCT,
      'Count unique code',
    ),
    value: INCLUDE_TYPE.COUNT_UNIQUE_CODE,
    title: null,
  },
  [INCLUDE_TYPE.ALL_UNIQUE_CODES]: {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_ALL,
      'All unique codes',
    ),
    value: INCLUDE_TYPE.ALL_UNIQUE_CODES,
    title: null,
  },
});

export const INCLUDE_TYPE_OPTS = Object.freeze(Object.values(INCLUDE_TYPE_MAP));

export const DELIMITER = Object.freeze({
  COMMA: 'comma',
  DOT: 'dot',
  SEMICOLON: 'semicolon',
  SPACE: 'space',
  DASH: 'dash',
});

export const DELIMITER_OPTS = Object.freeze([
  {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_DELIMETER_1,
      'Comma (,)',
    ),
    value: DELIMITER.COMMA,
  },
  {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_DELIMETER_2,
      'Dot (.)',
    ),
    value: DELIMITER.DOT,
  },
  {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_DELIMETER_3,
      'Semicolon (;)',
    ),
    value: DELIMITER.SEMICOLON,
  },
  {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_DELIMETER_4,
      'Space',
    ),
    value: DELIMITER.SPACE,
  },
  {
    label: translate(
      translations._PERSONALIZE_PROCESS_CODE_DELIMETER_5,
      'Dash (-)',
    ),
    value: DELIMITER.DASH,
  },
]);
