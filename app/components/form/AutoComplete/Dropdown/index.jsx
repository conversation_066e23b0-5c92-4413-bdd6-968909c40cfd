/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { toString } from 'lodash';

import { Popover } from '@antscorp/antsomi-ui';
import { useImmer } from 'use-immer';
import { OrderedSet } from 'immutable';
import classnames from 'classnames';
import { UITextSearch as TextSearch } from '@xlab-team/ui-components';
import WrapperDisableContent from '../../../common/WrapperDisableContent';
import Tree from './Tree';
import { safeParse, generateKey } from '../../../../utils/common';
import { mappingValueToOptions } from '../utils';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import ObjectServices from '../../../../services/Object';

import {
  WrapperAutoSuggestionContent,
  WrapperAutoSuggestionContentInline,
  TextSearchWrapper,
} from './styled';
import { isDecryptFields } from '../../../../utils/web/attribute';
import { HelperText } from '../../AutoSuggestion/Dropdown/styles';
import { safeParseV2 } from '../../../../utils/web/utils';

const MAP_TRANSLATE = {
  actSeachValue: getTranslateMessage(
    TRANSLATE_KEY._ACT_SEARCH_VALUE,
    'Search values',
  ),
};
const LIMIT = 20;

function PopoverDropdown(props) {
  // console.log('AutoComplete: ', props);
  const {
    property,
    isEncrypt,
    helperText,
    config,
    operatorValue,
    initValue,
    use,
    error,
    isMulti,
    isBackUp,
    fullWidthPopover = true,
    value: propValue,
  } = props;

  const { propertyCode } = config;

  const [componentKey] = useState(`autocomplete-id-${generateKey()}`);
  const [fristLoad, setFristLoad] = useState(false);
  const [value, setValue] = useState(OrderedSet([]));
  const [widthPopover, setWidthPopover] = useState('inherit');
  const [optionCurrentChecked] = useState(OrderedSet([]));
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [mapOptions, setMapOptions] = useState({});
  const [options, setOptions] = useState([]);

  const [indexCurrentValue, setIndexCurrentValue] = useState(0);

  const isEncyptable =
    isEncrypt === 1 &&
    !isDecryptFields({
      itemTypeId: property.itemTypeId,
      attributeCode: propertyCode,
    });

  const [state, setState] = useImmer({
    page: 1,
    limit: LIMIT,
    search: '',
    hasLoadMore: true,
    showCheckedOnly: false,
    isSelectAll: false,
  });

  const fetchData = (isLoadMore, page, limit, search, optional) => {
    if (!isBackUp) {
      const { itemTypeId, itemTypeName } = property;
      const params = {
        data: {
          limit,
          page,
          sd: 'asc',
          search: toString(search).trim(),
          decryptFields: isEncyptable ? [] : [`${propertyCode}`],
          itemTypeId,
          propertyCode,
          itemTypeName,
        },
      };
      params.data = { ...params.data, ...props.config };

      // lỗi props.config overwrite params.data
      params.data.search = toString(search).trim();
      setLoading(true);
      const feServices = safeParse(config.feServices, 'suggestion'); // suggestionMultilang
      ObjectServices[feServices].getList(params).then(res => {
        // console.log(res);

        if (res !== null) {
          setLoading(false);
          setState(draft => {
            draft.hasLoadMore = res.list.length > 0;
          });
          setState(draft => {
            draft.page = res.list.length > 0 ? page : state.page;
          });
          if (!fristLoad) {
            setFristLoad(true);
          }
          if (optional !== undefined) {
            setOptions(
              mappingValueToOptions(
                isLoadMore,
                optional.value,
                optional.mapOptions,
                optional.options,
                res,
              ),
            );
            setMapOptions({ ...optional.mapOptions, ...res.map });
          } else {
            setOptions(
              mappingValueToOptions(
                isLoadMore,
                value,
                mapOptions,
                options,
                res,
              ),
            );
            setMapOptions({ ...mapOptions, ...res.map });
          }
        }
      });
    }
  };

  const updateValue = newValue => {
    setValue(OrderedSet(newValue));
    props.onChange(newValue);
  };

  const onChange = item => {
    setState(draft => {
      draft.isSelectAll = false;
    });
    let arrValues = value;
    arrValues = OrderedSet([item.value]); // maybe wrong
    setOpen(false);
    // popupState.close();
    updateValue(arrValues.toArray());
  };

  const onSearch = valueSearch => {
    // const valueSearch = value;
    // for single
    if (!isMulti) {
      const arrValues = [];
      arrValues.push(valueSearch);
      updateValue(arrValues);
    }

    setState(draft => {
      draft.search = valueSearch;
      draft.page = 1;
      draft.limit = LIMIT;
    });

    fetchData(false, 1, LIMIT, valueSearch);
  };

  const onLoadMore = () => {
    if (!state.showCheckedOnly) {
      const nextPage = state.page + 1;
      if (state.hasLoadMore) {
        // setState(draft => {
        //   draft.page = nextPage;
        // });
        fetchData(true, nextPage, LIMIT, state.search);
      }
    }
  };

  const handleInitValue = temptValue => {
    const imutableValue = OrderedSet(temptValue);
    setValue(imutableValue);

    const search = safeParseV2(temptValue[0], '');
    setState(draft => {
      draft.search = search;
    });
  };

  const handleInitState = () => {
    setMapOptions({});
    setOptions([]);
    setLoading(false);
    setFristLoad(false);
    setState(draft => {
      draft.page = 1;
      draft.limit = LIMIT;
      draft.search = '';
      draft.hasLoadMore = true;
      draft.showCheckedOnly = false;
      draft.isSelectAll = false;
    });
  };

  useEffect(() => {
    handleInitState();
  }, [config.feKey, operatorValue, isEncyptable]);

  useEffect(() => {
    handleInitValue(safeParse(initValue, []));

    if (!fristLoad && use === 'inline') {
      setFristLoad(true);
      fetchData(false, 1, LIMIT, '');
      setOpen(true);
    }
  }, [initValue, config.feKey, operatorValue, isEncyptable]);

  useEffect(() => {
    if (!isMulti && open && value.size > 0) {
      let indexItem = -1;

      options.forEach((item, index) => {
        if (value.has(item.value)) {
          indexItem = index;
        }
      });

      if (indexItem === -1) {
        setIndexCurrentValue(0);
      } else {
        setIndexCurrentValue(indexItem);
      }
    }
    // reset indexCurrentValue every time willUnMount
    return () => {
      if (!isMulti && !open) {
        setIndexCurrentValue(0);
      }
    };
  }, [open, options]);

  useEffect(() => {
    if (open && fullWidthPopover) {
      const button = document.getElementById(componentKey);
      const { width } = button.getBoundingClientRect();
      setWidthPopover(safeParse(`${width}px`, 'inherit'));
    }
  }, [open]);

  const toggle = visible => {
    if (!open && !fristLoad) {
      fetchData(false, state.page, state.limit, state.search);
    }

    // console.log('isOpenModalDecrypt: ', isOpenModalDecrypt, open, fristLoad);
    setOpen(visible);
  };

  const callbackHandler = (type, data) => {
    switch (type) {
      case 'DECRYPT_DATA': {
        setOpen(false);
        props.callback(type, data);
        break;
      }
      default:
        break;
    }
  };

  const renderTree = () => {
    return (
      <div className="private-dropdown uiDropdown__dropdown private-dropdown--list p-y-0">
        <WrapperDisableContent
          disabled={isEncrypt === 2 || isEncyptable}
          isEncrypt={isEncrypt}
          callback={callbackHandler}
          className="width-100"
        >
          <Tree
            isMulti={false}
            isSearchable={false}
            isLoading={isLoading}
            options={options}
            mapOptions={mapOptions}
            optionCurrentChecked={optionCurrentChecked}
            value={value}
            onChange={onChange}
            onSearch={onSearch}
            onLoadMore={onLoadMore}
            showCheckedOnly={state.showCheckedOnly}
            indexCurrentValue={indexCurrentValue}
            isOpen={open}
          />
        </WrapperDisableContent>
      </div>
    );
  };

  if (use === 'inline') {
    return (
      <>
        <TextSearchWrapper className="wrapper-search-filter">
          <TextSearch
            classDiv="search-filter"
            classInput="p-x-0 text-search-input"
            placeholder={MAP_TRANSLATE.actSeachValue}
            onChange={onSearch}
            value={safeParseV2(props.value[0], '')}
            // focus
            id={componentKey}
            type="text"
          />
        </TextSearchWrapper>

        <WrapperAutoSuggestionContentInline>
          {renderTree()}
        </WrapperAutoSuggestionContentInline>
      </>
    );
  }

  return (
    <React.Fragment>
      <>
        <Dropdown
          direction="down"
          className="width-100"
          isOpen={open}
          toggle={toggle}
          flip="false"
          modifiers={{ preventOverflow: { boundariesElement: 'window' } }}
        >
          <DropdownToggle caret tag="div" className="width-100">
            <TextSearchWrapper
              className={classnames({
                'input-error': props.error,
              })}
            >
              <TextSearch
                classDiv="search-filter"
                classInput="p-x-0 text-search-input"
                placeholder={MAP_TRANSLATE.actSeachValue}
                onChange={onSearch}
                value={safeParseV2(props.value[0], '')}
                // focus
                id={componentKey}
                // noIcon
                onBlur={onBlur}
                onFocus={onFocus}
                type="text"
              />

              {props.helperText !== '' && (
                <HelperText className="helper-text">
                  {props.helperText}
                </HelperText>
              )}
            </TextSearchWrapper>
          </DropdownToggle>

          <DropdownMenu
            // className="width-100 cdp-tree-min-width"
            // modifiers={{ preventOverflow: { boundariesElement: 'window' } }}
            caret="true"
            persist={false}
            container="body"
            style={{ minWidth: '280px' }}
            // positionFixed={true}
          >
            <WrapperAutoSuggestionContent
              className="private-dropdown uiDropdown__dropdown private-dropdown--list p-y-0"
              width={widthPopover}
            >
              {renderTree()}
            </WrapperAutoSuggestionContent>
          </DropdownMenu>
        </Dropdown>
      </>
    </React.Fragment>
  );
}

export default PopoverDropdown;
