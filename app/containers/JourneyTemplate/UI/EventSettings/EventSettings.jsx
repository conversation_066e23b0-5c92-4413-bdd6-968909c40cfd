import React, { useMemo } from 'react';
import { JOURNEY_OBJECT_TYPE } from '../../constant';
import { get } from 'lodash';
import EventPreview from '../../../UIPreview/EventPreview';
import { useActiveObj } from '../../hooks/useActiveObj';
import { useGetObjects } from '../../hooks/useGetObjects';
import { useSelector } from 'react-redux';
import { selectMode, selectVerification } from '../../selector';

export const EventSettings = () => {
  const mode = useSelector(selectMode);
  const verification = useSelector(selectVerification);

  const { info, saveInfo } = useActiveObj();
  const { allObjects } = useGetObjects();
  const { activeObj } = verification;
  const { eventActionId, eventCategoryId } = activeObj;

  const eventInfo = useMemo(() => {
    const result = {
      eventActionId,
      eventCategoryId,
    };
    if (mode === 'use' && info.isExist) {
      result.eventActionId = info.settings.eventActionId;
      result.eventCategoryId = info.settings.eventCategoryId;
    }
    return result;
  }, [info]);

  const isEventObject = get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.event;
  // eventActionId, eventCategoryId, settings
  if (!isEventObject) return null;

  return (
    <EventPreview
      eventActionId={eventInfo.eventActionId}
      eventCategoryId={eventInfo.eventCategoryId}
      settings={info.settings || saveInfo.settings}
      mode={mode}
      isExistEvent={info.isExist}
      allObjects={allObjects}
    />
  );
};
