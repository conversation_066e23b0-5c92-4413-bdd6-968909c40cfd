/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect, useMemo } from 'react';
import _ from 'lodash';
import { useDispatch, useSelector } from 'react-redux';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from '../../../../../hooks';

// Selectors
import { makeSelectPersonalizations } from '../../../../../modules/Dashboard/selector';

// Constants
import {
  DEFAULT_DATA_TYPES,
  DEFAULT_GROUP_CODES,
  JOURNEY_CUSTOM_FN_CODE,
  setInput,
  SHORT_LINK_V2,
} from '../constants';

// Utils
import { safeParse } from '../../../../../utils/common';
import {
  findMissingGroupCodes,
  getGroupAttrs,
  getGroupCodes,
  getPayloadsPersonalizeToWorkerSaga,
} from '../utils';
import {
  initPersonalizations,
  removeRelatedContentSource,
  startFetchingContentSource,
  startPersonalizations,
} from '../libs/action';

let hasInitialized = false;

function withPersonalizations(WrappedComponent) {
  return props => {
    // Props
    const {
      groupCodes = DEFAULT_GROUP_CODES,
      isBlastCampaign = false,
      itemTypeId = '',
      eventValue = {},
      typeComponent = 'input',
      dataTypes = DEFAULT_DATA_TYPES,
      isAppendBOContentSource = false,
      appendPersonalizeType = [],
      showPersonalization = true,
      onlyAddIcon = false,
      journeyNodeTagProperties,
      enableShortLink,
    } = props;

    // Hooks
    const dispatch = useDispatch();

    // Selectors
    const { isInitDone = false, settings = {} } = useSelector(
      makeSelectPersonalizations(),
    );

    // Memoizations
    const {
      personalizationData = {},
      personalizationType = {},
      personalizationDataError = {},
      contentSources = [],
    } = useMemo(() => settings || {}, [settings]);

    const mapAttributes = useDeepCompareMemo(() => {
      const result = {};
      Object.entries(personalizationData || {}).forEach(
        ([key, dataAttributes]) => {
          const { map = {} } = dataAttributes || {};

          result[key] = map;
        },
      );
      return result;
    }, [personalizationData]);

    const eventCategoryId = useMemo(
      () => safeParse(eventValue.eventCategoryId, null),
      [eventValue?.eventCategoryId],
    );

    const eventActionId = useMemo(
      () => safeParse(eventValue.eventActionId, null),
      [eventValue?.eventActionId],
    );

    const groupCodesMemoized = useMemo(() => {
      const arr = window.location.pathname.split('/');
      const codes = _.cloneDeep(groupCodes);

      // add more custom function for journey into groupCodes
      if (arr.includes('journeys')) {
        codes.push(JOURNEY_CUSTOM_FN_CODE);
      }

      if (enableShortLink) {
        codes.push(SHORT_LINK_V2);
      }

      return codes;
    }, [groupCodes, enableShortLink]);

    const groupsAttrs = useMemo(() => {
      const newGroupCodes = getGroupCodes({
        groupCodes: groupCodesMemoized,
        isBlastCampaign,
        itemTypeId,
      });

      return getGroupAttrs(newGroupCodes);
    }, [groupCodesMemoized, isBlastCampaign, itemTypeId]);

    // Initialize personalizations
    useEffect(() => {
      if (!hasInitialized) {
        hasInitialized = true;

        // Prepare data for personalization to worker init
        const payloads = getPayloadsPersonalizeToWorkerSaga({
          groupCodes: groupCodesMemoized,
          groupsAttrs,
          dataTypes,
          eventCategoryId,
          eventActionId,
          typeComponent,
          setInput,
        });

        if (!_.isEmpty(payloads)) {
          dispatch(initPersonalizations());
          dispatch(startPersonalizations(payloads));
        }
      }
    }, [
      dispatch,
      groupCodesMemoized,
      groupsAttrs,
      dataTypes,
      eventActionId,
      eventCategoryId,
      typeComponent,
    ]);

    // append more missing personalization by group codes
    useDeepCompareEffect(() => {
      if (hasInitialized && isInitDone && groupCodesMemoized?.length > 0) {
        const foundMissingGroupCodes = findMissingGroupCodes({
          groupCodes: groupCodesMemoized,
          personalizationType,
          personalizationData,
        });

        if (foundMissingGroupCodes?.length > 0) {
          const payloads = getPayloadsPersonalizeToWorkerSaga({
            groupCodes: foundMissingGroupCodes,
            groupsAttrs,
            dataTypes,
            eventCategoryId,
            eventActionId,
            typeComponent,
            setInput,
          });

          if (!_.isEmpty(payloads)) {
            dispatch(startPersonalizations(payloads));
          }
        }
      }
    }, [
      groupCodesMemoized,
      groupsAttrs,
      isInitDone,
      dataTypes,
      eventActionId,
      eventCategoryId,
      typeComponent,
      personalizationType,
      personalizationData,
    ]);

    // Add more Personalization attribute type in case has BO content source
    // eslint-disable-next-line consistent-return
    useEffect(() => {
      const isValidCS =
        isAppendBOContentSource &&
        _.isArray(appendPersonalizeType) &&
        appendPersonalizeType.length > 0;

      const isChangedCS = !_.isEqual(contentSources, appendPersonalizeType);
      const isValidPS = showPersonalization && !onlyAddIcon;
      const isAllowFetchCS = isValidCS && isChangedCS && isValidPS;

      if (isAllowFetchCS) {
        const dispatchFetchContentSource = () => {
          const matchItemTypeId = new Set();
          const boContentSources = [];
          const personalizeMap = appendPersonalizeType.reduce((accur, item) => {
            accur[item.groupId] = {
              label: item.label,
              value: item.groupId,
              color: item.color,
            };

            matchItemTypeId.add(Number(item.itemTypeId));
            boContentSources.push(item);

            return accur;
          }, {});

          const payload = {
            contentSources: boContentSources,
            appendPersonalizeType,
            personalizeMap,
            params: {
              data: {
                filters: {
                  OR: [
                    {
                      AND: [
                        {
                          column: 'item_type_id',
                          data_type: 'number',
                          operator: 'matches',
                          value: [...matchItemTypeId],
                        },
                        {
                          type: 1,
                          column: 'status',
                          data_type: 'number',
                          operator: 'matches',
                          value: [1, 2],
                        },
                      ],
                    },
                  ],
                },
                limit: null,
                page: null,
              },
            },
          };

          dispatch(startFetchingContentSource(payload));
        };

        const startDispatchContentSourceDebounce = _.debounce(
          dispatchFetchContentSource,
          450,
        );

        startDispatchContentSourceDebounce();

        return () => {
          startDispatchContentSourceDebounce.cancel();
        };
      }
    }, [
      dispatch,
      onlyAddIcon,
      contentSources,
      showPersonalization,
      appendPersonalizeType,
      isAppendBOContentSource,
    ]);

    // Remove content source if not append
    useDeepCompareEffect(() => {
      if (appendPersonalizeType.length === 0) {
        dispatch(removeRelatedContentSource());
      }
    }, [dispatch, appendPersonalizeType?.length]);

    return (
      <WrappedComponent
        {...props}
        mapAttributes={mapAttributes}
        tagProperties={journeyNodeTagProperties}
        mapErrorAttributes={personalizationDataError}
      />
    );
  };
}

export default withPersonalizations;
